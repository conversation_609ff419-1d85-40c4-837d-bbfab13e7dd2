# IT服务账单明细Excel样式规范文档

## 概述
本文档描述了IT服务账单明细Excel导出的详细样式规范，基于销售账单明细的实际显示效果。

## 整体布局

### 表头结构（2行）
1. **第1行** - 主标题行（公司名称和报告标题）
2. **第2行** - 金额汇总行（账单汇总信息）

### 数据行
- **高度**: 标准行高
- **对齐**: 水平居中，垂直居中

## 详细样式规范

### 第1行 - 主标题行
- **内容**: 公司名称和报告标题
  - 格式: `{公司名称}\n{年份}年{月份}月-销售账单明细`
  - 示例: `深圳未来奇迹人工智能科技有限公司\n2025年06月-销售账单明细`
- **合并范围**: 整个表头宽度
- **字体**: 微软雅黑，粗体，黑色
- **对齐**: 水平居中，垂直居中，自动换行
- **背景**: 白色
- **边框**: 黑色细线边框
- **行高**: 自适应

### 第2行 - 金额汇总行
- **内容**: 账单汇总信息
  - 格式: `{月份}月账单应付总金额: {金额}元, 已付: {金额}元, 未付金额: {金额}元`
  - 示例: `06月账单应付总金额: 225.00元, 已付: 225.00元, 未付金额: 0.00元`
- **合并范围**: 整个表头宽度
- **字体**: 微软雅黑，粗体，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 黑色细线边框
- **行高**: 自适应


### 第3行 - 计算逻辑描述行
- **内容**: 金额计算逻辑的文字描述
  - 格式: `服务金额 已付金额 未付金额`
  - 各描述间用空字符串分隔
- **字体**: 微软雅黑，11pt，粗体，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 无边框
- **行高**: 30px

### 第4行 - 计算逻辑数值行
- **内容**: 对应第3行的具体数值和运算符
  - 格式: `100 - 30 = 70`
- **字体**: 微软雅黑，11pt，粗体，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 无边框
- **行高**: 30px

### 第5行 - 字段分组行
- **分组结构**:
  - A5:I5 - 账单信息
  - J5:P5 - 应付信息
  - Q5:V5 - 客户信息
- **字体**: 微软雅黑，11pt，粗体，灰色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 黑色细线边框
- **行高**: 30px

## 字段映射关系

### 账单信息字段（主要字段）
| Excel列 | 字段名 | 数据类型 | 说明 | 特殊处理 |
|---------|--------|----------|------|----------|
| 单号 | businessOrderNo | String | 业务订单号 | 显示格式：YYYYMMDD-XX |
| 日期 | rentStartTime | Date | 租赁开始时间 | 格式：yyyy/MM/dd |
| 名称 | productName | String | 商品名称 | 可能包含特殊字符 |
| 类别 | categoryName | String | 商品类别名称 | 可能为空 |
| 单价(元) | unitAmount | BigDecimal | 单价金额 | 显示格式：xxx.xx/天 |
| 数量 | count | Integer | 商品数量 | 数值格式 |
| 应付金额 | billAmount | BigDecimal | 应付金额 | 数值格式，用于求和 |
| 支付状态 | payStatus | Integer | 支付状态 | 映射为中文描述 |
| 是否续租 | isRelet | Integer | 是否续租标识 | 显示为"是"或"否" |
| 账单标识 | billPeriodFlag | String | 账单期标识 | 显示为"新增设备"等 |

### 应付信息字段
| Excel列 | 字段名 | 数据类型 | 说明 | 特殊处理 |
|---------|--------|----------|------|----------|
| 出账金额 | billStatementAmount | BigDecimal | 出账金额 | 数值格式 |
| 退租金额 | billReturnStatementAmount | BigDecimal | 退租金额 | 数值格式 |
| 调整金额 | correctAmount | BigDecimal | 调整金额 | 数值格式，负数显示 |
| 优惠金额 | couponAmount | BigDecimal | 优惠金额 | 数值格式，负数显示 |
| 已付金额 | paidAmount | BigDecimal | 已付金额 | 数值格式 |
| 已退押金 | returnAmount | BigDecimal | 已退押金金额 | 数值格式 |
| 说明 | payInfo | String | 支付说明信息 | 可能包含活动信息 |

### 客户信息字段
| Excel列 | 字段名 | 数据类型 | 说明 | 特殊处理 |
|---------|--------|----------|------|----------|
| 收件人 | consigneeName | String | 收件人姓名 | 直接显示 |
| 省 | provinceName | String | 省份名称 | 直接显示 |
| 市 | cityName | String | 城市名称 | 直接显示 |
| 区 | districtName | String | 区县名称 | 直接显示 |
| 详细地址 | address | String | 详细地址 | 可能显示"外勤" |
| 子公司 | customerSubName | String | 分子公司 | 直接显示 |

## 数据格式化规则

### 数值格式化
- **金额字段**: 最多保留2位小数，整数不显示小数部分
  - 示例: `225.00` 或 `225`
- **日期字段**: `yyyy/MM/dd` 格式
  - 示例: `2025/06/28`
- **单价字段**: 显示为 `xxx.xx/天` 格式
  - 示例: `107.00/天`

### 特殊字段处理
- **支付状态**: 映射为中文描述
  - 示例: `已支付`
- **是否续租**: 显示为"是"或"否"
  - 示例: `否`
- **账单标识**: 显示为中文描述
  - 示例: `新增设备`
- **说明字段**: 可能包含活动信息
  - 示例: `活动(电源租...)` 或 `1000),销售金(...)`

## 列宽调整规则

### 自动调整逻辑
1. **基础计算**: 根据内容长度 + 2个字符间隔
2. **标题考虑**: 如果标题比内容更长，使用标题长度
3. **最小边距**: 确保至少2个字符的边距
4. **最大宽度限制**: 30个字符

### 特殊列宽度限制
以下列需要特殊处理：
- 单号（A列）：固定宽度
- 名称（C列）：适当加宽，可能包含长文本
- 说明（N列）：适当加宽，包含活动信息
- 详细地址（R列）：适当加宽

## 金额列数值处理
为确保Excel中的求和功能正常工作，以下金额列直接存储为数值格式：
- 应付金额
- 出账金额
- 退租金额
- 调整金额
- 优惠金额
- 已付金额
- 已退押金

## 边框规范
- **边框样式**: 黑色细线
- **边框范围**: 所有数据单元格
- **例外**: 标题行和汇总行无边框

## 字体规范
- **主字体**: 微软雅黑
- **字体大小**:
  - 主标题: 13pt
  - 汇总行: 12pt
  - 字段标题: 10.5pt
  - 数据行: 9pt

## 颜色规范
- **主标题**: 黑色字体，白色背景
- **汇总行**: 黑色字体，白色背景
- **字段标题**: 白色字体，蓝色背景（#0066CC）
- **数据行**: 黑色字体，白色背景

## 对齐规范
- **主标题**: 水平居中，垂直居中，自动换行
- **汇总行**: 水平左对齐，垂直居中
- **字段标题**: 水平居中，垂直居中
- **数据行**: 水平居中，垂直居中

## 特殊处理逻辑

### 数据合并处理
- **相同订单**: 相同单号的行可能需要合并显示
- **支付信息**: 相同支付信息的行可能需要合并

### 条件显示
- **退租金额**: 仅在有退租时显示
- **调整金额**: 仅在有调整时显示
- **优惠金额**: 仅在有优惠时显示

### 样式处理
- **支付状态**: 根据状态值设置不同样式
- **金额字段**: 负数显示为红色
- **重要信息**: 关键金额用粗体显示

## 性能优化
- 批量设置样式
- 高效的单元格合并
- 优化的列宽计算算法
- 减少不必要的单元格访问 