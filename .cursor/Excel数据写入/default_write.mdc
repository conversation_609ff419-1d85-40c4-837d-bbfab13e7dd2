---
description:
globs:
alwaysApply: false
---
# 账单数据Excel输出规则

## 输出顺序和结构

### 1. 顶部汇总栏
- 账单月份 + 应付总金额 + 已付金额 + 未付金额
- 格式：`{月份}账单应付总金额: {金额}元, 已付: {金额}元, 未付金额: {金额}元`

### 2. 账单扩展信息
包含以下字段：
- 新输设备金额 (newEquipmentAmount)
- 生期设备金实 (oldEquipmentAmount) 
- 退回设备金额 (returnEquipmentAmount)
- 其他费用 (otherAmount)
- 押金 (depositEquipmentAmount)
- 已付金额 (paidAmount)
- 未付金额 (unPaidAmount)

### 3. 主要账单明细表格（蓝色表头部分）
表头字段顺序：
1. 单号 (businessOrderNo)
2. 起租时间 (rentStartTime)
3. 商品名 (productName)
4. 类别 (categoryName)
5. 配置/详情 (productSkuName)
6. 单价(元) (unitAmount)
7. 数量 (count)
8. 本期开始日 (periodStartTime)
9. 本期结束日 (periodEndTime)
10. 期数 (phase/totalPhase)
11. 设备序列号 (serialNumberSet)
12. 应付金额 (billAmount)
13. 支付状态 (payStatus)
14. 账单标识

### 4. 右侧信息面板（灰色表头部分）

#### 4.1 应付信息
字段顺序：
- 出账金额 (billStatementAmount)
- 退租金额 (billReturnStatementAmount)
- 调整金额 (correctAmount)
- 优惠金额 (couponAmount)
- 已付金额 (paidAmount)
- 已退押金 (returnAmount)
- 说明 (payInfo)

#### 4.2 客户信息
字段顺序：
- 收件人 (consigneeName)
- 省 (provinceName)
- 市 (cityName)
- 区 (districtName)
- 详细地址 (address)
- 分子公司 (orderSubCompanyId)

#### 4.3 订单信息
字段顺序：
- 订单状态 (orderStatus)
- 关联信息 (associationCreateType)
- 原订单 (originOrderNo)
- 退租日期 (returnTime)

#### 4.4 商品信息
字段顺序：
- 成色 (isNew)
- 租赁方式 (rentModeDesc/rentMode)
- 备注 (rentSceneMark)

#### 4.5 使用信息
字段顺序：
- 使用人 (customerUser)
- 使用组织 (customerOrganization)
- 使用描述 (customerRemark)

## 数据来源优先级
- 优先使用 `excelBillPeriodDetailList`

## 显示条数限定
- 没有限定


## 格式化规则
- 金额字段：最多保留2位小数，如果为整数，则只显示整数
- 日期字段：格式为 YYYY/MM/DD
- 支付状态：未支付、部分支付、已支付、无需支付、无需支付）


## 样式特点
- 自动调整列宽，但是需要露出完整的上一级标题

## 相关文件
- [test_bill_data_hierarchy.py](mdc:excel_data/test_bill_data_hierarchy.py) - 当前打印逻辑实现
- [field_mapping.py](mdc:excel_data/field_mapping.py) - 字段映射定义
- [example_usage.py](mdc:excel_data/example_usage.py) - 数据获取逻辑


