# Excel账单导出样式描述

## 整体布局结构

### 行结构定义
- **表头区域**：固定5行表头
  - **第1行**：主标题行（完整跨列合并）
  - **第2行**：金额汇总行（整行合并）
  - **第3行**：计算逻辑行（按模块分区域合并）
  - **第4行**：字段分组行（多级分组合并）
  - **第5行**：具体字段行（无跨列合并）
- **数据区域**：从第6行开始，行数根据实际数据量动态确定

## 详细样式规范

### 1. 第1行 - 主标题行
**单元格范围**：A1:AL1（完整跨列合并，38列）
**内容**：客户名称 + 账单标题（如：极限人工智能有限公司 2025年07月-长租账单明细）
**样式要求**：
- 合并状态：完整跨列合并
- 对齐方式：水平居中 + 垂直居中
- 字体：微软雅黑｜16pt｜加粗｜黑色
- 背景：纯白无填充

### 2. 第2行 - 金额汇总行
**单元格范围**：A2:AL2（整行合并，38列）
**内容示例**：07月账单应付总金额: 424,309.00元, 已付: 383,427.00元, 未付金额: 40,882.00元
**样式要求**：
- 合并状态：整行单单元格合并
- 对齐方式：水平居中 + 垂直居中
- 字体：微软雅黑｜12pt｜加粗｜深红色(#8B0000)
- 背景：纯白无填充（透明）

### 3. 第3行 - 计算逻辑行
**单元格范围**：按模块分区域合并
**内容结构**：
- 新增设备金额 + 数值 + + + 往期设备金额 + 数值 + + + 退回设备金额 + 数值 + + + 其他费用 + 数值 + + + 押金 + 数值 + - + 已付金额 + 数值 + = + 未付金额 + 数值
**样式要求**：
- 合并状态：分13个合并块（文字+数值组合）
- 对齐方式：水平居中 + 垂直居中
- 字体：微软雅黑｜11pt｜常规｜黑色
- 背景：浅灰色填充（RGB: 240,240,240）
- 边框：黑色细线

### 4. 第4行 - 字段分组行
**单元格范围**：多级分组合并
**内容结构**：
- A4:O4 → 空白（账单信息区域，不显示标题）
- P4:V4 → "应付信息"（合并）
- W4:AB4 → "客户信息"（合并）
- AC4:AF4 → "订单信息"（合并）
- AG4:AI4 → "商品信息"（合并）
- AJ4:AL4 → "使用信息"（合并）
**样式要求**：
- 合并状态：多列横向合并（每组覆盖子字段）
- 对齐方式：水平居中 + 垂直居中
- 字体：微软雅黑｜11pt｜加粗｜灰色(#808080)
- 背景：白色填充
- 边框：黑色细线分隔

### 5. 第5行 - 具体字段行
**单元格范围**：无跨列合并
**列标题结构**（共38列）：
- **账单信息**（1-15列）：单号、起租时间、类别、商品名、配置/详情、单价（元）、数量、本期开始日、本期结束日、期数、设备序列号、应付金额、支付状态、是否续租、账单标识
- **应付信息**（16-22列）：出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金、说明
- **客户信息**（23-28列）：收件人、省、市、区、详细地址、分子公司
- **订单信息**（29-32列）：订单状态、关联信息、原订单、退租日期
- **商品信息**（33-35列）：成色、租赁方式、备注
- **使用信息**（36-38列）：使用人、使用组织、使用备注

**样式要求**：
- **账单信息字段**（前15列）：
  - 字体：微软雅黑｜10.5pt｜加粗｜白色
  - 背景：蓝色填充（RGB: 0,102,204）
- **其他字段**（后23列）：
  - 字体：微软雅黑｜10.5pt｜常规｜灰色(#808080)
  - 背景：灰色填充（RGB: 192,192,192）
- 合并状态：无合并（每列独立）
- 对齐方式：居中对齐
- 边框：黑色细线分隔列

## 数据格式规范

### 1. 金额数值格式
- 最多保持两位小数，如果是整数，则只显示整数
- 右对齐
- 示例：424,309.00 或 424,309

### 2. 日期格式
- 格式：yyyy/MM/dd
- 示例：2024/07/15

### 3. 状态字段格式
- 支付状态：直接显示描述文本（未支付、部分支付、已支付、无需结算、已冲正）
- 订单状态：直接显示描述文本
- 是否续租：显示为"是"或"否"

### 4. 符号标识
- 计算行中的 "+" "=" 符号保留独立单元格
- 负向金额显示为红色文字

## 样式系统规范

### 颜色方案
| 元素类型 | 颜色方案 | RGB值 |
|---------|---------|-------|
| 负向金额 | 红色文字 | #FF0000 |
| 主标题字体 | 黑色 | #000000 |
| 汇总行字体 | 深红色 | #8B0000 |
| 计算行字体 | 黑色 | #000000 |
| 分组行字体 | 灰色 | #808080 |
| 账单信息字段字体 | 白色 | #FFFFFF |
| 其他字段字体 | 灰色 | #808080 |
| 账单信息字段背景 | 蓝色 | #0066CC |
| 其他字段背景 | 灰色 | #C0C0C0 |
| 计算行背景 | 浅灰 | #F0F0F0 |
| 分组行背景 | 白色 | #FFFFFF |

### 字体规范
| 行类型 | 字体大小 | 字体粗细 | 颜色 |
|--------|----------|----------|------|
| 主标题行 | 16pt | 加粗 | 黑色 |
| 汇总行 | 12pt | 加粗 | 深红色 |
| 计算行 | 11pt | 常规 | 黑色 |
| 分组行 | 11pt | 加粗 | 灰色 |
| 账单信息字段 | 10.5pt | 加粗 | 白色 |
| 其他字段 | 10.5pt | 常规 | 灰色 |

## 布局优化规范

### 1. 列宽原则
- 自动调整列宽，确保露出完整的上一级标题
- 考虑分组标题长度和内容长度的最大值
- 最大宽度限制为50个字符
- 最小留3个字符的边距

### 2. 边框规范
- 所有单元格都有黑色细线边框
- 计算行和字段行保持边框清晰

### 3. 合并单元格规则
- 主标题：完整跨列合并（A1:AL1）
- 汇总行：整行合并（A2:AL2）
- 计算行：按逻辑分块合并
- 分组行：按字段组合并
- 数据行：无合并

## 特殊处理说明

### 1. 数据完整性
- 所有数据字段完整输出，不截断
- 明细数据和扩展数据完整显示
- 支付状态不重复显示

### 2. 条件显示
- 设备序列号：仅在非通用账单模板时显示
- 关联信息：仅显示特定类型的关联信息
- 原订单号：根据关联创建类型条件显示

### 3. 样式处理
- 支付状态：根据状态值设置不同背景色
- 账单标识：根据不同类型显示不同文本和颜色
  - 新增/押金：浅绿色
  - 退货：浅黄色
  - 调整：珊瑚色
  - 其他：默认样式

## 实现要点

### 1. 代码实现要点
- 使用openpyxl库进行Excel操作
- 实现精确的单元格合并
- 设置正确的字体、颜色、对齐方式
- 智能列宽调整算法
- 优化内存使用

### 2. 数据映射
- 严格按照convert.mdc中的字段映射关系
- 确保所有字段正确对应到Excel列
- 处理特殊的数据转换逻辑

### 3. 性能优化
- 批量设置样式以提高性能
- 合理使用合并单元格
- 优化内存使用 