# 账单总览Excel样式规范文档

## 概述
本文档描述了账单总览Excel导出的详细样式规范，基于账单总览的实际显示效果。

## 整体布局

### 表头结构（3行）
1. **第1行** - 公司名称行
2. **第2行** - 空行
3. **第3行** - 报告标题行

### 主要内容区域
- **主表格**：账单类型汇总表格（第4-10行）
- **左侧信息区**：收款账户信息（第12-15行）
- **右侧信息区**：财务汇总信息（第12-16行）

## 详细样式规范

### 第1行 - 公司名称行
- **内容**: 公司名称
  - 格式: `{公司名称}`
  - 示例: `深圳未来奇迹人工智能科技有限公司`
- **合并范围**: B2:F2
- **字体**: 微软雅黑，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 无边框
- **行高**: 自适应

### 第2行 - 空行
- **内容**: 空行，用于间距
- **行高**: 自适应

### 第3行 - 报告标题行
- **内容**: 报告标题
  - 格式: `{年份}年{月份}月-账单总览`
  - 示例: `2025年06月-账单总览`
- **合并范围**: B3:F3
- **字体**: 微软雅黑，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 黑色细线边框
- **行高**: 自适应

### 第4行 - 主表格标题行
- **内容**: 表格字段标题 (B4-F4)
  - 格式: `账单类型` `应付金额` `空白` `已付金额` `未付金额`
- **字体**: 微软雅黑，粗体，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 浅灰色
- **边框**: 黑色细线
- **行高**: 自适应

## 主表格结构

### 表格范围
- **起始位置**: B4:F10
- **行内容**: 短租账单、长租账单、销售账单、IT服务账单、合计

### 数据行样式
- **账单类型名称**: 左对齐，黑色字体
- **数值**: 右对齐，黑色字体
- **背景**: 白色
- **边框**: 无边框

### 合计行样式
- **背景**: 浅蓝色
- **字体**: 粗体，黑色
- **特殊处理**: 未付金额显示为红色


## 收款账户信息区域

### 位置和范围
- **位置**: B12:C12
- **标题**: 收款账户（合并B12:C12）

- **位置**: B13:B13
- **标题**: 户名

- **位置**: C13:C13 
- **内容**: 户名数据accountName直接显示

- **位置**: B14:B14 
- **标题**: 开户行

- **位置**: C14:C14 
- **内容**: 开户行数据accountBank直接显示

- **位置**: B15:B15 
- **标题**: 账号

- **位置**: C15:C15
- **内容**: 开户行数据accountBank直接显示

### 字段映射
| Excel行 | 字段名 | 数据类型 | 说明 | 特殊处理 |
|---------|--------|----------|------|----------|
| 户名 | accountName | String | 账户名称 | 直接显示 |
| 开户行 | accountBank | String | 银行名称 | 直接显示 |
| 账号 | accountNo | String | 银行账号 | 直接显示 |

## 财务汇总信息区域

### 位置和范围
- **位置**: E12:F12
- **标题**: 导出时间（合并E12:G12）

- **位置**: E13:E13
- **标题**: 往期未支付

- **位置**: F13:F13 
- **内容**: 往期未支付金额

- **位置**: E14:E14
- **标题**: 本期未付金额

- **位置**: F14:F14 
- **内容**: 本期未付金额数值

- **位置**: E15:E15
- **标题**: 累计未付金额

- **位置**: F15:F15 
- **内容**: 累计未付金额数值

- **位置**: E16:E16
- **标题**: 账户余额

- **位置**: F16:F16 
- **内容**: 账户余额数值

## 数据格式化规则

### 数值格式化
- **金额字段**: 最多保留2位小数，整数不显示小数部分
  - 示例: `65980.05` 或 `17456`
- **日期时间字段**: `yyyy-MM-dd HH:mm:ss` 格式
  - 示例: `2025-07-23 11:54:06`

### 特殊字段处理
- **未付金额**: 在合计行显示为红色
- **导出时间**: 显示完整的日期时间信息
- **账户信息**: 直接显示文本内容

## 列宽调整规则

### 自动调整逻辑
1. **基础计算**: 根据内容长度 + 2个字符间隔
2. **标题考虑**: 如果标题比内容更长，使用标题长度
3. **最小边距**: 确保至少2个字符的边距
4. **最大宽度限制**: 30个字符

### 特殊列宽度限制
以下列需要特殊处理：
- 公司名称列（E-G）：适当加宽
- 账单类型名称列（A）：固定宽度
- 金额列（D-F）：根据数值长度调整

## 金额列数值处理
为确保Excel中的求和功能正常工作，以下金额列直接存储为数值格式：
- 应付金额
- 已付金额
- 未付金额
- 往期未支付
- 本期未付金额
- 累计未付金额
- 账户余额

## 边框规范
- **边框样式**: 黑色细线
- **边框范围**: 主表格区域、收款账户区域、财务汇总区域
- **例外**: 标题行和备注行无边框

## 字体规范
- **主字体**: 微软雅黑
- **字体大小**:
  - 公司名称: 12pt
  - 报告标题: 11pt
  - 表格标题: 10.5pt
  - 数据行: 9pt
  - 备注文字: 9pt

## 颜色规范
- **主标题**: 黑色字体，白色背景
- **表格标题**: 黑色字体，浅灰色背景
- **数据行**: 黑色字体，白色背景
- **合计行**: 黑色字体，浅蓝色背景
- **未付金额**: 红色字体（仅合计行）
- **备注区域**: 黑色字体，白色背景

## 对齐规范
- **公司名称**: 水平居中，垂直居中
- **报告标题**: 水平左对齐，垂直居中
- **表格标题**: 水平居中，垂直居中
- **账单类型名称**: 水平左对齐，垂直居中
- **数值**: 水平右对齐，垂直居中
- **账户信息**: 水平左对齐，垂直居中
- **备注文字**: 水平左对齐，垂直居中

## 特殊处理逻辑


### 条件显示
- **未付金额**: 仅在合计行显示为红色
- **账户信息**: 根据实际数据决定是否显示

### 样式处理
- **合计行**: 浅蓝色背景，粗体字体
- **未付金额**: 红色字体突出显示
- **表格边框**: 完整的表格边框

## 性能优化
- 批量设置样式
- 高效的单元格合并
- 优化的列宽计算算法
- 减少不必要的单元格访问 