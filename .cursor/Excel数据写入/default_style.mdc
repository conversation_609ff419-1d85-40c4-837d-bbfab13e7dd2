# Excel导出样式规范文档

## 概述
本文档描述了账单数据Excel导出的详细样式规范，基于 `excel_data/excel_write/default_excel_write.py` 的当前实现。

## 整体布局

### 表头结构（6行）
1. **第1行** - 主标题行（55px高度）
2. **第2行** - 金额汇总行（30px高度）
3. **第3行** - 计算逻辑描述行（30px高度）
4. **第4行** - 计算逻辑数值行（30px高度）
5. **第5行** - 字段分组行（30px高度）
6. **第6行** - 具体字段行（25px高度）

### 数据行
- **高度**: 23px
- **对齐**: 水平居中，垂直居中

## 详细样式规范

### 第1行 - 主标题行
- **内容**: 动态获取客户名称和账单月份
  - 格式: `{客户名称}\n{账单月份}-长租账单明细`
  - 示例: `张三\n2025年07月-长租账单明细`
- **合并范围**: A1:O1（15列）
- **字体**: 微软雅黑，13pt，粗体，黑色
- **对齐**: 水平居中，垂直居中，自动换行
- **背景**: 白色
- **边框**: 黑色细线
- **行高**: 55px

### 第2行 - 金额汇总行
- **内容**: 账单汇总信息
  - 格式: `{月份}账单应付总金额: {金额}元, 已付: {金额}元, 未付金额: {金额}元`
  - 示例: `07月账单应付总金额: 598.77元, 已付: 598.77元, 未付金额: 0.00元`
- **合并范围**: A2:O2（15列）
- **字体**: 微软雅黑，12pt，粗体，深红色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 黑色细线
- **行高**: 30px

### 第3行 - 计算逻辑描述行
- **内容**: 金额计算逻辑的文字描述
  - 格式: `新增设备金额 往期设备金额 退回设备金额 其他费用 押金 已付金额 未付金额`
  - 各描述间用空字符串分隔
- **字体**: 微软雅黑，11pt，粗体，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 无边框
- **行高**: 30px

### 第4行 - 计算逻辑数值行
- **内容**: 对应第3行的具体数值和运算符
  - 格式: `100 + 0 + 30 + 10 + 99 + 8 - 100 = 147`
- **字体**: 微软雅黑，11pt，粗体，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 无边框
- **行高**: 30px

### 第5行 - 字段分组行
- **分组结构**:
  - A5:O5 - 账单信息
  - P5:V5 - 应付信息
  - W5:AB5 - 客户信息
  - AC5:AF5 - 订单信息
  - AG5:AI5 - 商品信息
  - AJ5:AL5 - 使用信息
- **字体**: 微软雅黑，11pt，粗体，灰色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 黑色细线
- **行高**: 30px

### 第6行 - 具体字段行
- **字段分布**:
  - A6:O6 - 账单信息字段（蓝色背景，白色字体）
  - P6:AL6 - 其他字段（灰色背景，灰色字体）
- **字体**: 微软雅黑，10.5pt
  - 账单信息字段: 粗体，白色
  - 其他字段: 非粗体，灰色
- **对齐**: 水平居中，垂直居中
- **边框**: 黑色细线
- **行高**: 25px

## 数据格式化规则

### 数值格式化
- **金额字段**: 最多保留2位小数，整数不显示小数部分
  - 示例: `1,234.56` 或 `1,234`
- **日期字段**: `yyyy/MM/dd` 格式
  - 示例: `2025/07/15`
- **列表字段**: 用逗号分隔
  - 示例: `SN001, SN002, SN003`

### 特殊字段处理
- **支付状态**: 映射为中文描述
- **是否续租**: 显示为"是"或"否"
- **成色**: 显示为"全新"或"次新"
- **租赁方式**: 显示中文描述

## 列宽调整规则

### 自动调整逻辑
1. **基础计算**: 根据内容长度 + 4个字符间隔
2. **分组标题考虑**: 如果分组标题比内容更长，使用分组标题长度
3. **最小边距**: 确保至少2个字符的边距
4. **最大宽度限制**: 35个字符

### 特殊列宽度限制
以下列的最大宽度限制为23个字符：
- 单号（A列）
- 商品名（D列）
- 配置/详情（E列）
- 设备序列号（K列）

## 金额列数值处理
为确保Excel中的求和功能正常工作，以下金额列直接存储为数值格式：
- 第12列：应付金额
- 第16列：出账金额
- 第17列：退租金额
- 第18列：调整金额
- 第19列：优惠金额
- 第20列：已付金额
- 第21列：已退押金

## 边框规范
- **边框样式**: 黑色细线
- **边框范围**: 所有数据单元格
- **例外**: 第3行和第4行无边框

## 字体规范
- **主字体**: 微软雅黑
- **字体大小**:
  - 主标题: 13pt
  - 汇总行: 12pt
  - 计算行: 11pt
  - 分组行: 11pt
  - 字段行: 10.5pt
  - 数据行: 9pt

## 颜色规范
- **主标题**: 黑色字体，白色背景
- **汇总行**: 深红色字体，白色背景
- **计算行**: 黑色字体，白色背景
- **分组行**: 灰色字体，白色背景
- **账单信息字段**: 白色字体，蓝色背景
- **其他字段**: 灰色字体，灰色背景
- **数据行**: 黑色字体，白色背景

## 对齐规范
- **主标题**: 水平居中，垂直居中，自动换行
- **汇总行**: 水平左对齐，垂直居中
- **计算行**: 水平居中，垂直居中
- **分组行**: 水平居中，垂直居中
- **字段行**: 水平居中，垂直居中
- **数据行**: 水平居中，垂直居中

## 性能优化
- 批量设置样式
- 高效的单元格合并
- 优化的列宽计算算法