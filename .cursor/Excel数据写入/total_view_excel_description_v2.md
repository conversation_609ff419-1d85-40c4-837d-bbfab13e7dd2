# 账单总览工作表详细描述 v2.0

## 概述
账单总览工作表是Excel文件中的第一个工作表，提供客户账单的汇总信息和财务概览。该工作表包含账单类型统计、收款账户信息和财务汇总等关键信息。

## 工作表基本信息
- **工作表名称**: 账单总览
- **工作表位置**: 第一个工作表（在长租账单明细之前）
- **数据范围**: B1:E24
- **列宽设置**: B-E列统一为36字符宽度

## 详细布局结构

### 第1行 - 公司名称行
- **位置**: B1:E1（合并单元格）
- **内容**: 客户名称（customerName）
- **字体**: 微软雅黑，16pt，加粗，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **行高**: 55px

### 第2行 - 空白行
- **位置**: B2:E2（合并单元格）
- **内容**: 空白
- **背景**: 白色

### 第3行 - 报告标题行
- **位置**: B3:E3（合并单元格）
- **内容**: "{账单月份}-账单总览"（如：2024年01月-账单总览）
- **字体**: 微软雅黑，11pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 细边框
- **行高**: 25px

### 第4行 - 主表格标题行
- **位置**: B4:E4
- **标题内容**: 
  - B4: "账单类型"
  - C4: "应付金额"
  - D4: "已付金额"
  - E4: "未付金额"
- **字体**: 微软雅黑，11pt，加粗，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 浅灰色（D3D3D3）
- **边框**: 细边框
- **行高**: 25px

### 第5-8行 - 账单类型数据行
每行显示一种账单类型的汇总数据：

#### 数据行格式（第5-8行）
- **B列（账单类型名称）**:
  - 字体: 微软雅黑，10pt，黑色
  - 对齐: 水平左对齐，垂直居中
  - 背景: 浅灰色（D3D3D3）

- **C列（应付金额）**:
  - 字体: 微软雅黑，10pt，黑色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 浅灰色（D3D3D3）
  - 格式: 数字格式

- **D列（已付金额）**:
  - 字体: 微软雅黑，10pt，黑色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 浅灰色（D3D3D3）
  - 格式: 数字格式

- **E列（未付金额）**:
  - 字体: 微软雅黑，10pt，黑色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 浅灰色（D3D3D3）
  - 格式: 数字格式

#### 账单类型列表
1. **短租账单** (BUSINESS_BILL_TYPE_SHORT)
2. **长租账单** (BUSINESS_BILL_TYPE_LONG)
3. **销售账单** (BUSINESS_BILL_TYPE_SALE)
4. **IT服务账单** (BUSINESS_BILL_TYPE_IT)

### 第10行 - 合计行
- **B列（合计标签）**:
  - 内容: "合计"
  - 字体: 微软雅黑，10pt，加粗，黑色
  - 对齐: 水平左对齐，垂直居中
  - 背景: 蓝色（99CCFF）

- **C列（应付金额合计）**:
  - 字体: 微软雅黑，10pt，加粗，黑色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 蓝色（99CCFF）
  - 格式: 数字格式

- **D列（已付金额合计）**:
  - 字体: 微软雅黑，10pt，加粗，黑色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 蓝色（99CCFF）
  - 格式: 数字格式

- **E列（未付金额合计）**:
  - 字体: 微软雅黑，10pt，加粗，红色
  - 对齐: 水平右对齐，垂直居中
  - 背景: 蓝色（99CCFF）
  - 格式: 数字格式

### 第11行 - 收款账户信息标题行
- **位置**: B11:C11（合并单元格）
- **内容**: "收款账户"
- **字体**: 微软雅黑，10pt，加粗，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 细边框

### 第12-14行 - 收款账户详细信息
每行显示一个账户信息字段：

#### 户名信息（第12行）
- **B12**: "户名"
- **C12**: 账户名称（accountName）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 细边框

#### 开户行信息（第13行）
- **B13**: "开户行"
- **C13**: 开户行名称（accountBank）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 细边框

#### 账号信息（第14行）
- **B14**: "账号"
- **C14**: 账号号码（accountNo）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **边框**: 细边框

### 第15-16行 - 空白行
- **内容**: 空白
- **背景**: 白色

### 第17行 - 财务汇总信息标题行
- **位置**: B17:C17（合并单元格）
- **内容**: "导出时间: {当前时间}"
- **字体**: 微软雅黑，10pt，加粗，黑色
- **对齐**: 水平居中，垂直居中
- **背景**: 白色
- **边框**: 细边框

### 第18-21行 - 财务汇总详细信息
每行显示一个财务汇总字段：

#### 往期未支付（第18行）
- **B18**: "往期未支付"
- **C18**: 往期未支付金额（previousUnpaidAmount）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 标签左对齐，数值居中对齐
- **背景**: 白色
- **边框**: 细边框
- **格式**: 数字格式

#### 本期未付金额（第19行）
- **B19**: "本期未付金额"
- **C19**: 本期未付金额数值
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 标签左对齐，数值居中对齐
- **背景**: 白色
- **边框**: 细边框
- **格式**: 数字格式

#### 累计未付金额（第20行）
- **B20**: "累计未付金额"
- **C20**: 累计未付金额数值（往期+本期）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 标签左对齐，数值居中对齐
- **背景**: 白色
- **边框**: 细边框
- **格式**: 数字格式

#### 账户余额（第21行）
- **B21**: "账户余额"
- **C21**: 账户余额数值（accountBalance）
- **字体**: 微软雅黑，10pt，黑色
- **对齐**: 标签左对齐，数值居中对齐
- **背景**: 白色
- **边框**: 细边框
- **格式**: 数字格式

### 第22行 - 空白行
- **内容**: 空白
- **背景**: 白色

### 第23-24行 - 备注信息
每行显示一条备注信息：

#### 备注1（第23行）
- **位置**: B23:E23（合并单元格）
- **内容**: "备注:如对账单明细金额有异议的,请在收到账单3个工作日内书面回复我司异议内容或将异议发送至我司经办人邮箱;"
- **字体**: 微软雅黑，11pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **行高**: 25px

#### 备注2（第24行）
- **位置**: B24:E24（合并单元格）
- **内容**: "否则视为贵司对账单无异议,认可我司账单内容。账单确认后请及时安排付款,回传水单谢谢!"
- **字体**: 微软雅黑，11pt，黑色
- **对齐**: 水平左对齐，垂直居中
- **背景**: 白色
- **行高**: 25px

## 样式规范

### 字体规范
- **主要字体**: 微软雅黑
- **公司名称**: 16pt，加粗
- **报告标题**: 11pt
- **表格标题**: 11pt，加粗
- **数据内容**: 10pt
- **合计行**: 10pt，加粗
- **收款账户标题**: 10pt，加粗
- **财务汇总标题**: 10pt，加粗
- **备注信息**: 11pt

### 颜色规范
- **背景色**:
  - 白色: FFFFFF（默认背景）
  - 浅灰色: D3D3D3（表格标题行、数据行）
  - 蓝色: 99CCFF（合计行）
- **字体色**:
  - 黑色: 000000（默认）
  - 红色: FF0000（未付金额合计）

### 对齐规范
- **文本内容**: 左对齐
- **数字内容**: 右对齐（数据行），居中对齐（财务汇总）
- **标题内容**: 居中对齐
- **垂直对齐**: 全部垂直居中

### 边框规范
- **边框样式**: 细边框（THIN_BORDER）
- **应用范围**: 表格标题行、数据行、收款账户信息、财务汇总信息

## 数据来源

### 账单数据来源
- **客户名称**: bill_data.customerName
- **账单月份**: bill_data.currentBillMonth
- **账单类型数据**: split_data中各账单类型的汇总统计

### 收款账户数据来源
- **账户名称**: bill_data.shroffAccount.accountName
- **开户行**: bill_data.shroffAccount.accountBank
- **账号**: bill_data.shroffAccount.accountNo

### 财务汇总数据来源
- **往期未支付**: bill_data.customerAccount.previousUnpaidAmount
- **本期未付金额**: 各账单类型未付金额合计
- **累计未付金额**: 往期+本期
- **账户余额**: bill_data.customerAccount.accountBalance

## 特殊处理

### 合并单元格处理
- 使用`get_merge_start_cell`方法确保在合并单元格的起始位置写入数据
- 避免在非起始单元格设置值导致的错误

### 背景色处理
- 使用`set_white_background_for_unset_cells`方法确保B1:E24范围内所有未设置背景的单元格为白色
- 保持已有背景色设置不变

### 动态列宽调整
- 根据实际内容长度动态调整列宽
- 确保最小宽度为23字符
- B-E列统一设置为36字符宽度

## 工作表创建条件
- 只有当存在账单数据时才创建此工作表
- 在长租账单明细工作表之前插入
- 作为Excel文件的第一个工作表 