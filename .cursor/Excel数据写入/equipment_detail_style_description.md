# Excel租赁设备明细样式描述

## 整体布局结构

### 行结构定义
- **表头区域**：固定3行表头
  - **第1行**：主标题行（完整跨列合并）
  - **第2行**：汇总信息行（整行合并）
  - **第3行**：字段标题行（无跨列合并）
- **数据区域**：从第4行开始，行数根据实际数据量动态确定

## 详细样式规范

### 1. 第1行 - 主标题行
**单元格范围**：A1:V1（完整跨列合并，22列）
**内容**：2025年07月-租赁设备明细
**样式要求**：
- 合并状态：完整跨列合并
- 对齐方式：水平居中 + 垂直居中
- 字体：微软雅黑｜16pt｜加粗｜黑色
- 背景：纯白无填充
- 边框：黑色细线边框

### 2. 第2行 - 汇总信息行
**单元格范围**：A2:V2（整行合并，22列）
**内容示例**：07月期初数量:166,退货数量:19,在租数量:147
**样式要求**：
- 合并状态：整行单单元格合并
- 对齐方式：水平左对齐 + 垂直居中
- 字体：微软雅黑｜12pt｜常规｜黑色
- 特殊样式：数字"147"显示为红色
- 背景：纯白无填充（透明）
- 边框：黑色细线边框

### 3. 第3行 - 字段标题行
**单元格范围**：无跨列合并
**列标题结构**（共22列）：
1. 收件人
2. 省
3. 市
4. 区
5. 详细地址
6. 子公司
7. 品牌
8. 类别
9. 商品名
10. 成色
11. 租赁方式
12. 配置
13. 设备序列号
14. 数量
15. 设备价值
16. 在租状态
17. 单号
18. 起租日期
19. 预计归还日期
20. 退租日期
21. 类型
22. 归属公司

**样式要求**：
- 字体：微软雅黑｜10.5pt｜加粗｜白色
- 背景：蓝色填充（RGB: 0,102,204）
- 合并状态：无合并（每列独立）
- 对齐方式：水平居中 + 垂直居中
- 边框：黑色细线分隔列
- 行高：标准行高，适应文字内容

## 数据格式规范

### 1. 数值格式
- 数量字段：整数显示
- 设备价值：保留两位小数
- 对齐方式：右对齐

### 2. 日期格式
- 格式：yyyy/MM/dd
- 示例：2024/07/15
- 对齐方式：居中对齐

### 3. 状态字段格式
- 在租状态：直接显示描述文本
- 成色：显示为"全新"或"次新"
- 租赁方式：显示具体租赁方式描述
- 类型：显示设备类型描述

### 4. 地址信息格式
- 省、市、区：显示省份、城市、区县名称
- 详细地址：完整地址信息
- 对齐方式：左对齐

## 样式系统规范

### 颜色方案
| 元素类型 | 颜色方案 | RGB值 |
|---------|---------|-------|
| 主标题字体 | 黑色 | #000000 |
| 汇总行字体 | 黑色 | #000000 |
| 汇总行关键数字 | 红色 | #FF0000 |
| 字段标题字体 | 白色 | #FFFFFF |
| 字段标题背景 | 蓝色 | #0066CC |
| 数据行字体 | 黑色 | #000000 |
| 数据行背景 | 白色 | #FFFFFF |
| 边框颜色 | 黑色 | #000000 |

### 字体规范
| 行类型 | 字体大小 | 字体粗细 | 颜色 |
|--------|----------|----------|------|
| 主标题行 | 16pt | 加粗 | 黑色 |
| 汇总行 | 12pt | 常规 | 黑色/红色 |
| 字段标题行 | 10.5pt | 加粗 | 白色 |
| 数据行 | 9pt | 常规 | 黑色 |

## 布局优化规范

### 1. 列宽原则
- 自动调整列宽，确保内容完整显示
- 考虑字段标题长度和内容长度的最大值
- 地址相关列适当加宽
- 设备序列号列适当加宽
- 最小留2个字符的边距

### 2. 边框规范
- 所有单元格都有黑色细线边框
- 字段标题行和数据行保持边框清晰
- 主标题和汇总行保持边框

### 3. 合并单元格规则
- 主标题：完整跨列合并（A1:V1）
- 汇总行：整行合并（A2:V2）
- 字段标题行：无合并
- 数据行：无合并

## 特殊处理说明

### 1. 数据完整性
- 所有设备信息完整输出，不截断
- 地址信息完整显示
- 设备序列号完整显示

### 2. 条件显示
- 退租日期：仅在设备已退租时显示
- 预计归还日期：显示预期归还时间
- 在租状态：根据设备当前状态显示

### 3. 样式处理
- 汇总行中的关键数字（在租数量）用红色突出显示
- 字段标题使用蓝色背景突出显示
- 数据行使用标准白色背景

## 字段映射关系

### 1. 基础信息字段
| Excel列 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 收件人 | consigneeName | String | 收件人姓名 |
| 省 | provinceName | String | 省份名称 |
| 市 | cityName | String | 城市名称 |
| 区 | districtName | String | 区县名称 |
| 详细地址 | address | String | 详细地址信息 |
| 子公司 | customerSubName | String | 子公司名称 |

### 2. 设备信息字段
| Excel列 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 品牌 | brandName | String | 设备品牌名称 |
| 类别 | categoryName | String | 设备类别 |
| 商品名 | productName | String | 商品名称 |
| 成色 | isNew | Integer | 设备成色（1-全新，0-次新） |
| 租赁方式 | rentModeDesc | String | 租赁方式描述 |
| 配置 | productSkuName | String | 商品SKU配置 |
| 设备序列号 | serialNumberSet | List<String> | 设备序列号集合 |
| 数量 | count | Integer | 设备数量 |
| 设备价值 | productSkuPrice | Decimal | 设备价值 |

### 3. 租赁信息字段
| Excel列 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 在租状态 | rentStatus | Integer | 租赁状态 |
| 单号 | orderNo | String | 订单号 |
| 起租日期 | rentStartTime | Date | 租赁开始时间 |
| 预计归还日期 | expectReturnTime | Date | 预期归还时间 |
| 退租日期 | returnTime | Date | 实际退租时间 |
| 类型 | orderType | Integer | 订单类型 |
| 归属公司 | subsidiaryId | Integer | 归属公司ID |

## 实现要点

### 1. 代码实现要点
- 使用openpyxl库进行Excel操作
- 实现精确的单元格合并
- 设置正确的字体、颜色、对齐方式
- 智能列宽调整算法
- 优化内存使用

### 2. 数据映射
- 严格按照字段映射关系
- 确保所有字段正确对应到Excel列
- 处理特殊的数据转换逻辑

### 3. 性能优化
- 批量设置样式以提高性能
- 合理使用合并单元格
- 优化内存使用

### 4. 特殊处理
- 汇总行中的关键数字需要特殊颜色处理
- 设备序列号需要合并显示
- 地址信息需要完整显示
- 日期格式需要统一处理 