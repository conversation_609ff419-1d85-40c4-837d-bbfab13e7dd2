# 账单数据结构分析
# 基于 oms_api_client.py 和 default_excel_write.py 重新梳理

## 1. 主要数据类结构

### 1.1 BillPeriodStatementStatistics (账单期结算统计)
- currentBillMonth: datetime - 当前账单月份
- customerId: int - 客户ID
- customerNo: str - 客户编号
- customerName: str - 客户名称
- billDateOfPayment: datetime - 账单支付日期
- collectionAccount: CollectionAccount - 收款账户信息
- customerAccount: CustomerAccount - 客户账户信息
- billPeriodStatementList: List[BillPeriodStatement] - 账单期结算列表
- billingDetailsStatistics: Optional[BillPeriodStatementStatistics] - 计费明细统计(递归)
- shroffAccount: CollectionAccount - 新的收款账户信息

### 1.2 BillPeriodStatement (账单期结算)
- businessMode: int - 业务模式
- billType: str - 账单类型
- billAmount: Decimal - 账单金额
- paidAmount: Decimal - 已付金额
- unPaidAmount: Decimal - 未付金额
- newEquipmentAmount: Decimal - 新增设备金额
- oldEquipmentAmount: Decimal - 往期设备金额
- returnEquipmentAmount: Decimal - 退回设备金额
- otherAmount: Decimal - 其他费用金额
- adjustmentEquipmentAmount: Decimal - 调整设备金额
- itServiceAmount: Decimal - IT服务金额
- depositEquipmentAmount: Decimal - 押金设备金额
- couponAmount: Decimal - 优惠券金额
- periodStartEquipmentCount: int - 期初设备数量
- returnEquipmentCount: int - 退回设备数量
- rentingEquipmentCount: int - 租赁设备数量
- billPeriodDetailList: List[BillPeriodStatementDetail] - 账单期明细列表
- excelBillPeriodDetailList: List[BillPeriodStatementDetail] - Excel账单期明细列表
- penaltyAndOtherBillPeriodDetailList: List[BillPeriodStatementDetail] - 罚金和其他账单期明细列表
- feesBillPeriodDetailList: List[BillPeriodStatementDetail] - 费用账单期明细列表
- accountOrderMonthEquipmentDetailList: List[AccountOrderMonthEquipmentDetail] - 账户订单月设备明细列表

### 1.3 BillPeriodStatementDetail (账单期结算明细) - 核心数据结构
#### 基础业务信息
- businessOrderType: int - 业务订单类型
- businessOrderNo: str - 业务订单号
- businessOrderId: int - 业务订单ID
- rentStartTime: datetime - 租赁开始时间
- productName: str - 商品名称
- categoryName: str - 类别名称
- productSkuName: str - 商品SKU名称
- description: str - 描述
- unitAmount: Decimal - 单价
- couponUnitAmount: Decimal - 优惠单价
- count: int - 数量

#### 时间信息
- billExpectPayTime: datetime - 账单预期支付时间
- periodStartTime: datetime - 期初时间
- periodEndTime: datetime - 期末时间
- statementStartTime: datetime - 结算开始时间
- statementEndTime: datetime - 结算结束时间

#### 期数信息
- phase: int - 当前期数
- totalPhase: int - 总期数
- payStatus: int - 支付状态
- billAmount: Decimal - 账单金额
- partPayAmount: Decimal - 部分支付金额
- billPeriodFlag: str - 账单标识 (枚举字符串)
- statisticsOrderType: int - 统计订单类型

#### 订单信息
- associationCreateType: int - 关联创建类型
- orderStatus: int - 订单状态
- originOrderNo: str - 原订单号
- returnTime: Optional[datetime] - 退回时间
- orderSubCompanyId: int - 订单子公司ID
- rentLengthType: int - 租赁长度类型

#### 商品信息
- isNew: int - 是否新品
- isReturnAnyTime: int - 是否随时可退
- rentMode: int - 租赁模式
- rentModeDesc: str - 租赁模式描述
- rentSceneMark: str - 租赁场景备注

#### 收货信息
- consigneeName: str - 收件人姓名
- provinceName: str - 省份名称
- cityName: str - 城市名称
- districtName: str - 区县名称
- address: str - 详细地址
- customerSubName: str - 客户子公司名称
- subsidiaryId: int - 子公司ID

#### 发货信息
- deliveryMode: int - 配送模式
- deliverySubCompanyId: int - 配送子公司ID

#### 支付信息
- paidAmount: Decimal - 已付金额
- returnAmount: Decimal - 退回金额
- payInfo: str - 支付信息
- payInfoGroupUuid: str - 支付信息组UUID

#### 优惠和冲正信息
- couponAmount: Decimal - 优惠金额
- couponInfo: str - 优惠信息
- discountedAmount: Decimal - 折扣金额
- correctAmount: Decimal - 冲正金额
- hasCorrectAmount: bool - 是否有冲正金额

#### 账单结算信息
- billStatementAmount: Decimal - 账单结算金额
- billReturnStatementAmount: Decimal - 账单退回结算金额

#### 设备序列号信息
- serialNumberSet: List[str] - 设备序列号集合
- statementOrderDetailNoList: List[str] - 结算订单明细号列表

#### 使用信息
- customerUser: str - 客户使用人
- customerOrganization: str - 客户使用组织
- customerRemark: str - 客户使用备注

#### 续租信息
- isRelet: int - 是否续租标识

#### 新增字段
- businessMode: int - 业务类型
- productSummary: str - 商品摘要
- orderItemId: int - 订单项ID
- orderItemType: int - 订单项类型
- isChangeAllocation: int - 是否改配单
- returnOrderCreateType: int - 退货单创建类型
- rentType: int - 租赁方式 (2-月租；1-天租)
- businessEquipmentType: int - 大期业务设备类型

#### 合并相关字段
- orderItemMerge: bool - 原订单项维度是否需要合并
- payInfoMerge: bool - 原支付维度是否需要合并
- firstOrderItemMergeRecord: bool - 是否原订单项维度单元格合并的第一条
- orderItemMergeRowCount: int - 原订单项维度单元格合并行数
- firstPayInfoMergeRecord: bool - 是否原支付维度单元格合并的第一条
- payInfoMergeRowCount: int - 原支付维度单元格合并行数

#### 平台相关字段
- referPlatformThirdNo: str - 关联平台第三方单号
- firstReferPlatformThirdNo: str - 关联平台首期第三方单号
- thirdSubOrderNo: str - 第三方子订单订单号
- couponStatementCorrectOrderIds: List[int] - 优惠券产生优惠的冲正ID

### 1.4 CollectionAccount (收款账户信息)
- accountName: str - 账户名称
- accountBank: str - 开户银行
- accountNo: str - 账户号码

### 1.5 CustomerAccount (客户账户信息)
- customerBalanceAmount: Decimal - 客户余额金额
- totalUnPaidAmount: Decimal - 总未付金额
- totalNeedPayAmount: Decimal - 总需付金额

### 1.6 AccountOrderMonthEquipmentDetail (账户订单月设备明细)
#### 基础信息
- consigneeName: str - 收件人姓名
- provinceName: str - 省份名称
- cityName: str - 城市名称
- districtName: str - 区县名称
- address: str - 详细地址
- customerSubName: str - 客户子公司名称

#### 商品信息
- brandName: str - 品牌名称
- productName: str - 商品名称
- categoryName: str - 类别名称
- isNewProduct: int - 是否新品
- isReturnAnyTime: int - 是否随时可退
- productSkuName: str - 商品SKU名称
- productSkuPrice: Decimal - 商品SKU价格

#### 租赁信息
- rentingProductCount: int - 租赁产品数量
- orderNo: str - 订单号
- rentStartTime: datetime - 租赁开始时间
- returnTime: Optional[datetime] - 退回时间
- expectReturnTime: datetime - 预期退回时间
- rentLengthType: int - 租赁长度类型
- rentStatus: int - 租赁状态
- rentMode: int - 租赁模式
- rentModeDesc: str - 租赁模式描述

#### 业务信息
- orderSubCompanyId: int - 订单子公司ID
- businessMode: int - 业务模式
- orderItemId: int - 订单项ID
- orderItemType: int - 订单项类型
- orderId: int - 订单ID
- orderType: int - 订单类型
- subsidiaryId: int - 子公司ID

#### 序列号信息
- serialNumberSet: List[str] - 设备序列号集合

## 2. 枚举类型

### 2.1 BusinessBillType (账单类型枚举)
- BUSINESS_BILL_TYPE_LONG = "BUSINESS_BILL_TYPE_LONG"
- BUSINESS_BILL_TYPE_SHORT = "BUSINESS_BILL_TYPE_SHORT"
- BUSINESS_BILL_TYPE_SALE = "BUSINESS_BILL_TYPE_SALE"
- BUSINESS_BILL_TYPE_SERVICE = "BUSINESS_BILL_TYPE_SERVICE"

### 2.2 PayStatus (支付状态枚举)
- UNPAID = 0 - 未支付
- PARTIAL_PAID = 1 - 部分支付
- PAID = 2 - 已支付
- REFUNDED = 3 - 已退款

### 2.3 BusinessOrderType (业务单类型枚举)
- RENTAL_ORDER = 1 - 租赁订单
- SALE_ORDER = 2 - 销售订单
- SERVICE_ORDER = 3 - 服务订单

### 2.4 RentMode (租赁方式枚举)
- FIXED_TERM = 1 - 固定租期
- RENT_ANYTIME = 2 - 即租即还
- RENT_TO_OWN = 3 - 租完即送
- INSTALLMENT_SALE = 4 - 分期销售
- COMBINATION_RENTAL = 5 - 组合租赁

## 3. 映射关系

### 3.1 支付状态映射 (PAY_STATUS_MAPPING)
- 0: "未支付"
- 4: "部分支付"
- 8: "已支付"
- 16: "无需支付"
- 20: "无需支付"

### 3.2 订单状态映射 (ORDER_STATUS_MAPPING)
- 0: "待提交"
- 4: "审核中"
- 5: "待备货"
- 6: "备货中"
- 8: "待发货"
- 12: "处理中"
- 16: "已发货"
- 18: "已签收"
- 20: "租赁中"
- 22: "部分退还"
- 24: "全部归还"
- 28: "取消"
- 32: "结束"

### 3.3 关联创建类型映射 (ASSOCIATION_CREATE_TYPE_MAPPING)
- 1: "ERP创建"
- 4: "样机转租赁"
- 5: "客需换货"
- 7: "赔偿买断销售"
- 9: "租完即送到期销售"
- 11: "ERP退货单"
- 14: "异常检测"

### 3.4 租赁模式映射 (RENT_MODE_MAPPING)
- 1: "固定租期"
- 2: "即租即还"
- 3: "租完即送"
- 4: "分期销售"
- 5: "组合租赁"

### 3.5 账单标志映射 (BILL_PERIOD_FLAG_MAPPING)
- "BILL_PERIOD_FLAG_NEW": "新增设备"
- "BILL_PERIOD_FLAG_OLD": "往期设备"
- "BILL_PERIOD_FLAG_RETURN": "退回设备"
- "BILL_PERIOD_FLAG_ADJUST": "调整项"
- "BILL_PERIOD_FLAG_OTHER": "其他费用"
- "BILL_PERIOD_FLAG_IT_SERVICE": "服务金额"
- "BILL_PERIOD_FLAG_DEPOSIT": "押金金额"

## 4. Excel输出字段映射

### 4.1 账单信息 (15列)
1. 单号 - businessOrderNo
2. 起租时间 - rentStartTime
3. 类别 - categoryName
4. 商品名 - productName
5. 配置/详情 - productSkuName
6. 单价（元） - unitAmount/couponUnitAmount (优先使用优惠单价)
7. 数量 - count
8. 本期开始日 - periodStartTime
9. 本期结束日 - periodEndTime/statementEndTime (退回设备用periodEndTime，否则用statementEndTime)
10. 期数 - phase/totalPhase (只有两个都不为空时才显示)
11. 设备序列号 - serialNumberSet
12. 应付金额 - billAmount
13. 支付状态 - payStatus
14. 是否续租 - isRelet
15. 账单标识 - billPeriodFlag (复杂逻辑处理)

### 4.2 应付信息 (7列)
16. 出账金额 - billStatementAmount
17. 退租金额 - billReturnStatementAmount
18. 调整金额 - correctAmount
19. 优惠金额 - couponAmount
20. 已付金额 - paidAmount
21. 已退押金 - returnAmount
22. 说明 - payInfo

### 4.3 客户信息 (6列)
23. 收件人 - consigneeName
24. 省 - provinceName
25. 市 - cityName
26. 区 - districtName
27. 详细地址 - address
28. 分子公司 - customerSubName

### 4.4 订单信息 (4列)
29. 订单状态 - orderStatus
30. 关联信息 - associationCreateType
31. 原订单 - originOrderNo
32. 退租日期 - returnTime

### 4.5 商品信息 (3列)
33. 成色 - isNew
34. 租赁方式 - rentModeDesc/rentMode
35. 备注 - rentSceneMark

### 4.6 使用信息 (3列)
36. 使用人 - customerUser
37. 使用组织 - customerOrganization
38. 使用备注 - customerRemark

## 5. 特殊处理逻辑

### 5.1 单价处理
- 优先使用 couponUnitAmount (优惠单价)
- 如果 couponUnitAmount 为空或小于0，使用 unitAmount
- 根据 rentType 添加后缀：2-月租显示"/月"，1-天租显示"/天"

### 5.2 本期结束日处理
- 如果 billPeriodFlag == "BILL_PERIOD_FLAG_RETURN"，使用 periodEndTime
- 否则使用 statementEndTime

### 5.3 期数处理
- 只有当 phase 和 totalPhase 都不为空时才显示
- 显示格式：phase/totalPhase

### 5.4 账单标识处理
- BILL_PERIOD_FLAG_NEW: 检查 isChangeAllocation
  - isChangeAllocation == 1: "改配新增"
  - isChangeAllocation == 2: "维修换货新增"
  - 否则: "新增设备"
- BILL_PERIOD_FLAG_RETURN: 检查 returnOrderCreateType
  - returnOrderCreateType == 5: "改配退回"
  - returnOrderCreateType == 6 或 7: "维修换货退回"
  - 否则: "退回设备"
- 其他按映射显示

### 5.5 支付状态处理
- payStatus == 0: 显示"未支付"，背景色 FFCC99
- payStatus == 20: 转换为 8 后显示"已支付"
- 其他按映射显示

### 5.6 退回设备背景色
- 如果 billPeriodFlag == "BILL_PERIOD_FLAG_RETURN"，整行背景色设为 FFFF99

### 5.7 金额数值处理
- 去掉千分位逗号
- 整数显示为整数，小数显示两位小数

### 5.8 空值处理
- 所有空值显示为空白，不显示"无数据"

## 6. 合并单元格逻辑

### 6.1 订单信息合并 (列12-13)
- 应付金额、支付状态
- 根据 orderItemMerge 和 firstOrderItemMergeRecord 判断

### 6.2 支付信息合并 (列16-22)
- 出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金、说明
- 根据 payInfoMerge 和 firstPayInfoMergeRecord 判断

## 7. 数据获取流程

1. 通过 OMSApiClient.get_bill_data() 获取账单数据
2. 解析为 BillPeriodStatementStatistics 对象
3. 遍历 billPeriodStatementList
4. 优先使用 excelBillPeriodDetailList，如果没有则使用 billPeriodDetailList
5. 对每个 BillPeriodStatementDetail 进行Excel输出处理 