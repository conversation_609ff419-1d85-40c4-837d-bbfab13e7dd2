---
description: use these rules for every chat
globs: 
alwaysApply: false
---
# 客户Excel账单生成系统设计文档

## 目录
1. [系统概述](#系统概述)
2. [核心概念](#核心概念)
3. [客户配置规则](#客户配置规则)
4. [账单生成规则](#账单生成规则)
5. [数据映射规则](#数据映射规则)
6. [样式规则](#样式规则)
7. [文件命名规则](#文件命名规则)
8. [数据验证规则](#数据验证规则)
9. [输出规则](#输出规则)
10. [异常处理规则](#异常处理规则)

## 系统概述

### 1.1 系统目标
- 为不同客户生成个性化的Excel账单
- 支持多种数据源和格式
- 提供灵活的模板配置
- 实现批量处理和自动化

### 1.2 系统特点
- **个性化**：每个客户有独特的账单格式
- **可扩展**：支持新客户和新模板类型
- **自动化**：支持定时生成和批量处理
- **可追溯**：完整的操作日志和错误记录

## 核心概念

### 2.1 客户模板
每个客户都有独特的账单格式模板，包含：
- 表头结构
- 数据字段映射
- 样式配置
- 布局设置

### 2.2 数据源
系统支持多种数据源：
- 数据库（MySQL）
- API接口（OMS接口）
- 文件导入（JSON、Excel）

### 2.3 动态生成
根据客户配置自动生成个性化账单：
- 数据过滤和转换
- 格式化和计算
- 样式应用
- 文件输出

## 客户配置规则

### 3.1 模板配置结构

```yaml
客户配置:
  基本信息:
    客户代码: "CUST001"
    客户名称: "示例客户A"
    联系人: "张三"
    联系电话: "13800138000"
    邮箱: "<EMAIL>"
  
  模板配置:
    模板类型: "标准发票"
    模板版本: "v1.0"
    创建时间: "2024-01-01"
    更新时间: "2024-12-01"
    
    表头配置:
      起始行: 3
      表头行: 3
      数据起始行: 4
      合计行: "自动计算"
      
      字段列表:
        - 字段名: "订单号"
          列位置: "A"
          宽度: 15
          对齐: "左对齐"
          必填: true
          
        - 字段名: "商品名称"
          列位置: "B"
          宽度: 30
          对齐: "左对齐"
          必填: true
          
        - 字段名: "数量"
          列位置: "C"
          宽度: 10
          对齐: "右对齐"
          必填: true
          数据类型: "整数"
          
        - 字段名: "单价"
          列位置: "D"
          宽度: 12
          对齐: "右对齐"
          必填: true
          数据类型: "小数"
          小数位数: 2
          
        - 字段名: "小计"
          列位置: "E"
          宽度: 12
          对齐: "右对齐"
          必填: false
          计算规则: "数量 * 单价"
          数据类型: "小数"
          小数位数: 2
    
    样式配置:
      表头样式:
        字体: "微软雅黑"
        字号: 12
        粗体: true
        颜色: "#000000"
        背景色: "#E6E6E6"
        边框: "实线"
        对齐: "居中"
        
      数据样式:
        字体: "微软雅黑"
        字号: 10
        粗体: false
        颜色: "#333333"
        背景色: "#FFFFFF"
        边框: "细线"
        对齐: "左对齐"
        
      合计样式:
        字体: "微软雅黑"
        字号: 11
        粗体: true
        颜色: "#000000"
        背景色: "#F0F0F0"
        边框: "粗线"
        对齐: "右对齐"
        
      条件格式:
        - 条件: "小计 < 0"
          字体颜色: "#FF0000"
          背景色: "#FFE6E6"
          
        - 条件: "数量 > 100"
          字体颜色: "#0066CC"
          背景色: "#E6F3FF"
```



### 3.3 计算规则配置

```yaml
计算规则:
  基础计算:
    小计: "数量 * 单价"
    折扣金额: "小计 * 折扣率"
    实收金额: "小计 - 折扣金额"
    
  汇总计算:
    商品小计: "SUM(小计)"
    折扣总额: "SUM(折扣金额)"
    实收总额: "SUM(实收金额)"
    税额: "实收总额 * 税率"
    最终金额: "实收总额 + 税额"
    
  税率配置:
    默认税率: 0.13
    免税商品: ["教育用品", "医疗用品"]
    低税率商品: ["农产品", "图书"]
    低税率: 0.09
```

## 账单生成规则

### 4.1 生成流程

```python
生成流程:
  1. 参数验证
     - 验证客户代码是否存在
     - 验证时间范围是否有效
     - 验证数据源连接是否正常
     
  2. 数据获取
     - 连接数据源
     - 执行查询获取原始数据
     - 数据格式转换
     
  3. 数据处理
     - 字段映射转换
     - 数据计算（小计、合计等）
     - 数据排序和分组
     
  4. 模板应用
     - 读取客户的账单生成规则描述
     - 应用样式配置
     - 填充数据到模板
     
  5. 文件生成
     - 创建Excel文件
     - 写入数据
     - 应用样式和格式
     - 保存文件
     
  6. 后处理
     - 文件命名
     - 文件压缩（可选）
     - 发送通知
     - 记录日志
```


## 数据映射规则

### 5.1 字段映射类型

```yaml
字段映射类型:
  直接映射:
    源字段: "order_id"
    目标字段: "订单号"
    转换规则: "无"
    
  格式转换:
    源字段: "order_date"
    目标字段: "订单日期"
    转换规则: "日期格式化"
    格式: "YYYY-MM-DD"
    
  计算映射:
    源字段: ["quantity", "unit_price"]
    目标字段: "小计"
    转换规则: "乘法计算"
    公式: "quantity * unit_price"
    
  条件映射:
    源字段: "status"
    目标字段: "状态描述"
    转换规则: "条件判断"
    条件:
      "PENDING": "待处理"
      "PROCESSING": "处理中"
      "COMPLETED": "已完成"
      "CANCELLED": "已取消"
      "default": "未知状态"
    
  聚合映射:
    源字段: "subtotal"
    目标字段: "总金额"
    转换规则: "求和聚合"
    分组字段: "order_id"
```

### 5.2 数据类型转换

```yaml
数据类型转换:
  字符串转换:
    源类型: "字符串"
    目标类型: "字符串"
    处理规则:
      - 去除首尾空格
      - 转换为大写（可选）
      - 截取指定长度（可选）
    
  数字转换:
    源类型: "字符串/数字"
    目标类型: "数字"
    处理规则:
      - 去除非数字字符
      - 转换为浮点数
      - 保留指定小数位
      - 四舍五入
    
  日期转换:
    源类型: "字符串/日期"
    目标类型: "日期"
    处理规则:
      - 解析日期格式
      - 转换为标准格式
      - 时区转换（可选）
    
  布尔转换:
    源类型: "字符串/数字"
    目标类型: "布尔值"
    处理规则:
      - 真值: ["1", "true", "是", "Y", "yes"]
      - 假值: ["0", "false", "否", "N", "no"]
```

## 样式规则

### 6.1 基础样式配置

```yaml
基础样式:
  字体配置:
    默认字体: "微软雅黑"
    备用字体: "宋体"
    字号范围: [8, 72]
    默认字号: 10
    
  颜色配置:
    主色调: "#2E86AB"
    辅助色: "#A23B72"
    成功色: "#28A745"
    警告色: "#FFC107"
    错误色: "#DC3545"
    信息色: "#17A2B8"
    
  边框配置:
    边框样式: ["无", "细线", "粗线", "双线", "虚线", "点线"]
    边框颜色: "#000000"
    边框宽度: 1
```

### 6.2 条件格式规则

```yaml
条件格式:
  数值条件:
    - 条件: "小计 > 1000"
      样式:
        字体颜色: "#0066CC"
        背景色: "#E6F3FF"
        粗体: true
        
    - 条件: "小计 < 0"
      样式:
        字体颜色: "#FF0000"
        背景色: "#FFE6E6"
        斜体: true
        
    - 条件: "数量 > 100"
      样式:
        字体颜色: "#28A745"
        背景色: "#E6F7E6"
        
  文本条件:
    - 条件: "状态 = '已完成'"
      样式:
        字体颜色: "#28A745"
        背景色: "#E6F7E6"
        
    - 条件: "状态 = '已取消'"
      样式:
        字体颜色: "#DC3545"
        背景色: "#FFE6E6"
        
  日期条件:
    - 条件: "订单日期 > 今天"
      样式:
        字体颜色: "#FFC107"
        背景色: "#FFF8E6"
        
    - 条件: "订单日期 < 今天-30天"
      样式:
        字体颜色: "#6C757D"
        背景色: "#F8F9FA"
```


## 文件命名规则

### 7.1 基础命名规则

```yaml
文件命名规则:
  基础格式: "{客户名称}_{账单年月}{是否包含SN}.xlsx"
  
  参数说明:
    客户名称: "客户名称，对应字段customerName"
    年月: "YYYYMM格式"
    是否包含SN: "如果是SN账单，则需要添加_SN"
  
  示例:
    - "CUST001_发票_202412_001.xlsx"
    - "CUST002_清单_202412_001.xlsx"
    - "CUST003_对账单_202412_001.xlsx"
```




## 数据验证规则

### 8.1 基础验证规则

```yaml
数据验证:
  必填字段验证:
    - 订单号: "不能为空，长度1-50字符"
    - 商品名称: "不能为空，长度1-100字符"
    - 数量: "必须为正整数，范围1-999999"
    - 单价: "必须为正数，范围0.01-999999.99"
    
  数据类型验证:
    - 订单号: "字符串类型"
    - 商品名称: "字符串类型"
    - 数量: "整数类型"
    - 单价: "浮点数类型，保留2位小数"
    - 订单日期: "日期类型，格式YYYY-MM-DD"
    
  业务规则验证:
    - 订单号唯一性: "同一客户下订单号必须唯一"
    - 商品编码存在性: "商品编码必须在商品主数据中存在"
    - 客户有效性: "客户代码必须在客户主数据中存在"
    - 日期有效性: "订单日期不能超过当前日期"
```

### 8.2 计算验证规则

```yaml
计算验证:
  小计验证:
    公式: "小计 = 数量 × 单价"
    精度: "保留2位小数"
    范围: "0.01 - 999999.99"
    
  合计验证:
    公式: "总金额 = Σ(小计)"
    精度: "保留2位小数"
    范围: "0.01 - 99999999.99"
    
  折扣验证:
    公式: "折扣金额 = 小计 × 折扣率"
    折扣率范围: "0.00 - 1.00"
    折扣金额范围: "0.00 - 小计金额"
```

## 输出规则

### 9.1 文件格式规则

```yaml
文件格式:
  主要格式: "Excel (.xlsx)"
  备用格式: "Excel (.xls)"
  导出格式: "PDF (.pdf)"
  
  Excel配置:
    版本: "Excel 2016及以上"
    编码: "UTF-8"
    工作表数: "1个主工作表"
    最大行数: "1,048,576"
    最大列数: "16,384"
    
  PDF配置:
    页面大小: "A4"
    方向: "纵向"
    边距: "上下左右各2cm"
    字体嵌入: true
```

### 9.2 文件保存规则

```yaml
文件保存:
  保存路径: "C:/Users/<USER>/Desktop/客户账单/{客户代码}/{年月}/"
  默认路径: "C:/Users/<USER>/Desktop/客户账单/{客户代码}/{年月}/"
  路径配置:
    - Windows桌面: "%USERPROFILE%/Desktop/客户账单/{客户代码}/{年月}/"
    - 环境变量: "DESKTOP_PATH"
    - 备用路径: "./output/{客户代码}/{年月}/"
  
  目录结构:
    - 根目录: "客户账单"
    - 客户目录: "{客户代码}_{客户名称}"
    - 年月目录: "{YYYY年MM月}"
    - 文件命名: "{客户名称}_{账单类型}_{年月}_{序号}.xlsx"
  
  文件权限: "644"
  备份策略: "保留最近30天的文件"
  压缩策略: "文件大小超过10MB时自动压缩"
  
  命名冲突处理:
    - 策略: "自动重命名"
    - 格式: "{原文件名}_{序号}.xlsx"
    - 最大序号: 999
  
  路径检测:
    - 桌面路径检测: "自动检测Windows桌面路径"
    - 权限检查: "检查桌面目录写入权限"
    - 目录创建: "自动创建不存在的目录结构"
```

## 异常处理规则

### 10.1 数据异常处理

```yaml
数据异常处理:
  数据缺失:
    - 必填字段缺失: "记录错误日志，跳过该记录"
    - 可选字段缺失: "使用默认值填充"
    - 默认值配置:
      商品名称: "未知商品"
      数量: 1
      单价: 0.00
      折扣率: 0.00
    
  数据格式错误:
    - 数字格式错误: "尝试转换，失败则使用0"
    - 日期格式错误: "使用当前日期"
    - 字符串过长: "截取到最大长度"
    
  数据范围错误:
    - 数量超出范围: "设置为最大值或最小值"
    - 单价超出范围: "设置为最大值或最小值"
    - 日期超出范围: "使用当前日期"
```


