# OMS接口数据获取规则文档

## 接口概述

### 1.1 接口基本信息
- **接口名称**：获取账单数据接口
- **接口路径**：`/interface/getBillAccountOrderData`
- **请求方式**：POST
- **接口描述**：根据客户编号和账单月份获取客户的详细账单数据

### 1.2 接口用途
- 获取客户指定月份的账单统计信息
- 提供账单明细数据用于Excel生成
- 支持多种业务模式的账单数据获取

## 请求参数

### 2.1 请求参数结构

```json
{
  "exportBillAccountTemplateId": 1,
  "customerNo": "CUST001",
  "currentBillMonth": "2024-12",
  "shroffAccountId": 1001
}
```

### 2.2 参数详细说明

| 参数名 | 类型 | 必填 | 说明 | 示例值 |
|--------|------|------|------|--------|
| exportBillAccountTemplateId | Integer | 是 | 模板ID，用于指定账单模板 | 1 |
| customerNo | String | 是 | 客户编号，客户的唯一标识 | "CUST001" |
| currentBillMonth | Date | 是 | 当前月份，格式：yyyy-MM | "2024-12" |
| shroffAccountId | Integer | 否 | 收款账户ID | 1001 |

### 2.3 参数验证规则

```yaml
参数验证:
  exportBillAccountTemplateId:
    - 类型: "整数"
    - 范围: "1-999999"
    - 必填: true
    - 错误提示: "模板ID必须为正整数"
    
  customerNo:
    - 类型: "字符串"
    - 长度: "3-50字符"
    - 格式: "字母数字组合"
    - 必填: true
    - 错误提示: "客户编号格式不正确"
    
  currentBillMonth:
    - 类型: "日期"
    - 格式: "yyyy-MM"
    - 范围: "2020-01 至 当前年月+1"
    - 必填: true
    - 错误提示: "账单月份格式不正确或超出范围"
    
  shroffAccountId:
    - 类型: "整数"
    - 范围: "1-999999"
    - 必填: false
    - 默认值: null
    - 错误提示: "收款账户ID必须为正整数"
```

## 响应数据结构

# BillPeriodStatementStatistics 接口返回值说明

## 概述
BillPeriodStatementStatistics 是账单期结算统计的核心数据结构，用于返回客户的账单统计信息，包含客户基本信息、账户信息、收款账户信息以及各类账单明细数据。

## 主要字段说明

### 基础信息字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| currentBillMonth | Date | 当前账单月 - 统计的账单月份 |
| customerId | Integer | 客户ID - 客户的唯一标识 |
| customerNo | String | 客户编号 - 客户的业务编号 |
| customerName | String | 客户名称 - 客户的名称 |
| billDateOfPayment | Date | 客户出帐日 - 客户的账单出账日期 |

### 收款账户信息

#### CollectionAccount 收款账户
| 字段名 | 类型 | 说明 |
|--------|------|------|
| accountName | String | 账户名 - 收款账户的账户名称 |
| accountBank | String | 开户银行 - 收款账户的开户银行名称 |
| accountNo | String | 账号 - 收款账户的银行账号 |

#### CustomerAccount 客户账户
| 字段名 | 类型 | 说明 |
|--------|------|------|
| customerBalanceAmount | BigDecimal | 账户余额 - 客户当前可用余额 |
| totalUnPaidAmount | BigDecimal | 累计未付 - 客户累计未支付金额 |
| totalNeedPayAmount | BigDecimal | 尚未需支付 - 累计未付减去账户余额，小于0时返回0 |

### 账单明细列表

#### BillPeriodStatement 账单期结算
| 字段名 | 类型 | 说明 |
|--------|------|------|
| businessMode | Integer | 账单业务模型 - 业务模式标识 |
| billType | BusinessBillType | 账单类型 - 账单类型枚举 |
| billAmount | BigDecimal | 应付金额（总金额） - 账单总应付金额 |
| paidAmount | BigDecimal | 已付金额 - 已支付金额 |
| unPaidAmount | BigDecimal | 未付金额 - 未支付金额 |
| newEquipmentAmount | BigDecimal | 新增设备金额 - 新增设备的费用金额 |
| oldEquipmentAmount | BigDecimal | 往期设备金额 - 往期设备的费用金额 |
| returnEquipmentAmount | BigDecimal | 退回设备金额 - 退回设备的费用金额 |
| otherAmount | BigDecimal | 其他费用 - 其他杂项费用 |
| adjustmentEquipmentAmount | BigDecimal | 调整项金额 - 订单审核时间大于出账日的调整项金额 |
| itServiceAmount | BigDecimal | 服务金额 - IT服务相关费用 |
| depositEquipmentAmount | BigDecimal | 押金金额 - 设备押金金额 |
| couponAmount | BigDecimal | 本次账单优惠总金额 - 优惠券等优惠金额 |

#### 设备统计信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| periodStartEquipmentCount | Integer | 期初数量 - 期初设备数量 |
| returnEquipmentCount | Integer | 退货数量 - 退货设备数量 |
| rentingEquipmentCount | Integer | 在租数量 - 当前在租设备数量 |

#### 账单明细列表
| 字段名 | 类型 | 说明 |
|--------|------|------|
| excelBillPeriodDetailList | List<BillPeriodStatementDetail> | 完整导出excel使用 - 用于Excel导出的完整明细 |

#### 设备明细信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| accountOrderMonthEquipmentDetailList | List<AccountOrderMonthEquipmentDetail> | 租赁设备明细 - 租赁设备的详细清单 |

### 计费明细统计

| 字段名 | 类型 | 说明 |
|--------|------|------|
| billingDetailsStatistics | BillPeriodStatementStatistics | 计费明细数据 - 递归结构，包含计费明细的统计信息 |

### 收款账户信息（新）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| shroffAccount | CollectionAccount | 收款账户信息（新） - 新的收款账户信息结构 |

## 嵌套对象详细说明

### BillPeriodStatementDetail 账单期结算明细

#### 基础业务信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| businessOrderType | Integer | 业务单类型 - 业务单据类型：1-短租账单，2-长租账单，3-销售账单，4-服务账单 |
| businessOrderNo | String | 业务单编号 - 业务单据编号 |
| businessOrderId | Integer | 业务单ID - 业务单据ID |
| rentStartTime | Date | 起租时间 - 租赁开始时间 |
| productName | String | 商品名称 - 商品名称 |
| categoryName | String | 类别 - 商品类别 |
| productSkuName | String | 配置 - 商品SKU配置 |
| description | String | 详情 - 详细说明 |
| unitAmount | BigDecimal | 单价（元/台） - 商品单价 |
| couponUnitAmount | BigDecimal | 优惠单价（元/台） - 优惠后的单价 |
| count | Integer | 数量 - 商品数量 |

#### 时间信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| billExpectPayTime | Date | 预计支付时间(大期) - 大期预计支付时间 |
| periodStartTime | Date | 本期开始日 - 本期开始时间 |
| periodEndTime | Date | 本期结束日 - 本期结束时间 |
| statementStartTime | Date | 小期本期开始日 - 小期开始时间 |
| statementEndTime | Date | 小期本期结束日 - 小期结束时间 |

#### 期数信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| phase | Integer | 期数 - 当前期数 |
| totalPhase | Integer | 总期数 - 总期数 |
| payStatus | Integer | 支付状态 - 支付状态标识 |
| billAmount | BigDecimal | 应付金额 - 应付金额 |
| partPayAmount | BigDecimal | 部分支付金额 - 部分支付情况下的已支付金额 |

#### 订单信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| statisticsOrderType | Integer | 订单类型 - 统计订单类型 |
| associationCreateType | Integer | 关联信息 - 订单创建类型 |
| orderStatus | Integer | 订单状态 - 订单当前状态 |
| originOrderNo | String | 原订单 - 原订单编号 |
| returnTime | Date | 退租日期 - 退租时间 |
| orderSubCompanyId | Integer | 归属公司 - 订单归属公司ID |
| rentLengthType | Integer | 租赁时长类型 - 租赁时长类型 |
| sale | String | 订单业务员 |

#### 商品信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| isNew | Integer | 成色 - 1全新，0次新 |
| isReturnAnyTime | Integer | 是否即租即还 - 是否支持即租即还 |
| rentMode | Integer | 租赁方式 - 1固定租期,2即租即还,3租完即送,4分期销售,5组合租赁 |
| rentModeDesc | String | 组合租赁描述 - 租赁方式描述 |

#### 收货信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| consigneeName | String | 收货人 - 收货人姓名 |
| provinceName | String | 省 - 省份名称 |
| cityName | String | 市 - 城市名称 |
| districtName | String | 区 - 区县名称 |
| address | String | 详细地址 - 详细地址 |
| customerSubName | String | 分子公司 - 客户子公司名称 |
| subsidiaryId | Integer | 分子公司ID - 子公司ID |

#### 发货信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| deliveryMode | Integer | 发货方式 - 1快递，2自提，3凌雄配送 |
| deliverySubCompanyId | Integer | 发货所属分公司 - 发货分公司ID |

#### 支付信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| paidAmount | BigDecimal | 已付金额 - 已支付金额 |
| returnAmount | BigDecimal | 已退金额 - 已退还金额 |
| payInfo | String | 支付信息说明 - 支付相关信息说明 |
| payInfoGroupUuid | String | 支付信息单元格分组ID - 支付信息分组标识 |
| isPayInfoMerge | boolean | 支付信息单元格是否需要合并 - 是否需要合并显示 |
| isFirstPayInfoMergeRecord | boolean | 是否支付信息单元格合并的第一条 - 是否为合并记录的第一条 |
| payInfoMergeRowCount | Integer | 支付信息单元格合并行数 - 合并的行数 |

#### 优惠和冲正信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| couponAmount | BigDecimal | 优惠金额 - 优惠券等优惠金额 |
| couponInfo | String | 优惠说明 - 优惠相关信息说明 |
| discountedAmount | BigDecimal | 优惠金额（防污染） - 防止污染couponAmount字段 |
| correctAmount | BigDecimal | 冲正金额 - 不包含优惠金额的冲正金额 |
| hasCorrectAmount | boolean | 是否存在冲正 - 是否存在冲正记录 |

#### 账单结算信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| billStatementAmount | BigDecimal | 账单结算金额 - 结算原金额 |
| billReturnStatementAmount | BigDecimal | 账单退租金额 - 关联负项金额 |

#### 设备序列号信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| serialNumberSet | Set<String> | 设备序列号集合 - 设备序列号列表 |
| statementOrderDetailNoList | List<String> | 结算明细编号 - 结算明细编号列表 |

### AccountOrderMonthEquipmentDetail 账户订单月设备明细

#### 基础信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| consigneeName | String | 收货人 - 收货人姓名 |
| provinceName | String | 省 - 省份名称 |
| cityName | String | 市 - 城市名称 |
| districtName | String | 区 - 区县名称 |
| address | String | 详细地址 - 详细地址 |
| customerSubName | String | 分子公司 - 客户子公司名称 |

#### 商品信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| brandName | String | 品牌 - 商品品牌 |
| productName | String | 商品名 - 商品名称 |
| categoryName | String | 类别 - 商品类别 |
| isNewProduct | Integer | 成色 - 1全新，0次新 |
| isReturnAnyTime | Integer | 是否即租即还 - 是否支持即租即还 |
| productSkuName | String | 配置 - 商品SKU配置 |
| productSkuPrice | BigDecimal | 设备价值 - 设备价值金额 |

#### 租赁信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| rentingProductCount | Integer | 数量 - 租赁商品数量 |
| orderNo | String | 单号 - 订单编号 |
| rentStartTime | Date | 起租日期 - 租赁开始时间 |
| returnTime | Date | 退租日期 - 退租时间 |
| expectReturnTime | Date | 预计归还日期 - 预计归还时间 |
| rentLengthType | Integer | 类型 - 租赁时长类型 |
| rentStatus | Integer | 在租状态 - 1在租，2退货 |
| rentMode | Integer | 租赁方式 - 租赁方式标识 |
| rentModeDesc | String | 租赁方式描述 - 租赁方式描述 |

#### 业务信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| orderSubCompanyId | Integer | 归属公司 - 订单归属公司ID |
| businessMode | Integer | 业务类型 - 业务模式标识 |
| orderItemId | Integer | 订单项ID - 订单项ID |
| orderItemType | Integer | 订单项类型 - 订单项类型 |
| orderId | Integer | 业务单ID - 业务单据ID |
| orderType | Integer | 订单类型 - 订单类型 |
| subsidiaryId | Integer | 分公司ID - 分公司ID |

#### 序列号信息
| 字段名 | 类型 | 说明 |
|--------|------|------|
| serialNumberSet | Set<String> | 设备序列号集合 - 设备序列号列表 |

## 数据示例

{
  "currentBillMonth": "2024-01-01T00:00:00.000Z",
  "customerId": 12345,
  "customerNo": "CUST001",
  "customerName": "测试客户",
  "billDateOfPayment": "2024-01-15T00:00:00.000Z",
  "collectionAccount": {
    "accountName": "收款账户",
    "accountBank": "中国银行",
    "accountNo": "1234567890123456789"
  },
  "customerAccount": {
    "customerBalanceAmount": 10000.00,
    "totalUnPaidAmount": 50000.00,
    "totalNeedPayAmount": 40000.00
  },
  "billPeriodStatementList": [
    {
      "businessMode": 2,
      "billType": "BUSINESS_BILL_TYPE_LONG",
      "billAmount": 50000.00,
      "paidAmount": 10000.00,
      "unPaidAmount": 40000.00,
      "newEquipmentAmount": 30000.00,
      "oldEquipmentAmount": 15000.00,
      "returnEquipmentAmount": 5000.00,
      "otherAmount": 0.00,
      "adjustmentEquipmentAmount": 0.00,
      "itServiceAmount": 0.00,
      "depositEquipmentAmount": 0.00,
      "couponAmount": 0.00,
      "periodStartEquipmentCount": 10,
      "returnEquipmentCount": 2,
      "rentingEquipmentCount": 8,
      "billPeriodDetailList": [
        {
          "businessOrderType": 1,
          "businessOrderNo": "ORDER001",
          "productName": "笔记本电脑",
          "categoryName": "办公设备",
          "productSkuName": "ThinkPad X1 Carbon",
          "unitAmount": 500.00,
          "count": 2,
          "billAmount": 1000.00,
          "payStatus": 0,
          "phase": 1,
          "totalPhase": 12
        }
      ]
    }
  ]
}
