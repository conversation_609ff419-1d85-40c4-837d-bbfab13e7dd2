# Excel列和字段对应关系及层级结构分析

## 概述

本文档基于 DefaultExportBillExcelServiceImpl.java 中的代码，分析了账单导出Excel的列结构、字段映射关系和层级关系。

- ## 层级结构

  ```
  账单标题 
  ├── 统计信息 
  │   └── 统计详细信息
  │       └── 基础信息
  │           ├── 单号
  │           ├── 起租时间
  │           ├── 类别
  │           ├── 商品名
  │           ├── 配置/详情
  │           ├── 单价（元）
  │           ├── 数量
  │           ├── 本期开始日
  │           ├── 本期结束日
  │           ├── 期数
  │           ├── 设备序列号
  │           ├── 应付金额
  │           ├── 支付状态
  │           ├── 是否续租
  │           └── 账单标识
  └── 账单扩展信息 
      ├── 应付信息 
      │   ├── 出账金额
      │   ├── 退租金额
      │   ├── 调整金额
      │   ├── 优惠金额
      │   ├── 已付金额
      │   ├── 已退押金
      │   └── 说明
      ├── 客户信息 
      │   ├── 收件人
      │   ├── 省
      │   ├── 市
      │   ├── 区
      │   ├── 详细地址
      │   └── 分子公司
      ├── 订单信息 
      │   ├── 订单状态
      │   ├── 关联信息
      │   ├── 原订单
      │   └── 退租日期
      ├── 商品信息 
      │   ├── 成色
      │   ├── 租赁方式
      │   └── 备注
      └── 使用信息
          ├── 使用人
          ├── 使用组织
          └── 使用备注
  ```

## 详细列映射关系

### 1. 账单信息

| 列名       | 字段名                             | 数据类型       | 说明           | 特殊处理                                                     |
| ---------- | ---------------------------------- | -------------- | -------------- | ------------------------------------------------------------ |
| 单号       | businessOrderNo                    | String         | 业务订单号     | 无                                                           |
| 起租时间   | rentStartTime                      | Date           | 租赁开始时间   | 无                                                           |
| 类别       | categoryName                       | String         | 商品类别名称   | 无                                                           |
| 商品名     | productName                        | String         | 商品名称       | 无                                                           |
| 配置/详情  | productSkuName                     | String         | 商品SKU名称    | 无                                                           |
| 单价（元） | unitAmount                         | BigDecimal     | 单价金额       | 有优惠券时使用couponUnitAmount，显示格式：金额/月 或 金额/天 |
| 数量       | count                              | Integer        | 商品数量       | 无                                                           |
| 本期开始日 | statementStartTime/periodStartTime | Date           | 账单期开始时间 | 根据isBillingDetailsStatistics判断使用哪个字段               |
| 本期结束日 | statementEndTime/periodEndTime     | Date           | 账单期结束时间 | 根据isBillingDetailsStatistics判断使用哪个字段，退货时使用periodEndTime |
| 期数       | phase                              | Integer        | 期数信息       | 显示格式：当前期数/总期数                                    |
| 设备序列号 | serialNumberSet                    | Set<String>    | 设备序列号集合 | 用逗号分隔显示，仅在非通用账单模板时显示                     |
| 应付金额   | billAmount                         | BigDecimal     | 应付金额       | 支持合并单元格显示                                           |
| 支付状态   | payStatus                          | Integer        | 支付状态       | 支持合并单元格，有特殊样式处理。 值所对应的描述：0-未支付;4-部分支付;8-已支付;16-无需支付;20-无需支付
| 是否续租   | isRelet                            | Integer        | 是否续租标识   | 显示为"是"或"否"                                             |
| 账单标识   | billPeriodFlag                     | BillPeriodFlag | 账单期标识     | 根据不同类型显示不同文本和颜色；需要补充基础数据                          |

### 2. 应付信息 

| 列名     | 字段名                    | 数据类型   | 说明         | 特殊处理                   |
| -------- | ------------------------- | ---------- | ------------ | -------------------------- |
| 出账金额 | billStatementAmount       | BigDecimal | 出账金额     | 支持合并单元格             |
| 退租金额 | billReturnStatementAmount | BigDecimal | 退租金额     | 支持合并单元格             |
| 调整金额 | correctAmount             | BigDecimal | 调整金额     | 支持合并单元格，显示为负数 |
| 优惠金额 | discountedAmount          | BigDecimal | 优惠金额     | 支持合并单元格，显示为负数 |
| 已付金额 | paidAmount                | BigDecimal | 已付金额     | 支持合并单元格             |
| 已退押金 | returnAmount              | BigDecimal | 已退押金金额 | 支持合并单元格             |
| 说明     | payInfo                   | String     | 支付说明信息 | 支持合并单元格             |

### 3. 客户信息

| 列名     | 字段名          | 数据类型 | 说明             | 特殊处理 |
| -------- | --------------- | -------- | ---------------- | -------- |
| 收件人   | consigneeName   | String   | 收件人姓名       | 无       |
| 省       | provinceName    | String   | 省份名称         | 无       |
| 市       | cityName        | String   | 城市名称         | 无       |
| 区       | districtName    | String   | 区县名称         | 无       |
| 详细地址 | address         | String   | 详细地址         | 无       |
| 分子公司 | customerSubName | String   | 客户所属分子公司 | 需要补充基础数据       |

### 4. 订单信息

| 列名     | 字段名                | 数据类型 | 说明         | 特殊处理                 |
| -------- | --------------------- | -------- | ------------ | ------------------------ |
| 订单状态 | orderStatus           | Integer  | 订单状态     | 值所对应的描述：0-待提交;4-审核中;5-待备货;6-备货中;8-待发货;12-处理中;16-已发货;18-已签收;20-租赁中;22-部分退还;24-全部归还;28-取消;32-结束 |
| 关联信息 | associationCreateType | Integer  | 关联创建类型 | 值所对应的描述：1-ERP创建;2-商城创建;3-再来一单;4-样机转租赁;5-客需换货;6-客户更换抬头;7-赔偿买断销售;8-维修换货;9-租完即送到期销售;10-PMC拆单;11-ERP退货单;12-退租拆单;13-设备转移;14-异常检测;15-PMC拆单(系统);16-资产处置申请单 |
| 原订单   | originOrderNo         | String   | 原订单号     | associationCreateType 属于 (1,4,5,7,9,11,14)其中之一时才需要显示原订单号，否则显示空字符串 |
| 退租日期 | returnTime            | Date     | 退租日期     | 无                       |

### 5. 商品信息 

| 列名     | 字段名        | 数据类型 | 说明         | 特殊处理                                            |
| -------- | ------------- | -------- | ------------ | --------------------------------------------------- |
| 成色     | isNew         | Integer  | 商品成色     | 显示为"全新"或"次新"                                |
| 租赁方式 | rentModeDesc  | String   | 租赁方式描述 | 优先使用rentModeDesc描述字段，如果为空则根据rentMode获取对应的描述，rentMode的枚举描述对应关系(1-固定租期;2-即租即还;3-租完即送;4-分期销售;5-组合租赁;其余枚举值对应-其他) |
| 备注     | rentSceneMark | String   | 租赁场景备注 | 需要补充基础数据                    |

### 6. 使用信息

| 列名     | 字段名               | 数据类型 | 说明         | 特殊处理 |
| -------- | -------------------- | -------- | ------------ | -------- |
| 使用人   | customerUser         | String   | 客户使用人   | 无       |
| 使用组织 | customerOrganization | String   | 客户使用组织 | 无       |
| 使用备注 | customerRemark       | String   | 客户使用备注 | 无       |



## 特殊处理说明

### 1. 合并单元格处理

- **订单项合并**：isOrderItemMerge() 为true时，支持跨行合并
- **支付信息合并**：isPayInfoMerge() 为true时，支持跨行合并

### 2. 样式处理

- **支付状态**：根据状态值设置不同背景色
- **账单标识**：根据不同类型设置不同背景色
  - 新增/押金：浅绿色
  - 退货：浅黄色  
  - 调整：珊瑚色
  - 其他：默认样式

### 3. 条件显示

- **设备序列号**：仅在非通用账单模板时显示
- **本期时间字段**：根据isBillingDetailsStatistics参数决定使用哪个字段
- **关联信息**：仅显示特定类型的关联信息

### 4. 数据转换

- **金额字段**：支持负数显示（调整金额、优惠金额）
- **状态字段**：通过枚举类转换为可读文本
- **时间字段**：格式化为 yyyy/MM/dd 格式

### 5. 标题显示
1. 所有标题都是居中显示
2. 需要按照层级结构，进行合并相关的单元格

## 注意事项

1. 列的顺序必须按照父级层级顺序添加
2. 合并单元格需要特殊处理，避免重复写入
3. 样式处理需要考虑不同状态的颜色区分
4. 动态查询（如租赁场景备注）可能影响性能
5. 条件显示逻辑需要与业务规则保持一致 