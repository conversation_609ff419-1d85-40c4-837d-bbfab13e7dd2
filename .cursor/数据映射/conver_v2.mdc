账单数据处理逻辑与字段映射关系分析

## 一、整体数据流程

### 1. 数据获取流程
```
客户编号 + 账单月份 → 调用API获取账单数据 → 解析JSON数据 → 按账单类型拆分 → 写入Excel工作表
```

### 2. 账单类型分类
支持的账单类型（SUPPORTED_BILL_TYPES）：
- `BUSINESS_BILL_TYPE_LONG` - 长租账单
- `BUSINESS_BILL_TYPE_SHORT` - 短租账单
- `BUSINESS_BILL_TYPE_SALE` - 销售账单
- `BUSINESS_BILL_TYPE_IT` - IT服务账单
- `BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL` - 设备明细

### 3. 工作表创建逻辑
- **账单总览工作表**：始终创建（如果有数据）
- **明细工作表**：根据账单类型分别创建
- **设备明细工作表**：特殊处理，使用专门的EquipmentDetailExcelWriter

## 二、数据拆分逻辑

### 1. 按账单类型拆分（split_bill_data_by_type）
```python
# 遍历bill_data.billPeriodStatementList
for statement in bill_data.billPeriodStatementList:
    bill_type = statement.billType
    if bill_type in SUPPORTED_BILL_TYPES:
        split_data[bill_type]['statements'].append(statement)
        # 累计金额统计
        split_data[bill_type]['total_bill_amount'] += statement.billAmount
        split_data[bill_type]['total_paid_amount'] += statement.paidAmount
        split_data[bill_type]['total_unpaid_amount'] += statement.unPaidAmount
```

### 2. 数据源优先级
```python
# 优先使用Excel明细列表
details = statement.excelBillPeriodDetailList if statement.excelBillPeriodDetailList else statement.billPeriodDetailList
```

## 三、字段映射关系

### 1. 标准账单工作表字段映射（38列）

#### 账单信息字段（1-15列）
| 列号 | 字段名 | 数据源 | 格式化逻辑 |
|------|--------|--------|------------|
| 1 | 单号 | detail.businessOrderNo | 直接显示 |
| 2 | 起租时间 | detail.rentStartTime | 日期格式：YYYY/MM/DD |
| 3 | 类别 | detail.categoryName | 直接显示 |
| 4 | 商品名 | detail.productName | 直接显示 |
| 5 | 配置/详情 | detail.productSkuName | 直接显示 |
| 6 | 单价（元） | detail.unitAmount/detail.couponUnitAmount | 优先使用优惠单价，添加"/月"或"/天"后缀 |
| 7 | 数量 | detail.count | 直接显示 |
| 8 | 本期开始日 | detail.periodStartTime | 日期格式：YYYY/MM/DD |
| 9 | 本期结束日 | detail.periodEndTime | 日期格式：YYYY/MM/DD |
| 10 | 期数 | detail.phase/detail.totalPhase | 格式：phase/totalPhase（仅当两者都不为空时显示） |
| 11 | 设备序列号 | detail.serialNumberSet | 逗号分隔的字符串 |
| 12 | 应付金额 | detail.billAmount | 数值格式，用于Excel求和 |
| 13 | 支付状态 | detail.payStatus | 通过PAY_STATUS_MAPPING映射 |
| 14 | 是否续租 | detail.isRelet | 1→"是"，其他→"否" |
| 15 | 账单标识 | detail.billPeriodFlag | 通过format_bill_period_flag复杂逻辑处理 |

#### 应付信息字段（16-22列）
| 列号 | 字段名 | 数据源 | 特殊处理 |
|------|--------|--------|----------|
| 16 | 出账金额 | detail.billStatementAmount | 数值格式 |
| 17 | 退租金额 | detail.billReturnStatementAmount | 数值格式 |
| 18 | 调整金额 | detail.correctAmount | 数值格式，取负值 |
| 19 | 优惠金额 | detail.couponAmount | 数值格式，取负值 |
| 20 | 已付金额 | detail.paidAmount | 数值格式 |
| 21 | 已退押金 | detail.returnAmount | 数值格式 |
| 22 | 说明 | detail.payInfo | 直接显示，空值显示空字符串 |

#### 客户信息字段（23-28列）
| 列号 | 字段名 | 数据源 | 空值处理 |
|------|--------|--------|----------|
| 23 | 收件人 | detail.consigneeName | 空值显示空字符串 |
| 24 | 省 | detail.provinceName | 空值显示空字符串 |
| 25 | 市 | detail.cityName | 空值显示空字符串 |
| 26 | 区 | detail.districtName | 空值显示空字符串 |
| 27 | 详细地址 | detail.address | 空值显示空字符串 |
| 28 | 分子公司 | detail.customerSubName | 空值显示空字符串 |

#### 订单信息字段（29-32列）
| 列号 | 字段名 | 数据源 | 映射逻辑 |
|------|--------|--------|----------|
| 29 | 订单状态 | detail.orderStatus | 通过ORDER_STATUS_MAPPING映射 |
| 30 | 关联信息 | detail.associationCreateType | 通过ASSOCIATION_CREATE_TYPE_MAPPING映射 |
| 31 | 原订单 | detail.originOrderNo | 仅当关联信息不为空时显示 |
| 32 | 退租日期 | detail.returnTime | 日期格式：YYYY/MM/DD |

#### 商品信息字段（33-35列）
| 列号 | 字段名 | 数据源 | 映射逻辑 |
|------|--------|--------|----------|
| 33 | 成色 | detail.isNew | 1→"全新"，其他→"次新" |
| 34 | 租赁方式 | detail.rentMode/detail.rentModeDesc | 优先使用rentModeDesc，否则通过RENT_MODE_MAPPING映射 |
| 35 | 备注 | detail.rentSceneMark | 空值显示空字符串 |

#### 使用信息字段（36-38列）
| 列号 | 字段名 | 数据源 | 空值处理 |
|------|--------|--------|----------|
| 36 | 使用人 | detail.customerUser | 空值显示空字符串 |
| 37 | 使用组织 | detail.customerOrganization | 空值显示空字符串 |
| 38 | 使用备注 | detail.customerRemark | 空值显示空字符串 |

### 2. IT服务账单工作表字段映射（23列）

#### 账单信息字段（1-10列）
| 列号 | 字段名 | 数据源 | 格式化逻辑 |
|------|--------|--------|------------|
| 1 | 单号 | detail.businessOrderNo | 直接显示 |
| 2 | 日期 | detail.rentStartTime | 日期格式：YYYY/MM/DD |
| 3 | 名称 | detail.productName | 直接显示 |
| 4 | 类别 | detail.categoryName | 直接显示 |
| 5 | 单价(元) | detail.unitAmount/detail.couponUnitAmount | 优先使用优惠单价，添加后缀 |
| 6 | 数量 | detail.count | 直接显示 |
| 7 | 应付金额 | detail.billAmount | 数值格式 |
| 8 | 支付状态 | detail.payStatus | 通过PAY_STATUS_MAPPING映射 |
| 9 | 是否续租 | detail.isRelet | 1→"是"，其他→"否" |
| 10 | 账单标识 | detail.billPeriodFlag | 通过format_bill_period_flag处理 |

#### 应付信息字段（11-17列）
| 列号 | 字段名 | 数据源 | 特殊处理 |
|------|--------|--------|----------|
| 11 | 出账金额 | detail.billStatementAmount | 数值格式 |
| 12 | 退租金额 | detail.billReturnStatementAmount | 数值格式 |
| 13 | 调整金额 | detail.correctAmount | 数值格式，取负值 |
| 14 | 优惠金额 | detail.couponAmount | 数值格式，取负值 |
| 15 | 已付金额 | detail.paidAmount | 数值格式 |
| 16 | 已退押金 | detail.returnAmount | 数值格式 |
| 17 | 说明 | detail.payInfo | 直接显示 |

#### 客户信息字段（18-23列）
| 列号 | 字段名 | 数据源 | 空值处理 |
|------|--------|--------|----------|
| 18 | 收件人 | detail.consigneeName | 空值显示空字符串 |
| 19 | 省 | detail.provinceName | 空值显示空字符串 |
| 20 | 市 | detail.cityName | 空值显示空字符串 |
| 21 | 区 | detail.districtName | 空值显示空字符串 |
| 22 | 详细地址 | detail.address | 空值显示空字符串 |
| 23 | 分子公司 | detail.customerSubName | 空值显示空字符串 |

### 3. 账单总览工作表字段映射（5列）

## 一、工作表基本信息

### 1. 工作表名称
- 工作表名称：账单总览
- 工作表位置：始终作为第一个工作表创建（如果有数据）

### 2. 创建条件
```python
# 检查是否有账单数据
has_bill_data = any(split_data[bill_type]['statements'] for bill_type in self.SUPPORTED_BILL_TYPES)
if has_bill_data:
    # 创建账单总览工作表
    worksheet = self.workbook.create_sheet(title="账单总览")
    self.write_total_view_worksheet(worksheet, bill_data, split_data)
```

## 二、数据来源与处理逻辑

### 1. 数据来源
- **主数据源**：`bill_data`对象
- **拆分数据源**：`split_data`字典（按账单类型拆分的数据）
- **客户信息**：`bill_data.customerName`
- **账单月份**：`bill_data.currentBillMonth`
- **收款账户**：`bill_data.shroffAccount`
- **客户账户**：`bill_data.customerAccount`

### 2. 数据拆分逻辑
```python
def split_bill_data_by_type(self, bill_data):
    """根据账单类型拆分数据"""
    split_data = {}

    # 初始化每个账单类型的数据
    for bill_type in self.SUPPORTED_BILL_TYPES:
        split_data[bill_type] = {
            'statements': [],
            'total_bill_amount': Decimal('0'),
            'total_paid_amount': Decimal('0'),
            'total_unpaid_amount': Decimal('0')
        }

    # 遍历账单期结算列表
    for statement in bill_data.billPeriodStatementList:
        bill_type = statement.billType

        # 只处理支持的账单类型
        if bill_type in self.SUPPORTED_BILL_TYPES:
            split_data[bill_type]['statements'].append(statement)

            # 累计金额
            split_data[bill_type]['total_bill_amount'] += statement.billAmount
            split_data[bill_type]['total_paid_amount'] += statement.paidAmount
            split_data[bill_type]['total_unpaid_amount'] += statement.unPaidAmount

    return split_data
```

## 三、字段映射关系

### 1. 公司名称行（第1行）
| 字段名 | 数据源 | 处理逻辑 |
|--------|--------|----------|
| 公司名称 | bill_data.customerName | 直接显示，空值显示"未知客户" |

### 2. 报告标题行（第3行）
| 字段名 | 数据源 | 处理逻辑 |
|--------|--------|----------|
| 报告标题 | bill_data.currentBillMonth | 格式：YYYY年MM月-账单总览 |

### 3. 主表格字段映射（第5-8行）

#### 3.1 账单类型字段（动态生成）
| 列号 | 字段名 | 数据源 | 处理逻辑 |
|------|--------|--------|----------|
| B列 | 账单类型 | 动态生成 | 根据billPeriodStatementList中实际存在的账单类型动态显示 |

**账单类型动态生成逻辑：**
```python
# 账单类型定义
bill_types = [
    ("短租账单", "BUSINESS_BILL_TYPE_SHORT"),
    ("长租账单", "BUSINESS_BILL_TYPE_LONG"),
    ("销售账单", "BUSINESS_BILL_TYPE_SALE"),
    ("IT服务账单", "BUSINESS_BILL_TYPE_IT")
]

# 只显示有数据的账单类型
for bill_type_name, bill_type_key in bill_types:
    if bill_type_key in split_data and split_data[bill_type_key]['statements']:
        # 显示该账单类型的数据
        data = split_data[bill_type_key]
        # 累计总金额
        total_bill_amount += data['total_bill_amount']
        total_paid_amount += data['total_paid_amount']
        total_unpaid_amount += data['total_unpaid_amount']
```

#### 3.2 金额字段
| 列号 | 字段名 | 数据源 | 计算逻辑 |
|------|--------|--------|----------|
| C列 | 应付金额 | split_data[bill_type]['total_bill_amount'] | 累计该类型所有statement的billAmount |
| D列 | 已付金额 | split_data[bill_type]['total_paid_amount'] | 累计该类型所有statement的paidAmount |
| E列 | 未付金额 | split_data[bill_type]['total_unpaid_amount'] | 累计该类型所有statement的unPaidAmount |

### 4. 合计行字段（第9行）
| 列号 | 字段名 | 数据源 | 计算逻辑 |
|------|--------|--------|----------|
| B列 | 合计 | 固定值 | "合计" |
| C列 | 应付金额合计 | 累计所有类型 | sum(total_bill_amount) |
| D列 | 已付金额合计 | 累计所有类型 | sum(total_paid_amount) |
| E列 | 未付金额合计 | 累计所有类型 | sum(total_unpaid_amount) |

### 5. 收款账户信息字段（第12-14行）
| 行号 | 字段名 | 数据源 | 处理逻辑 |
|------|--------|--------|----------|
| 第12行 | 户名 | bill_data.shroffAccount.accountName | 直接显示，空值显示空字符串 |
| 第13行 | 开户行 | bill_data.shroffAccount.accountBank | 直接显示，空值显示空字符串 |
| 第14行 | 账号 | bill_data.shroffAccount.accountNo | 直接显示，空值显示空字符串 |
```

### 6. 财务汇总信息字段（第18-21行）
| 行号 | 字段名 | 数据源 | 处理逻辑 |
|------|--------|--------|----------|
| 第18行 | 往期未支付 | bill_data.customerAccount.previousUnpaidAmount | 直接显示，空值显示0 |
| 第19行 | 本期未付金额 | 计算得出 | total_unpaid_amount |
| 第20行 | 累计未付金额 | 计算得出 | previousUnpaidAmount + total_unpaid_amount |
| 第21行 | 账户余额 | bill_data.customerAccount.accountBalance | 直接显示，空值显示0 |


### 7. 备注信息字段（第23-24行）
| 行号 | 字段名 | 数据源 | 处理逻辑 |
|------|--------|--------|----------|
| 第23行 | 备注1 | 固定值 | "备注:如对账单明细金额有异议的,请在收到账单3个工作日内书面回复我司异议内容或将异议发送至我司经办人邮箱;" |
| 第24行 | 备注2 | 固定值 | "否则视为贵司对账单无异议,认可我司账单内容。账单确认后请及时安排付款,回传水单谢谢!" |

## 四、数据处理逻辑

### 1. 账单类型动态生成逻辑
```python
# 账单类型定义
bill_types = [
    ("短租账单", "BUSINESS_BILL_TYPE_SHORT"),
    ("长租账单", "BUSINESS_BILL_TYPE_LONG"),
    ("销售账单", "BUSINESS_BILL_TYPE_SALE"),
    ("IT服务账单", "BUSINESS_BILL_TYPE_IT")
]

# 只显示有数据的账单类型
for bill_type_name, bill_type_key in bill_types:
    if bill_type_key in split_data and split_data[bill_type_key]['statements']:
        # 显示该账单类型的数据
        data = split_data[bill_type_key]
        # 累计总金额
        total_bill_amount += data['total_bill_amount']
        total_paid_amount += data['total_paid_amount']
        total_unpaid_amount += data['total_unpaid_amount']
```

### 2. 金额累计逻辑
```python
# 初始化总金额
total_bill_amount = Decimal('0')
total_paid_amount = Decimal('0')
total_unpaid_amount = Decimal('0')

# 累计各类型金额
for bill_type_name, bill_type_key in bill_types:
    if bill_type_key in split_data and split_data[bill_type_key]['statements']:
        data = split_data[bill_type_key]
        total_bill_amount += data['total_bill_amount']
        total_paid_amount += data['total_paid_amount']
        total_unpaid_amount += data['total_unpaid_amount']
```

### 3. 收款账户信息处理逻辑
```python
# 检查收款账户信息是否存在
if hasattr(bill_data, 'shroffAccount') and bill_data.shroffAccount:
    account_info = [
        ("户名", bill_data.shroffAccount.accountName if hasattr(bill_data.shroffAccount, 'accountName') else ''),
        ("开户行", bill_data.shroffAccount.accountBank if hasattr(bill_data.shroffAccount, 'accountBank') else ''),
        ("账号", bill_data.shroffAccount.accountNo if hasattr(bill_data.shroffAccount, 'accountNo') else '')
    ]
```

### 4. 财务汇总信息处理逻辑
```python
# 检查客户账户信息是否存在
if hasattr(bill_data, 'customerAccount') and bill_data.customerAccount:
    previous_unpaid = getattr(bill_data.customerAccount, 'previousUnpaidAmount', 0)
    financial_data = [
        ("往期未支付", previous_unpaid),
        ("本期未付金额", total_unpaid_amount),
        ("累计未付金额", previous_unpaid + total_unpaid_amount),
        ("账户余额", getattr(bill_data.customerAccount, 'accountBalance', 0))
    ]
```

### 5. 导出时间处理逻辑
```python
# 获取当前时间作为导出时间
export_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
cell.value = f"导出时间: {export_time}"
```

## 七、关键配置

### 1. 支持的账单类型
```python
SUPPORTED_BILL_TYPES = [
    "BUSINESS_BILL_TYPE_LONG",
    "BUSINESS_BILL_TYPE_SHORT",
    "BUSINESS_BILL_TYPE_SALE",
    "BUSINESS_BILL_TYPE_IT",
    "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL"
]
```

### 2. 账单类型显示名称映射
```python
bill_types = [
    ("短租账单", "BUSINESS_BILL_TYPE_SHORT"),
    ("长租账单", "BUSINESS_BILL_TYPE_LONG"),
    ("销售账单", "BUSINESS_BILL_TYPE_SALE"),
    ("IT服务账单", "BUSINESS_BILL_TYPE_IT")
]
```

### 3. 财务汇总字段映射
```python
financial_fields = [
    ("往期未支付", "previousUnpaidAmount"),
    ("本期未付金额", "currentUnpaidAmount"),
    ("累计未付金额", "totalUnpaidAmount"),
    ("账户余额", "accountBalance")
]
```

### 4. 收款账户字段映射
```python
account_fields = [
    ("户名", "accountName"),
    ("开户行", "accountBank"),
    ("账号", "accountNo")
]
```
## 四、特殊处理逻辑

### 1. 账单标识格式化（format_bill_period_flag）
```python
# 复杂的状态映射逻辑
if bill_period_flag == "BILL_PERIOD_FLAG_NEW":
    if detail.isChangeAllocation == 1:
        return "改配新增"
    elif detail.isChangeAllocation == 2:
        return "维修换货新增"
    else:
        return "新增设备"
elif bill_period_flag == "BILL_PERIOD_FLAG_RETURN":
    if detail.returnOrderCreateType == 4:
        return "改配退回"
    elif detail.returnOrderCreateType in [6, 7]:
        return "维修换货退回"
    else:
        return "退回设备"
# ... 其他状态映射
```

### 2. 支付状态格式化（format_pay_status）
```python
# 通过PAY_STATUS_MAPPING映射
PAY_STATUS_MAPPING = {
    0: "未支付",
    1: "已支付",
    2: "部分支付",
    # ... 其他状态
}
```

### 3. 合并单元格处理
- **订单信息合并**：支付状态、应付金额列根据orderItemMerge逻辑合并
- **支付信息合并**：出账金额、退租金额等7列根据payInfoMerge逻辑合并
- **主标题合并**：G1:L1（6列1行）
- **汇总信息合并**：B2:O2、B3:O3、B4:O4（14列1行）

### 4. 背景色处理
- **支付状态列**：未支付状态（0）显示橙色背景（FFCC99）
- **账单标识列**：退回设备显示黄色背景（FFFF99），新增设备显示绿色背景（CCFFCC）
- **表格标题行**：浅灰色背景（D3D3D3）
- **合计行**：浅灰色背景（D3D3D3）

### 5. 数值处理
- **金额列**：保持数值格式以便Excel求和
- **负值处理**：调整金额和优惠金额取负值显示
- **日期格式**：统一为YYYY/MM/DD格式
- **空值处理**：None值显示空字符串

## 五、数据验证和错误处理

### 1. 合并单元格写入保护
```python
def get_merge_start_cell(self, worksheet, cell):
    """获取合并区域的起始单元格，避免写入合并单元格时出错"""
    for merged_range in worksheet.merged_cells.ranges:
        if cell.coordinate in merged_range:
            return worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
    return cell
```

### 2. 数据类型转换
```python
def format_value(self, value: Any, data_type: str = "String") -> str:
    """统一的值格式化处理"""
    if value is None:
        return ""
    elif isinstance(value, datetime):
        return value.strftime("%Y/%m/%d")
    elif isinstance(value, Decimal):
        float_val = float(value)
        if float_val == int(float_val):
            return f"{int(float_val)}"
        else:
            return f"{float_val:.2f}"
    # ... 其他类型处理
```

## 六、工作表创建顺序

1. **账单总览工作表**（如果有数据）
2. **长租账单明细工作表**
3. **短租账单明细工作表**
4. **销售账单明细工作表**
5. **IT服务账单明细工作表**
6. **设备明细工作表**（特殊处理）

## 七、关键配置映射

### 1. 账单类型映射（Config.BILL_TYPE_MAPPING）
```python
BILL_TYPE_MAPPING = {
    "BUSINESS_BILL_TYPE_LONG": "长租账单明细",
    "BUSINESS_BILL_TYPE_SHORT": "短租账单明细",
    "BUSINESS_BILL_TYPE_SALE": "销售账单明细",
    "BUSINESS_BILL_TYPE_IT": "IT服务账单明细",
    "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL": "设备明细"
}
```

### 2. 详细映射常量配置

#### 2.1 支付状态映射（PAY_STATUS_MAPPING）
```python
PAY_STATUS_MAPPING = {
    0: "未支付",
    1: "部分支付",
    2: "已支付",
    3: "已退款"
}
```

#### 2.2 业务订单类型映射（BUSINESS_ORDER_TYPE_MAPPING）
```python
BUSINESS_ORDER_TYPE_MAPPING = {
    1: "租赁订单",
    2: "销售订单",
    3: "服务订单"
}
```

#### 2.3 租赁方式映射（RENT_MODE_MAPPING）
```python
RENT_MODE_MAPPING = {
    1: "固定租期",
    2: "即租即还",
    3: "租完即送",
    4: "分期销售",
    5: "组合租赁"
}
```

#### 2.4 账单类型映射（BILL_TYPE_MAPPING）
```python
BILL_TYPE_MAPPING = {
    "BUSINESS_BILL_TYPE_LONG": "长租账单明细",
    "BUSINESS_BILL_TYPE_SHORT": "短租账单明细",
    "BUSINESS_BILL_TYPE_SALE": "销售账单明细",
    "BUSINESS_BILL_TYPE_IT": "IT服务账单明细",
    "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL": "租赁设备明细"
}
```

#### 2.5 归属公司映射（ORDER_SUB_COMPANY_MAPPING）
```python
ORDER_SUB_COMPANY_MAPPING = {
    1: "总公司",
    2: "深圳分公司",
    3: "上海分公司",
    4: "北京分公司",
    5: "广州分公司",
    6: "南京分公司",
    7: "厦门分公司",
    8: "武汉分公司",
    9: "成都分公司",
    10: "电销",
    11: "渠道大客户",
    12: "战略客户中心",
    13: "杭州分公司",
    14: "业务发展部",
    15: "用户代办池",
    16: "小熊优服",
    17: "平台营销部",
    18: "文印业务中心",
    19: "杭州战区",
    20: "元建科技",
    21: "凌雄美邦公司",
    22: "凌雄优企分公司"
}
```

#### 2.6 租赁时长类型映射（RENT_LENGTH_TYPE_MAPPING）
```python
RENT_LENGTH_TYPE_MAPPING = {
    1: "短租订单",
    2: "长租订单",
    3: "样机订单",
    4: "销售订单",
    5: "租完即送-分期付款",
    6: "租完即送-一次性付款",
    7: "销售订单（分期）",
    8: "费用单",
    9: "IT服务订单",
    10: "资产处置订单",
    11: "资产报废订单"
}
```

#### 2.7 租赁状态映射（RENT_STATUS_MAPPING）
```python
RENT_STATUS_MAPPING = {
    1: "在租",
    2: "退货"
}
```

#### 2.8 订单状态映射（ORDER_STATUS_MAPPING）
```python
# 在field_mapping.py中定义
ORDER_STATUS_MAPPING = {
    0: "待确认",
    1: "已确认",
    2: "已发货",
    3: "已签收",
    4: "已完成",
    5: "已取消",
    6: "已退款",
    7: "部分退款",
    8: "未支付",
    9: "部分支付",
    10: "已支付",
    11: "已逾期",
    12: "已续租",
    13: "已退租",
    14: "已改配",
    15: "已维修",
    16: "已换货",
    17: "已报废",
    18: "已处置",
    19: "已转移",
    20: "已调整"
}
```

#### 2.9 关联创建类型映射（ASSOCIATION_CREATE_TYPE_MAPPING）
```python
# 在field_mapping.py中定义
ASSOCIATION_CREATE_TYPE_MAPPING = {
    1: "续租",
    2: "改配",
    3: "维修",
    4: "换货",
    5: "转移",
    6: "报废",
    7: "处置",
    8: "调整",
    9: "退款",
    10: "补发",
    11: "重发",
    12: "换新",
    13: "升级",
    14: "降级",
    15: "替换"
}
```

#### 2.10 租赁场景映射（RENT_SCENE_MAPPING）
```python
# 在field_mapping.py中定义
RENT_SCENE_MAPPING = {
    1: "办公设备",
    2: "会议设备",
    3: "展示设备",
    4: "测试设备",
    5: "培训设备",
    6: "临时设备",
    7: "备用设备",
    8: "特殊设备",
    9: "定制设备",
    10: "标准设备"
}
```

#### 2.11 账单标识映射（BILL_PERIOD_FLAG_MAPPING）
```python
# 在field_mapping.py中定义
BILL_PERIOD_FLAG_MAPPING = {
    "BILL_PERIOD_FLAG_NEW": "新增设备",
    "BILL_PERIOD_FLAG_OLD": "往期设备",
    "BILL_PERIOD_FLAG_RETURN": "退回设备",
    "BILL_PERIOD_FLAG_ADJUST": "调整项",
    "BILL_PERIOD_FLAG_OTHER": "其他费用",
    "BILL_PERIOD_FLAG_DEPOSIT": "押金金额",
    "BILL_PERIOD_FLAG_IT_SERVICE": "服务金额"
}
```

### 3. 复杂业务逻辑映射

#### 3.1 账单标识复杂格式化逻辑（format_bill_period_flag）
```python
def format_bill_period_flag(self, detail) -> str:
    """格式化账单标志显示，根据复杂逻辑处理"""
    bill_period_flag = detail.billPeriodFlag if detail.billPeriodFlag else "BILL_PERIOD_FLAG_OLD"

    if bill_period_flag == "BILL_PERIOD_FLAG_NEW":
        # 新增设备，需要检查是否改配
        if hasattr(detail, 'isChangeAllocation') and detail.isChangeAllocation:
            if detail.isChangeAllocation == 1:  # COMMON_CONSTANT_YES
                return "改配新增"
            elif detail.isChangeAllocation == 2:  # COMMON_TWO
                return "维修换货新增"
            else:
                return "新增设备"
        else:
            return "新增设备"

    elif bill_period_flag == "BILL_PERIOD_FLAG_DEPOSIT":
        return "押金金额"

    elif bill_period_flag == "BILL_PERIOD_FLAG_OLD":
        return "往期设备"

    elif bill_period_flag == "BILL_PERIOD_FLAG_RETURN":
        # 退货，需要检查退货单创建类型
        if hasattr(detail, 'returnOrderCreateType') and detail.returnOrderCreateType:
            if detail.returnOrderCreateType == 4:  # RETURN_ORDER_SOURCE_CHANGE_ALLOCATION
                return "改配退回"
            elif (detail.returnOrderCreateType == 6 or  # RETURN_ORDER_SOURCE_REPAIR_REPLACE1
                  detail.returnOrderCreateType == 7):   # RETURN_ORDER_SOURCE_REPAIR_REPLACE2
                return "维修换货退回"
            else:
                return "退回设备"
        else:
            return "退回设备"

    elif bill_period_flag == "BILL_PERIOD_FLAG_ADJUST":
        return "调整项"

    elif bill_period_flag == "BILL_PERIOD_FLAG_OTHER":
        return "其他费用"

    elif bill_period_flag == "BILL_PERIOD_FLAG_IT_SERVICE":
        return "服务金额"

    else:
        return "往期设备"  # 默认值
```

#### 3.2 支付状态特殊处理逻辑
```python
# 特殊处理支付状态列
if col == 13:  # 支付状态列
    pay_status_value = detail.payStatus
    # 状态值转换逻辑
    if pay_status_value == 0:  # STATEMENT_ORDER_STATUS_INIT
        target_cell.fill = PatternFill(start_color='FFCC99', end_color='FFCC99', fill_type='solid')
    else:
        target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')

    # 状态值转换
    if pay_status_value == 20:  # STATEMENT_ORDER_STATUS_CORRECTED
        pay_status_value = 8  # STATEMENT_ORDER_STATUS_NO

    # 获取状态描述
    status_text = self.format_pay_status(pay_status_value)
    target_cell.value = status_text
```

#### 3.3 单价处理逻辑
```python
# 处理单价逻辑：优先使用优惠单价，如果没有则使用原单价
unit_amount = detail.unitAmount
if detail.couponUnitAmount and detail.couponUnitAmount >= 0:
    unit_amount = detail.couponUnitAmount
if unit_amount is None:
    unit_amount = 0

# 根据租赁类型添加后缀
rent_type_suffix = "/月" if hasattr(detail, 'rentType') and detail.rentType == 2 else "/天"
unit_amount_display = f"{unit_amount}{rent_type_suffix}"
```

#### 3.4 期数处理逻辑
```python
# 处理期数逻辑：只有当phase和totalPhase都不为空时才显示
phase_display = ""
if detail.phase is not None and detail.totalPhase is not None:
    phase_display = f"{detail.phase}/{detail.totalPhase}"
```

#### 3.5 序列号处理逻辑
```python
# 设备序列号处理
', '.join(str(item) for item in detail.serialNumberSet if item is not None) if detail.serialNumberSet else ""
```

#### 3.6 成色处理逻辑
```python
# 成色处理
is_new = "全新" if detail.isNew == 1 else "次新"
```

#### 3.7 租赁方式处理逻辑
```python
# 租赁方式处理：优先使用rentModeDesc，否则通过RENT_MODE_MAPPING映射
rent_mode = detail.rentModeDesc or RENT_MODE_MAPPING.get(detail.rentMode, '其他')
```

#### 3.8 续租状态处理逻辑
```python
# 是否续租处理
"是" if detail.isRelet == 1 else "否"
```

#### 3.9 原订单号条件显示逻辑
```python
# 如果关联信息为空，则原订单号也为空
origin_order_no = detail.originOrderNo if assoc_type else ''
```

### 4. 数值处理特殊逻辑

#### 4.1 金额列负值处理
```python
# 定义需要取负值的列（优惠金额和调整金额）
negate_columns = [18, 19]  # 调整金额(18)、优惠金额(19)

# 对于需要取负值的列，进行特殊处理
if col in negate_columns:
    # 取绝对值后取负值
    abs_value = abs(float(value))
    target_cell.value = -abs_value
```

#### 4.2 金额列数值格式保持
```python
# 特殊处理金额列（保持数值格式以便Excel求和）
amount_columns = [12, 16, 17, 18, 19, 20, 21]  # 应付金额、出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金
if col in amount_columns:
    if isinstance(value, (int, float, Decimal)):
        target_cell.value = float(value)
    else:
        target_cell.value = value
```

### 5. 背景色处理逻辑

#### 5.1 支付状态背景色
```python
# 特殊处理支付状态列的背景色
if col == 13:  # 支付状态列
    pay_status_value = detail.payStatus
    if pay_status_value == 0:  # STATEMENT_ORDER_STATUS_INIT
        target_cell.fill = PatternFill(start_color='FFCC99', end_color='FFCC99', fill_type='solid')
    else:
        target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
```

#### 5.2 账单标识背景色
```python
# 特殊处理账单标志的背景色（只对账单标志列）
if col == 15:
    if detail.billPeriodFlag == "BILL_PERIOD_FLAG_RETURN":
        target_cell.fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')
    elif detail.billPeriodFlag == "BILL_PERIOD_FLAG_NEW":
        target_cell.fill = PatternFill(start_color='CCFFCC', end_color='CCFFCC', fill_type='solid')
```

### 6. 合并单元格处理逻辑

#### 6.1 订单信息合并
```python
# 定义订单信息合并的列（支付状态、应付金额）
order_merge_columns = [12, 13]  # 应付金额、支付状态

# 处理订单信息合并逻辑（支付状态、应付金额）
if hasattr(detail, 'orderItemMerge') and detail.orderItemMerge:
    if hasattr(detail, 'firstOrderItemMergeRecord') and detail.firstOrderItemMergeRecord:
        # 这是订单信息合并组的第一行，记录合并信息
        merge_row_count = getattr(detail, 'orderItemMergeRowCount', 1)
        merge_info[current_row] = merge_row_count

        # 对订单信息相关列进行合并
        for col in order_merge_columns:
            if col <= len(all_data):
                start_row = current_row
                end_row = current_row + merge_row_count - 1

                # 先取消可能存在的合并
                self.unmerge_cells_if_exists(worksheet, col, start_row, end_row)

                # 然后合并单元格
                worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
```

#### 6.2 支付信息合并
```python
# 定义支付信息相关列合并的列
pay_info_merge_columns = [16, 17, 18, 19, 20, 21, 22]  # 出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金、说明

# 处理支付信息相关列合并逻辑
if hasattr(detail, 'payInfoMerge') and detail.payInfoMerge:
    if hasattr(detail, 'firstPayInfoMergeRecord') and detail.firstPayInfoMergeRecord:
        # 这是支付信息合并组的第一行
        pay_info_merge_row_count = getattr(detail, 'payInfoMergeRowCount', 1)

        # 对支付信息相关列进行合并
        for col in pay_info_merge_columns:
            if col <= len(all_data):
                start_row = current_row
                end_row = current_row + pay_info_merge_row_count - 1

                # 先取消可能存在的合并
                self.unmerge_cells_if_exists(worksheet, col, start_row, end_row)

                # 然后合并单元格
                worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
```

### 7. 空值处理逻辑

#### 7.1 客户信息空值处理
```python
# 客户信息数据 (6列)
customer_data = [
    detail.consigneeName if detail.consigneeName else '',
    detail.provinceName if detail.provinceName else '',
    detail.cityName if detail.cityName else '',
    detail.districtName if detail.districtName else '',
    detail.address if detail.address else '',
    detail.customerSubName if detail.customerSubName else ''
]
```

#### 7.2 使用信息空值处理
```python
# 使用信息数据 (3列)
usage_data = [
    detail.customerUser if detail.customerUser else '',
    detail.customerOrganization if detail.customerOrganization else '',
    detail.customerRemark if detail.customerRemark else ''
]
```

#### 7.3 说明字段空值处理
```python
# 说明字段处理
detail.payInfo if detail.payInfo else ''
```

### 8. 错误码映射（ERROR_CODES）
```python
ERROR_CODES = {
    200: "成功",
    400: "请求参数错误",
    401: "未授权",
    403: "禁止访问",
    404: "资源不存在",
    500: "服务器内部错误",
    503: "服务不可用",
    1001: "客户编号不存在",
    1002: "账单月份格式错误",
    1003: "模板ID不存在",
    1004: "收款账户不存在",
    1005: "无账单数据",
    1006: "数据权限不足"
}
```

### 9. 日期格式配置（DATE_FORMATS）
```python
DATE_FORMATS = [
    "%Y-%m-%dT%H:%M:%S.%fZ",
    "%Y-%m-%dT%H:%M:%SZ",
    "%Y-%m-%d %H:%M:%S",
    "%Y-%m-%d"
]
```

这个数据逻辑和映射关系确保了账单数据能够准确、完整地转换为Excel格式，并保持了良好的可维护性和扩展性。所有的映射关系都经过详细梳理，包括简单的枚举映射、复杂的业务逻辑处理、数值格式化、背景色处理、合并单元格逻辑等各个方面。