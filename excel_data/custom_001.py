"""
Custom_001 处理器
基于 UnifiedMobanProcessor，专门处理模板数据填充
特殊处理：根据账单类型和账单标志分别填充不同工作表
"""

import os
import sys
from datetime import datetime

import openpyxl
import openpyxl.utils

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入父类处理器
from unified_moban_processor import UnifiedMobanProcessor


class Custom001Processor(UnifiedMobanProcessor):
    """
    继承自 UnifiedMobanProcessor 的特殊处理器
    根据账单类型和账单标志分别填充不同工作表
    """

    def __init__(self, template_path: str = None, data_path: str = None):
        """初始化处理器"""
        
        # 默认模板和数据路径
        if template_path is None:
            template_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC-1000-20220721-22557.xlsx"
        
        if data_path is None:
            # 使用最新的测试数据
            data_path = "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json"
        
        # 调用父类构造函数
        super().__init__(template_path, data_path)
        
        # 定义账单标志常量
        self.BILL_PERIOD_FLAG_NEW = "BILL_PERIOD_FLAG_NEW"  # 新增账单
        self.BILL_PERIOD_FLAG_OLD = "BILL_PERIOD_FLAG_OLD"  # 往期账单
    
    def _is_special_worksheet(self, worksheet_name: str) -> bool:
        """
        判断是否是特殊处理的工作表
        重写父类方法，定义需要特殊处理的工作表
        """
        # 定义需要特殊处理的工作表
        special_worksheets = [
            "长租账单-新增",
            "长租账单-往期"
        ]
        return worksheet_name in special_worksheets
    
    def _process_special_worksheet(self, worksheet, worksheet_name: str):
        """
        处理特殊工作表
        重写父类方法，实现特殊处理逻辑
        """
        if worksheet_name == "长租账单-新增":
            # 处理长租账单-新增工作表
            new_bills = self._filter_bills_by_type_and_flag(
                bill_type="BUSINESS_BILL_TYPE_LONG",
                bill_flag=self.BILL_PERIOD_FLAG_NEW
            )
            self._process_worksheet_with_filtered_data(worksheet, worksheet_name, new_bills, "long")
            
        elif worksheet_name == "长租账单-往期":
            # 处理长租账单-往期工作表
            old_bills = self._filter_bills_by_type_and_flag(
                bill_type="BUSINESS_BILL_TYPE_LONG",
                bill_flag=self.BILL_PERIOD_FLAG_OLD
            )
            self._process_worksheet_with_filtered_data(worksheet, worksheet_name, old_bills, "long")
            
        else:
            # 其他特殊工作表使用标准处理
            self._process_worksheet(worksheet)
    
    def _filter_bills_by_type_and_flag(self, bill_type: str, bill_flag: str):
        """
        根据账单类型和账单标志过滤数据
        数据路径: 遍历所有 billPeriodStatementList 中的 excelBillPeriodDetailList
        
        Args:
            bill_type: 账单类型 (如 'BUSINESS_BILL_TYPE_LONG')
            bill_flag: 账单标志 (如 'BILL_PERIOD_FLAG_NEW')
        
        Returns:
            list: 过滤后的账单数据列表
        """
        filtered_bills = []
        
        if not self.bill_data:
            return filtered_bills
        
        # 尝试不同的数据结构路径
        bill_period_statement_list = []
        
        # 路径1: 直接在根级别
        if 'billPeriodStatementList' in self.bill_data:
            bill_period_statement_list = self.bill_data.get('billPeriodStatementList', [])
        
        # 路径2: 在resultMap.data中
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_period_statement_list = data.get('billPeriodStatementList', [])
        
        if not bill_period_statement_list:
            return filtered_bills
        
        # 遍历所有账单周期，收集所有匹配的数据
        for statement in bill_period_statement_list:
            # 检查账单周期级别的账单类型
            if statement.get('billType') == bill_type:
                excel_bill_period_detail_list = statement.get('excelBillPeriodDetailList', [])
                
                for bill in excel_bill_period_detail_list:
                    # 检查账单标志
                    if bill.get('billPeriodFlag') == bill_flag:
                        filtered_bills.append(bill)
        
        return filtered_bills
    
    def _process_worksheet_with_filtered_data(self, worksheet, worksheet_name: str, 
                                            filtered_bills: list, bill_type_key: str = 'long'):
        """
        使用过滤后的数据填充指定工作表
        
        Args:
            worksheet: openpyxl工作表对象
            worksheet_name: 工作表名称
            filtered_bills: 过滤后的账单数据
            bill_type_key: 账单类型键名 (如 'long', 'short')
        """
        if not filtered_bills:
            print(f"⚠️  工作表 '{worksheet_name}' 没有匹配的数据")
            return
        
        print(f"📊 工作表 '{worksheet_name}' 找到 {len(filtered_bills)} 条匹配数据")
        
        # 处理工作表数据填充
        self._process_worksheet_data(worksheet, filtered_bills, bill_type_key)
    
    def _process_worksheet_data(self, worksheet, bills: list, bill_type_key: str):
        """
        处理工作表数据填充（重写父类方法）
        
        Args:
            worksheet: openpyxl工作表对象
            bills: 账单数据列表
            bill_type_key: 账单类型键名
        """
        if not bills:
            print(f"⚠️  没有数据需要填充")
            return
        
        print(f"📊 开始填充 {len(bills)} 条数据到工作表")
        
        # 查找循环模板行
        loop_templates = self._find_loop_templates(worksheet)
        
        if loop_templates:
            print(f"  找到 {len(loop_templates)} 个循环模板")
            
            # 从后往前处理，避免行号变化影响
            for template_info in reversed(loop_templates):
                self._process_loop_template_with_custom_data(worksheet, template_info, bills, bill_type_key)
        
        # 处理非循环占位符
        self._process_non_loop_placeholders(worksheet)
    
    def _process_loop_template_with_custom_data(self, worksheet, template_info: dict, bills: list, bill_type_key: str):
        """使用自定义数据处理循环模板"""
        try:
            row_num = template_info['row_num']
            list_name = template_info['list_name']
            template_data = template_info['template_data']
            
            print(f"  处理第{row_num}行循环模板: {bill_type_key}.{list_name} ({len(bills)}条数据)")
            
            if len(bills) > 0:
                print(f"  将复制样式到新增的 {len(bills)} 行")
            
            # 获取原始行高
            original_row_height = worksheet.row_dimensions[row_num].height
            
            # 删除模板行
            worksheet.delete_rows(row_num)
            
            # 为每条数据创建新行
            for i, bill_data in enumerate(bills):
                new_row_num = row_num + i
                
                # 创建带样式的循环行
                self._create_styled_loop_row_with_custom_data(
                    worksheet, new_row_num, template_data, bill_data, bill_type_key, list_name, original_row_height
                )
            
        except Exception as e:
            print(f"  ❌ 处理循环模板时出错: {e}")
    
    def _create_styled_loop_row_with_custom_data(self, worksheet, row_num: int, template_data: list, 
                                                data_item: dict, bill_type: str, list_name: str, row_height: float):
        """使用自定义数据创建带样式的循环行"""
        try:
            # 设置行高
            if row_height:
                worksheet.row_dimensions[row_num].height = row_height
            
            # 为每个单元格填充数据和样式
            for col_num, cell_info in enumerate(template_data, 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                
                # 获取原始值并替换占位符
                original_value = cell_info['value']
                new_value = self._replace_loop_placeholders(original_value, data_item, bill_type, list_name)
                
                # 设置单元格值
                cell.value = new_value
                
                # 复制样式
                if cell_info['font']:
                    cell.font = cell_info['font']
                if cell_info['border']:
                    cell.border = cell_info['border']
                if cell_info['fill']:
                    cell.fill = cell_info['fill']
                if cell_info['alignment']:
                    cell.alignment = cell_info['alignment']
                if cell_info['number_format']:
                    cell.number_format = cell_info['number_format']
                
                # 设置列宽
                if cell_info['col_width']:
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    worksheet.column_dimensions[col_letter].width = cell_info['col_width']
            
        except Exception as e:
            print(f"  ❌ 创建样式行时出错: {e}")
    
    def _get_custom_loop_data(self, bills: list, list_name: str) -> list:
        """从自定义账单数据中获取循环数据"""
        loop_data = []
        
        for bill in bills:
            # 根据list_name获取对应的数据列表
            if list_name in bill:
                loop_data.extend(bill[list_name])
            elif 'excelBillPeriodDetailList' in bill:
                # 默认使用excelBillPeriodDetailList
                loop_data.extend(bill['excelBillPeriodDetailList'])
        
        return loop_data
    
    def process_template(self, output_path: str = None) -> bool:
        """
        处理模板的主要方法（重写父类方法）
        使用新的架构：自动识别特殊工作表并使用相应的处理逻辑
        """
        try:
            # 生成默认输出路径
            if output_path is None:
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"custom_001_output_{current_time}.xlsx"
            
            # 检查文件是否存在
            if not os.path.exists(self.moban_path):
                print(f"❌ 模板文件不存在: {self.moban_path}")
                return False
            
            if not self.bill_data:
                print(f"❌ 数据文件为空")
                return False
            
            # 加载工作簿
            import openpyxl
            workbook = openpyxl.load_workbook(self.moban_path)
            
            print(f"📋 开始处理模板: {self.moban_path}")
            
            # 使用父类的统一处理方法，会自动调用子类的特殊处理逻辑
            self._process_all_worksheets(workbook)
            
            # 保存文件
            workbook.save(output_path)
            print(f"💾 保存到: {output_path}")
            
            return True
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def process_with_files(self, template_path: str, data_path: str, output_path: str = None) -> bool:
        """
        使用指定的模板和数据文件处理
        """
        try:
            # 重新初始化路径
            self.moban_path = self._find_file_path(template_path)
            self.data_path = self._find_file_path(data_path)
            
            # 重新加载数据
            self.bill_data = self._load_data()
            
            if not self.bill_data:
                return False
            
            # 重新分组数据
            self.bill_groups = self._group_bills_by_type()
            
            # 处理模板
            return self.process_template(output_path)
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def show_data_statistics(self):
        """显示数据统计信息"""
        if not self.bill_data:
            print("❌ 没有数据")
            return
        
        # 尝试不同的数据结构路径
        bill_period_statement_list = []
        
        # 路径1: 直接在根级别
        if 'billPeriodStatementList' in self.bill_data:
            bill_period_statement_list = self.bill_data.get('billPeriodStatementList', [])
        
        # 路径2: 在resultMap.data中
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_period_statement_list = data.get('billPeriodStatementList', [])
        
        if not bill_period_statement_list:
            print("❌ 没有账单周期数据")
            return
        
        # 遍历所有账单周期，统计总数据量
        total_bills = 0
        for statement in bill_period_statement_list:
            excel_bill_period_detail_list = statement.get('excelBillPeriodDetailList', [])
            total_bills += len(excel_bill_period_detail_list)
        
        new_bills = self._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", self.BILL_PERIOD_FLAG_NEW)
        old_bills = self._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", self.BILL_PERIOD_FLAG_OLD)
        
        print(f"📊 数据统计:")
        print(f"   总账单数: {total_bills}")
        print(f"   长租账单-新增: {len(new_bills)} 条")
        print(f"   长租账单-往期: {len(old_bills)} 条")


def main():
    """主函数 - 演示特殊处理功能"""
    
    print("🚀 Custom_001 特殊处理器")
    print("=" * 60)
    
    # 创建处理器
    processor = Custom001Processor()
    
    # 显示数据统计
    processor.show_data_statistics()
    
    # 处理模板
    template_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC-1000-20220721-22557.xlsx"
    data_file = "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json"
    output_file = "custom_001_special_output.xlsx"
    
    success = processor.process_with_files(template_path, data_file, output_file)
    
    if success:
        print(f"\n✅ 特殊处理成功完成!")
    else:
        print(f"\n❌ 特殊处理失败")
    
    return success


if __name__ == "__main__":
    main()
