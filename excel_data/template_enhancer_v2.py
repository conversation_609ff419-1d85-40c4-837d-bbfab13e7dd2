"""
模板占位符增强器 V2
基于工作表名称、标题列行号和数据起始行号自动生成占位符
"""

import os
import openpyxl
import xlrd
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple
from datetime import datetime


@dataclass
class WorksheetConfig:
    """工作表配置对象"""
    worksheet_name: str      # 工作表名称
    header_row_num: int      # 标题列的行号（1-based）
    data_start_row_num: int  # 需要填充数据的起始行号（1-based）
    
    def __post_init__(self):
        """验证参数"""
        if self.header_row_num < 1:
            raise ValueError(f"标题行号必须大于0，当前值: {self.header_row_num}")
        if self.data_start_row_num < 1:
            raise ValueError(f"数据起始行号必须大于0，当前值: {self.data_start_row_num}")
        if self.data_start_row_num <= self.header_row_num:
            raise ValueError(f"数据起始行号({self.data_start_row_num})必须大于标题行号({self.header_row_num})")


class TemplateEnhancerV2:
    """模板占位符增强器 V2"""
    
    def __init__(self):
        # 工作表名称到账单类型的映射
        self.worksheet_bill_type_mapping = {
            '长租': 'long', '长租账单': 'long', '长租账单明细': 'long', 
            '长租账单-往期': 'long', '长租账单-新增': 'long', '长租明细': 'long',
            '短租': 'short', '短租账单': 'short', '短租账单明细': 'short', '短租明细': 'short',
            '销售': 'sale', '销售账单': 'sale', '销售账单明细': 'sale', '销售明细': 'sale',
            'IT': 'it', 'IT服务': 'it', 'IT账单': 'it', 'IT服务账单': 'it', 'IT明细': 'it',
            '设备': 'equipment', '设备明细': 'equipment', '租赁设备明细': 'equipment',
        }
        
        # 标题列名到占位符字段的映射
        self.column_field_mapping = {
            '单号': 'businessOrderNo', '业务单编号': 'businessOrderNo',
            '起租时间': 'rentStartTime', '类别': 'categoryName',
            '商品名': 'productName', '商品名称': 'productName',
            '配置/详情': 'productSkuName', '配置': 'productSkuName', '详情': 'productSkuName',
            '单价（元）': 'unitAmount', '单价(元)': 'unitAmount', '单价': 'unitAmount',
            '数量': 'count', '本期开始日': 'periodStartTime', '本期结束日': 'periodEndTime',
            '期数': 'phase', '设备序列号': 'serialNumberSet', '序列号': 'serialNumberSet',
            '应付金额': 'billAmount', '支付状态': 'payStatus', '是否续租': 'isRelet',
            '账单标识': 'billPeriodFlag', '出账金额': 'billStatementAmount',
            '退租金额': 'billReturnStatementAmount', '调整金额': 'correctAmount',
            '优惠金额': 'couponAmount', '已付金额': 'paidAmount',
            '已退押金': 'returnAmount', '说明': 'payInfo',
            '收件人': 'consigneeName', '省': 'provinceName', '市': 'cityName',
            '区': 'districtName', '详细地址': 'address', '分子公司': 'customerSubName',
            '订单状态': 'orderStatus', '关联信息': 'associationCreateType',
            '原订单': 'originOrderNo', '退租日期': 'returnTime',
            '成色': 'isNew', '租赁方式': 'rentMode', '备注': 'rentSceneMark',
            '使用人': 'customerUser', '使用组织': 'customerOrganization',
            '使用备注': 'customerRemark', '日期': 'rentStartTime', '名称': 'productName',
        }
        
        self.default_bill_type = 'long'
        self.default_list_name = 'excelBillPeriodDetailList'
    
    def convert_xls_to_xlsx(self, xls_path: str) -> str:
        """将XLS文件转换为XLSX文件"""
        try:
            base_name = os.path.splitext(xls_path)[0]
            xlsx_path = f"{base_name}_converted.xlsx"
            
            # 读取XLS文件
            xls_workbook = xlrd.open_workbook(xls_path)
            
            # 创建XLSX工作簿
            xlsx_workbook = openpyxl.Workbook()
            xlsx_workbook.remove(xlsx_workbook.active)  # 删除默认工作表
            
            for sheet_name in xls_workbook.sheet_names():
                xls_sheet = xls_workbook.sheet_by_name(sheet_name)
                xlsx_sheet = xlsx_workbook.create_sheet(title=sheet_name)
                
                for row_idx in range(xls_sheet.nrows):
                    for col_idx in range(xls_sheet.ncols):
                        cell_value = xls_sheet.cell_value(row_idx, col_idx)
                        xlsx_sheet.cell(row=row_idx + 1, column=col_idx + 1, value=cell_value)
            
            xlsx_workbook.save(xlsx_path)
            print(f"📄 已转换XLS文件: {xls_path} -> {xlsx_path}")
            return xlsx_path
            
        except Exception as e:
            print(f"❌ XLS转换失败: {e}")
            return None
    
    def determine_bill_type(self, worksheet_name: str) -> str:
        """根据工作表名称确定账单类型"""
        worksheet_name = worksheet_name.strip()
        
        # 精确匹配
        if worksheet_name in self.worksheet_bill_type_mapping:
            return self.worksheet_bill_type_mapping[worksheet_name]
        
        # 模糊匹配
        for pattern, bill_type in self.worksheet_bill_type_mapping.items():
            if pattern in worksheet_name:
                return bill_type
        
        return self.default_bill_type
    
    def generate_placeholder(self, field_name: str, bill_type: str) -> str:
        """生成占位符"""
        return f"${{@{bill_type}.{self.default_list_name}.{field_name}}}"
    
    def analyze_header_row(self, worksheet, header_row_num: int) -> List[Tuple[int, str, str]]:
        """分析标题行，返回列号、标题名称和对应字段名的列表"""
        header_info = []
        
        for col_num in range(1, worksheet.max_column + 1):
            cell = worksheet.cell(row=header_row_num, column=col_num)
            if cell.value:
                header_text = str(cell.value).strip()
                field_name = self.column_field_mapping.get(header_text)
                if field_name:
                    header_info.append((col_num, header_text, field_name))
        
        return header_info
    
    def enhance_template(self, template_path: str, worksheet_name: str, 
                        header_row_num: int, data_start_row_num: int,
                        output_path: str = None) -> bool:
        """为指定工作表增加占位符"""
        try:
            if not os.path.exists(template_path):
                print(f"❌ 文件不存在: {template_path}")
                return False
            
            # 处理XLS文件
            working_path = template_path
            if template_path.endswith('.xls'):
                working_path = self.convert_xls_to_xlsx(template_path)
                if not working_path:
                    return False
            
            # 加载工作簿
            workbook = openpyxl.load_workbook(working_path)
            
            if worksheet_name not in workbook.sheetnames:
                print(f"❌ 工作表不存在: {worksheet_name}")
                print(f"📋 可用工作表: {workbook.sheetnames}")
                return False
            
            worksheet = workbook[worksheet_name]
            
            # 确定账单类型
            bill_type = self.determine_bill_type(worksheet_name)
            print(f"📋 工作表 '{worksheet_name}' -> 账单类型: {bill_type}")
            
            # 检查是否需要处理此工作表
            should_process = worksheet_name in self.worksheet_bill_type_mapping
            if not should_process:
                for pattern in self.worksheet_bill_type_mapping.keys():
                    if pattern in worksheet_name:
                        should_process = True
                        break
            
            if not should_process:
                print(f"⚠️  工作表没有匹配的账单类型，跳过处理")
                return True
            
            # 分析标题行
            header_info = self.analyze_header_row(worksheet, header_row_num)
            if not header_info:
                print(f"⚠️  未找到可识别的标题列")
                return True
            
            print(f"📊 识别到 {len(header_info)} 个标题列")
            
            # 在数据起始行填入占位符
            placeholder_count = 0
            for col_num, header_text, field_name in header_info:
                placeholder = self.generate_placeholder(field_name, bill_type)
                cell = worksheet.cell(row=data_start_row_num, column=col_num)
                cell.value = placeholder
                placeholder_count += 1
                print(f"   第{col_num}列 '{header_text}' -> {placeholder}")
            
            print(f"✅ 在第{data_start_row_num}行填入了 {placeholder_count} 个占位符")
            
            # 保存文件
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(template_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"{base_name}_enhanced_v2_{timestamp}.xlsx"
            
            workbook.save(output_path)
            print(f"💾 保存到: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def enhance_worksheets_with_configs(self, template_path: str, 
                                       worksheet_configs: List[WorksheetConfig],
                                       output_path: str = None) -> bool:
        """使用配置对象批量处理多个工作表"""
        try:
            if not os.path.exists(template_path):
                print(f"❌ 文件不存在: {template_path}")
                return False
            
            if not worksheet_configs:
                print(f"⚠️  没有工作表配置，跳过处理")
                return True
            
            # 处理XLS文件
            working_path = template_path
            if template_path.endswith('.xls'):
                working_path = self.convert_xls_to_xlsx(template_path)
                if not working_path:
                    return False
            
            # 加载工作簿
            workbook = openpyxl.load_workbook(working_path)
            
            print(f"📋 开始批量处理工作簿: {template_path}")
            print(f"📊 配置了 {len(worksheet_configs)} 个工作表")
            print(f"📊 模板包含 {len(workbook.sheetnames)} 个工作表: {workbook.sheetnames}")
            
            processed_count = 0
            skipped_count = 0
            total_placeholders = 0
            
            for config in worksheet_configs:
                print(f"\n--- 处理工作表配置: {config.worksheet_name} ---")
                print(f"    标题行: {config.header_row_num}, 数据起始行: {config.data_start_row_num}")
                
                # 检查工作表是否存在
                if config.worksheet_name not in workbook.sheetnames:
                    print(f"❌ 工作表不存在: {config.worksheet_name}")
                    skipped_count += 1
                    continue
                
                worksheet = workbook[config.worksheet_name]
                
                # 确定账单类型
                bill_type = self.determine_bill_type(config.worksheet_name)
                print(f"📋 账单类型: {bill_type}")
                
                # 检查是否需要处理此工作表
                should_process = config.worksheet_name in self.worksheet_bill_type_mapping
                if not should_process:
                    for pattern in self.worksheet_bill_type_mapping.keys():
                        if pattern in config.worksheet_name:
                            should_process = True
                            break
                
                if not should_process:
                    print(f"⚠️  工作表没有匹配的账单类型，跳过处理")
                    skipped_count += 1
                    continue
                
                # 分析标题行
                header_info = self.analyze_header_row(worksheet, config.header_row_num)
                if not header_info:
                    print(f"⚠️  未找到可识别的标题列，跳过")
                    skipped_count += 1
                    continue
                
                print(f"📊 识别到 {len(header_info)} 个标题列")
                
                # 在数据起始行填入占位符
                placeholder_count = 0
                for col_num, header_text, field_name in header_info:
                    placeholder = self.generate_placeholder(field_name, bill_type)
                    cell = worksheet.cell(row=config.data_start_row_num, column=col_num)
                    cell.value = placeholder
                    placeholder_count += 1
                    print(f"   第{col_num}列 '{header_text}' -> {placeholder}")
                
                print(f"✅ 在第{config.data_start_row_num}行填入了 {placeholder_count} 个占位符")
                processed_count += 1
                total_placeholders += placeholder_count
            
            # 保存文件
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(template_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"{base_name}_enhanced_batch_{timestamp}.xlsx"
            
            workbook.save(output_path)
            
            print(f"\n🎉 批量处理完成!")
            print(f"📊 处理统计:")
            print(f"   ✅ 已处理工作表: {processed_count}")
            print(f"   ⚠️  跳过工作表: {skipped_count}")
            print(f"   🎯 总占位符数: {total_placeholders}")
            print(f"💾 保存到: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 批量处理失败: {e}")
            return False
    
    def enhance_all_worksheets(self, template_path: str, header_row_num: int, 
                              data_start_row_num: int, output_path: str = None) -> bool:
        """为所有匹配的工作表增加占位符"""
        try:
            if not os.path.exists(template_path):
                print(f"❌ 文件不存在: {template_path}")
                return False
            
            # 处理XLS文件
            working_path = template_path
            if template_path.endswith('.xls'):
                working_path = self.convert_xls_to_xlsx(template_path)
                if not working_path:
                    return False
            
            # 加载工作簿
            workbook = openpyxl.load_workbook(working_path)
            
            print(f"📋 开始处理工作簿: {template_path}")
            print(f"📊 找到 {len(workbook.sheetnames)} 个工作表")
            
            processed_count = 0
            skipped_count = 0
            
            for worksheet_name in workbook.sheetnames:
                print(f"\n--- 处理工作表: {worksheet_name} ---")
                
                # 确定账单类型
                bill_type = self.determine_bill_type(worksheet_name)
                
                # 检查是否需要处理此工作表
                should_process = worksheet_name in self.worksheet_bill_type_mapping
                if not should_process:
                    for pattern in self.worksheet_bill_type_mapping.keys():
                        if pattern in worksheet_name:
                            should_process = True
                            break
                
                if not should_process:
                    print(f"⚠️  工作表没有匹配的账单类型，跳过处理")
                    skipped_count += 1
                    continue
                
                worksheet = workbook[worksheet_name]
                print(f"📋 账单类型: {bill_type}")
                
                # 分析标题行
                header_info = self.analyze_header_row(worksheet, header_row_num)
                if not header_info:
                    print(f"⚠️  未找到可识别的标题列，跳过")
                    skipped_count += 1
                    continue
                
                print(f"📊 识别到 {len(header_info)} 个标题列")
                
                # 在数据起始行填入占位符
                placeholder_count = 0
                for col_num, header_text, field_name in header_info:
                    placeholder = self.generate_placeholder(field_name, bill_type)
                    cell = worksheet.cell(row=data_start_row_num, column=col_num)
                    cell.value = placeholder
                    placeholder_count += 1
                
                print(f"✅ 在第{data_start_row_num}行填入了 {placeholder_count} 个占位符")
                processed_count += 1
            
            # 保存文件
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(template_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"{base_name}_enhanced_all_v2_{timestamp}.xlsx"
            
            workbook.save(output_path)
            
            print(f"\n🎉 处理完成!")
            print(f"📊 处理统计:")
            print(f"   ✅ 已处理工作表: {processed_count}")
            print(f"   ⚠️  跳过工作表: {skipped_count}")
            print(f"💾 保存到: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False


def main():
    """主函数"""
    enhancer = TemplateEnhancerV2()
    
    print("🚀 模板占位符增强器 V2")
    print("=" * 60)
    
    # 示例1: 使用配置对象批量处理
    print(f"\n--- 示例1: 使用配置对象批量处理 ---")
    
    # 创建工作表配置
    worksheet_configs = [
        WorksheetConfig(
            worksheet_name="长租账单-往期",
            header_row_num=1,
            data_start_row_num=2
        ),
        WorksheetConfig(
            worksheet_name="长租账单-新增",
            header_row_num=1,
            data_start_row_num=2
        ),
        WorksheetConfig(
            worksheet_name="短租账单明细",
            header_row_num=2,      # 可以为不同工作表指定不同的行号
            data_start_row_num=3
        ),
        WorksheetConfig(
            worksheet_name="设备明细",
            header_row_num=1,
            data_start_row_num=3   # 可以为不同工作表指定不同的起始行
        )
    ]
    
    template_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC-1000-20220721-22557.xls"
    
    if os.path.exists(template_path):
        success = enhancer.enhance_worksheets_with_configs(
            template_path=template_path,
            worksheet_configs=worksheet_configs,
            output_path="enhanced_with_configs.xlsx"
        )
        
        if success:
            print(f"✅ 配置对象批量处理成功")
        else:
            print(f"❌ 配置对象批量处理失败")
    else:
        print(f"⚠️  模板文件不存在")
    
    print(f"\n💡 配置对象使用方法:")
    print(f"   from excel_data.template_enhancer_v2 import TemplateEnhancerV2, WorksheetConfig")
    print(f"   ")
    print(f"   # 创建工作表配置")
    print(f"   configs = [")
    print(f"       WorksheetConfig('长租账单-往期', 1, 2),")
    print(f"       WorksheetConfig('短租账单明细', 2, 3),")
    print(f"       WorksheetConfig('设备明细', 1, 3)")
    print(f"   ]")
    print(f"   ")
    print(f"   # 创建增强器并批量处理")
    print(f"   enhancer = TemplateEnhancerV2()")
    print(f"   enhancer.enhance_worksheets_with_configs(template_path, configs)")


if __name__ == "__main__":
    main() 