"""
简单测试Custom001Processor的数据过滤功能
"""

from custom_001 import Custom001Processor
import json


def test_data_filtering():
    """测试数据过滤功能"""
    print("🧪 测试数据过滤功能")
    print("=" * 50)
    
    # 创建处理器
    processor = Custom001Processor()
    
    # 显示数据统计
    processor.show_data_statistics()
    
    # 测试过滤功能
    new_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", processor.BILL_PERIOD_FLAG_NEW)
    old_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", processor.BILL_PERIOD_FLAG_OLD)
    
    print(f"\n📊 过滤结果:")
    print(f"   长租账单-新增 (BILL_PERIOD_FLAG_NEW): {len(new_bills)} 条")
    print(f"   长租账单-往期 (BILL_PERIOD_FLAG_OLD): {len(old_bills)} 条")
    
    # 显示前几条数据的详细信息
    if new_bills:
        print(f"\n📋 新增账单示例:")
        for i, bill in enumerate(new_bills[:2], 1):
            print(f"   {i}. 账单类型: {bill.get('billType')}, 标志: {bill.get('billPeriodFlag')}")
            print(f"      订单号: {bill.get('businessOrderNo')}, 金额: {bill.get('billStatementAmount')}")
    
    if old_bills:
        print(f"\n📋 往期账单示例:")
        for i, bill in enumerate(old_bills[:2], 1):
            print(f"   {i}. 账单类型: {bill.get('billType')}, 标志: {bill.get('billPeriodFlag')}")
            print(f"      订单号: {bill.get('businessOrderNo')}, 金额: {bill.get('billStatementAmount')}")


def main():
    """主函数"""
    print("🚀 Custom001Processor 数据过滤测试")
    print("=" * 60)
    
    # 测试数据过滤
    test_data_filtering()
    
    print(f"\n📖 功能说明:")
    print(f"   1. 根据账单类型='BUSINESS_BILL_TYPE_LONG'和账单标志进行数据过滤")
    print(f"   2. BILL_PERIOD_FLAG_NEW -> 填充到'长租账单-新增'工作表")
    print(f"   3. BILL_PERIOD_FLAG_OLD -> 填充到'长租账单-往期'工作表")
    print(f"   4. 实际数据填充功能已实现，可以调用process_with_files()方法")


if __name__ == "__main__":
    main() 