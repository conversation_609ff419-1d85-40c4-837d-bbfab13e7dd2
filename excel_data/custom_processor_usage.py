"""
Custom001Processor 使用示例
演示如何根据账单类型和账单标志分别填充不同工作表
"""

from custom_001 import Custom001Processor
import os


def main():
    """主函数 - 演示特殊处理功能"""
    
    print("🚀 Custom001Processor 特殊处理功能演示")
    print("=" * 60)
    
    # 创建处理器
    processor = Custom001Processor()
    
    # 显示数据统计
    print("📊 数据统计:")
    processor.show_data_statistics()
    
    # 模板文件路径
    template_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC-1000-20220721-22557.xlsx"
    data_file = "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json"
    output_file = "custom_processor_output.xlsx"
    
    print(f"\n📄 处理配置:")
    print(f"   模板文件: {template_path}")
    print(f"   数据文件: {data_file}")
    print(f"   输出文件: {output_file}")
    
    if os.path.exists(template_path):
        print(f"\n🔄 开始特殊处理...")
        
        # 使用特殊处理器处理模板
        success = processor.process_with_files(template_path, data_file, output_file)
        
        if success:
            print(f"\n✅ 特殊处理成功完成!")
            print(f"💾 输出文件: {output_file}")
            print(f"\n📋 处理结果:")
            print(f"   - 长租账单-新增工作表: 填充了新增账单数据")
            print(f"   - 长租账单-往期工作表: 填充了往期账单数据")
            print(f"   - 保持了Excel模板的原始样式和格式")
        else:
            print(f"\n❌ 特殊处理失败")
    else:
        print(f"\n⚠️  模板文件不存在: {template_path}")
        print(f"请确保模板文件路径正确")


def demonstrate_filtering():
    """演示数据过滤功能"""
    print(f"\n🔍 数据过滤演示:")
    print("=" * 40)
    
    processor = Custom001Processor()
    
    # 过滤新增账单
    new_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", processor.BILL_PERIOD_FLAG_NEW)
    print(f"   长租账单-新增: {len(new_bills)} 条")
    
    # 过滤往期账单
    old_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", processor.BILL_PERIOD_FLAG_OLD)
    print(f"   长租账单-往期: {len(old_bills)} 条")
    
    # 显示详细信息
    if new_bills:
        print(f"\n   新增账单示例:")
        for i, bill in enumerate(new_bills[:1], 1):
            print(f"     {i}. 订单号: {bill.get('businessOrderNo')}")
            print(f"        金额: {bill.get('billStatementAmount')}")
            print(f"        标志: {bill.get('billPeriodFlag')}")
    
    if old_bills:
        print(f"\n   往期账单示例:")
        for i, bill in enumerate(old_bills[:1], 1):
            print(f"     {i}. 订单号: {bill.get('businessOrderNo')}")
            print(f"        金额: {bill.get('billStatementAmount')}")
            print(f"        标志: {bill.get('billPeriodFlag')}")


def show_processing_rules():
    """显示处理规则"""
    print(f"\n📖 处理规则说明:")
    print("=" * 40)
    print(f"   1. 账单类型 = 'BUSINESS_BILL_TYPE_LONG' (长租账单)")
    print(f"   2. 账单标志 = 'BILL_PERIOD_FLAG_NEW' → 填充到 '长租账单-新增' 工作表")
    print(f"   3. 账单标志 = 'BILL_PERIOD_FLAG_OLD' → 填充到 '长租账单-往期' 工作表")
    print(f"   4. 使用父类的数据处理逻辑，保持Excel样式")
    print(f"   5. 支持循环占位符和非循环占位符的替换")


if __name__ == "__main__":
    main()
    demonstrate_filtering()
    show_processing_rules()
    
    print(f"\n🎉 演示完成!")
    print(f"现在你可以使用 Custom001Processor 来处理你的模板了!") 