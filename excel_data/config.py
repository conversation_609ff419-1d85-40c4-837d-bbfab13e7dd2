"""
配置文件 - OMS API配置
"""

import os
from typing import Dict, Any


class Config:
    """配置类"""
    
    # OMS API配置
    OMS_API_BASE_URL = os.getenv('OMS_API_BASE_URL', 'http://oms-api.example.com')
    OMS_API_TOKEN = os.getenv('OMS_API_TOKEN', 'your_token_here')
    OMS_API_TIMEOUT = int(os.getenv('OMS_API_TIMEOUT', '30'))
    OMS_API_MAX_RETRIES = int(os.getenv('OMS_API_MAX_RETRIES', '3'))
    
    # 默认模板配置
    DEFAULT_TEMPLATE_ID = int(os.getenv('DEFAULT_TEMPLATE_ID', '1'))
    
    # 日志配置
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = os.getenv('LOG_FILE', './logs/oms_api.log')
    
    # 数据验证配置
    MIN_CUSTOMER_NO_LENGTH = 3
    MAX_CUSTOMER_NO_LENGTH = 50
    MIN_BILL_MONTH = '2020-01'
    MAX_BILL_MONTH_OFFSET = 1  # 当前月份+1
    
    # 金额精度配置
    DECIMAL_PRECISION = 2
    DECIMAL_ROUNDING = 'ROUND_HALF_UP'
    
    # 枚举映射配置
    PAY_STATUS_MAPPING = {
        0: "未支付",
        1: "部分支付", 
        2: "已支付",
        3: "已退款"
    }
    
    BUSINESS_ORDER_TYPE_MAPPING = {
        1: "租赁订单",
        2: "销售订单",
        3: "服务订单"
    }
    
    RENT_MODE_MAPPING = {
        1: "固定租期",
        2: "即租即还",
        3: "租完即送",
        4: "分期销售",
        5: "组合租赁"
    }
    
    BILL_TYPE_MAPPING = {
        "BUSINESS_BILL_TYPE_LONG": "长租账单明细",
        "BUSINESS_BILL_TYPE_SHORT": "短租账单明细",
        "BUSINESS_BILL_TYPE_SALE": "销售账单明细",
        "BUSINESS_BILL_TYPE_IT": "IT服务账单明细",
        "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL": "租赁设备明细"
    }
    
    # 归属公司映射
    ORDER_SUB_COMPANY_MAPPING = {
        1: "总公司",
        2: "深圳分公司",
        3: "上海分公司", 
        4: "北京分公司",
        5: "广州分公司",
        6: "南京分公司",
        7: "厦门分公司",
        8: "武汉分公司",
        9: "成都分公司",
        10: "电销",
        11: "渠道大客户",
        12: "战略客户中心",
        13: "杭州分公司",
        14: "业务发展部",
        15: "用户代办池",
        16: "小熊优服",
        17: "平台营销部",
        18: "文印业务中心",
        19: "杭州战区",
        20: "元建科技",
        21: "凌雄美邦公司",
        22: "凌雄优企分公司"
    }
    
    # 租赁时长类型映射
    RENT_LENGTH_TYPE_MAPPING = {
        1: "短租订单",
        2: "长租订单",
        3: "样机订单",
        4: "销售订单",
        5: "租完即送-分期付款",
        6: "租完即送-一次性付款",
        7: "销售订单（分期）",
        8: "费用单",
        9: "IT服务订单",
        10: "资产处置订单",
        11: "资产报废订单"
    }
    
    # 租赁状态映射
    RENT_STATUS_MAPPING = {
        1: "在租",
        2: "退货"
    }
    
    # 错误码配置
    ERROR_CODES = {
        200: "成功",
        400: "请求参数错误",
        401: "未授权",
        403: "禁止访问",
        404: "资源不存在",
        500: "服务器内部错误",
        503: "服务不可用",
        1001: "客户编号不存在",
        1002: "账单月份格式错误",
        1003: "模板ID不存在",
        1004: "收款账户不存在",
        1005: "无账单数据",
        1006: "数据权限不足"
    }
    
    # 日期格式配置
    DATE_FORMATS = [
        "%Y-%m-%dT%H:%M:%S.%fZ",
        "%Y-%m-%dT%H:%M:%SZ", 
        "%Y-%m-%d %H:%M:%S",
        "%Y-%m-%d"
    ]
    
    # 请求头配置
    DEFAULT_HEADERS = {
        'Content-Type': 'application/json',
        'User-Agent': 'CustomExcel/1.0',
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip, deflate'
    }
    
    # 缓存配置
    CACHE_ENABLED = os.getenv('CACHE_ENABLED', 'true').lower() == 'true'
    CACHE_TTL = int(os.getenv('CACHE_TTL', '300'))  # 5分钟
    
    # 重试配置
    RETRY_DELAY = float(os.getenv('RETRY_DELAY', '1.0'))  # 1秒
    RETRY_BACKOFF = float(os.getenv('RETRY_BACKOFF', '2.0'))  # 指数退避
    
    @classmethod
    def get_oms_config(cls) -> Dict[str, Any]:
        """获取OMS API配置"""
        return {
            'base_url': cls.OMS_API_BASE_URL,
            'timeout': cls.OMS_API_TIMEOUT,
            'max_retries': cls.OMS_API_MAX_RETRIES,
            'headers': cls.DEFAULT_HEADERS.copy()
        }
    
    @classmethod
    def get_logging_config(cls) -> Dict[str, Any]:
        """获取日志配置"""
        return {
            'level': cls.LOG_LEVEL,
            'format': cls.LOG_FORMAT,
            'file': cls.LOG_FILE
        }
    
    @classmethod
    def get_validation_config(cls) -> Dict[str, Any]:
        """获取验证配置"""
        return {
            'min_customer_no_length': cls.MIN_CUSTOMER_NO_LENGTH,
            'max_customer_no_length': cls.MAX_CUSTOMER_NO_LENGTH,
            'min_bill_month': cls.MIN_BILL_MONTH,
            'max_bill_month_offset': cls.MAX_BILL_MONTH_OFFSET
        }


# 开发环境配置
class DevelopmentConfig(Config):
    """开发环境配置"""
    OMS_API_BASE_URL = 'http://localhost:20001'
    LOG_LEVEL = 'DEBUG'
    CACHE_ENABLED = False


# 测试环境配置
class TestingConfig(Config):
    """测试环境配置"""
    OMS_API_BASE_URL = 'http://test-oms-api.example.com'
    LOG_LEVEL = 'INFO'
    CACHE_ENABLED = True


# 生产环境配置
class ProductionConfig(Config):
    """生产环境配置"""
    OMS_API_BASE_URL = 'https://oms-api.example.com'
    LOG_LEVEL = 'WARNING'
    CACHE_ENABLED = True


# 根据环境变量选择配置
def get_config():
    """根据环境变量获取配置"""
    env = os.getenv('ENVIRONMENT', 'development').lower()
    
    if env == 'production':
        return ProductionConfig
    elif env == 'testing':
        return TestingConfig
    else:
        return DevelopmentConfig


# 当前配置实例
current_config = get_config() 