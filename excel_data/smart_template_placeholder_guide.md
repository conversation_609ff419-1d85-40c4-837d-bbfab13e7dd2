# Excel模板占位符使用指南

## 智能模板增强功能

本增强器能够智能分析Excel模板内容，自动识别并替换合适的占位符，无需依赖工作表名称。

### 核心特性
- **内容智能识别**：基于单元格内容自动匹配占位符，不依赖工作表名称
- **上下文分析**：分析工作表整体内容，智能选择最合适的账单类型
- **多类型支持**：支持长租、短租、销售、IT服务等多种账单类型
- **格式兼容**：支持xls和xlsx格式，自动转换并保留样式

## 基础信息占位符
- `${customerName}` - 客户名称
- `${customerNo}` - 客户编号  
- `${customerId}` - 客户ID
- `${currentBillMonth}` - 当前账单月份
- `${accountTime}` - 格式化月份 (如：2025年07月)
- `${currentTime}` - 当前时间

## 金额汇总占位符
- `${totalBillAmount}` - 总账单金额
- `${totalPaidAmount}` - 总已付金额
- `${totalUnPaidAmount}` - 总未付金额

## 收款账户占位符
- `${accountName}` - 收款账户名称
- `${accountBank}` - 收款银行
- `${accountNo}` - 收款账号

## 账单类型占位符
- `${long.billType}` - 长租账单类型
- `${short.billType}` - 短租账单类型
- `${sale.billType}` - 销售账单类型
- `${it.billType}` - IT服务账单类型
- `${equipment.billType}` - 设备明细
- `${fees.billType}` - 费用明细

## 智能循环占位符

系统会根据工作表内容自动选择最合适的账单类型：

### 长租账单明细 (long)
- `${@long.excelBillPeriodDetailList.productName}` - 商品名称
- `${@long.excelBillPeriodDetailList.businessOrderNo}` - 订单号
- `${@long.excelBillPeriodDetailList.billAmount}` - 金额
- `${@long.excelBillPeriodDetailList.count}` - 数量
- `${@long.excelBillPeriodDetailList.payStatus}` - 支付状态

### 短租账单明细 (short)
- `${@short.excelBillPeriodDetailList.productName}` - 商品名称
- `${@short.excelBillPeriodDetailList.businessOrderNo}` - 订单号
- `${@short.excelBillPeriodDetailList.billAmount}` - 金额

### 销售账单明细 (sale)
- `${@sale.excelBillPeriodDetailList.productName}` - 商品名称
- `${@sale.excelBillPeriodDetailList.businessOrderNo}` - 订单号
- `${@sale.excelBillPeriodDetailList.count}` - 数量

## 智能匹配规则

### 关键字识别
系统会识别以下关键字并自动匹配对应占位符：
- **客户信息**：客户名称、客户编号、客户号 → 客户信息占位符
- **金额信息**：总金额、应付金额、已付金额、未付金额 → 金额占位符
- **账户信息**：收款户名、开户行、账号 → 账户信息占位符
- **设备信息**：商品名称、订单号、数量、单价 → 循环明细占位符

### 上下文分析
系统会分析工作表内容，根据出现的关键字频率判断账单类型：
- **长租相关**：长租、租赁、期数、租金、租期 → 选择 @long 占位符
- **短租相关**：短租、短期、临时 → 选择 @short 占位符  
- **销售相关**：销售、购买、出售、售价 → 选择 @sale 占位符
- **IT服务**：IT、服务、技术支持、维护 → 选择 @it 占位符
- **设备明细**：设备、明细、序列号、型号 → 选择 @equipment 占位符

## 使用方法

### 1. 自动增强任意模板
```python
python excel_data/template_placeholder_enhancer.py
# 修改脚本中的输入文件路径即可
```

### 2. 处理增强后的模板
```python
python excel_data/unified_moban_processor.py
# 使用增强后的模板自动填充数据
```

### 3. 测试模板效果
```python
python test_enhanced_template.py
# 验证模板处理效果
```

## 示例转换

### 原始文本 → 占位符
- "客户名称" → `${customerName}`
- "应付金额" → `${totalBillAmount}`
- "商品名称" → `${@long.excelBillPeriodDetailList.productName}` (如果检测到长租上下文)
- "商品名称" → `${@sale.excelBillPeriodDetailList.productName}` (如果检测到销售上下文)

### 智能上下文判断
- 工作表包含"长租"、"租期"等关键字 → 优先选择 @long 类型占位符
- 工作表包含"销售"、"购买"等关键字 → 优先选择 @sale 类型占位符

## 技术优势

1. **通用性强**：适用于任何Excel模板，不依赖特定的工作表名称
2. **智能化高**：基于内容分析自动选择最合适的占位符类型
3. **容错性好**：即使关键字匹配失败，也会选择合理的默认占位符
4. **扩展性强**：可以轻松添加新的关键字和占位符类型

## 注意事项

1. 系统会分析工作表前50行、前20列的内容进行上下文判断
2. 如果无法确定具体类型，会默认选择长租 (@long) 类型占位符
3. 建议在模板中使用明确的关键字，有助于系统准确识别
4. 支持中文关键字识别，对中文模板友好
