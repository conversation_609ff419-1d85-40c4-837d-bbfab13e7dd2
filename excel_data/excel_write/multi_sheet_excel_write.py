"""
多工作表账单数据Excel输出模块
根据账单类型拆分数据并创建多个工作表
"""

import os
from datetime import datetime, date
from decimal import Decimal
from typing import Any, Dict, List, Optional
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet

from excel_data.field_mapping import BillDataFieldMapping, ORDER_STATUS_MAPPING, ASSOCIATION_CREATE_TYPE_MAPPING, RENT_MODE_MAPPING, PAY_STATUS_MAPPING, RENT_SCENE_MAPPING, BILL_PERIOD_FLAG_MAPPING
from excel_data.config import Config


class MultiSheetBillDataExcelWriter:
    """多工作表账单数据Excel写入器"""
    
    def __init__(self):
        self.workbook = None
        self.current_row = 1
        
        # 定义样式常量
        self.MAIN_TITLE_FONT = Font(name='微软雅黑', size=13, bold=True, color='000000')
        self.SUMMARY_FONT = Font(name='微软雅黑', size=12, bold=True, color='8B0000')
        self.CALC_FONT = Font(name='微软雅黑', size=11, bold=False, color='000000')
        self.GROUP_FONT = Font(name='微软雅黑', size=11, bold=True, color='FFFFFF')
        self.FIELD_FONT_MAIN = Font(name='微软雅黑', size=10.5, bold=True, color='FFFFFF')
        self.FIELD_FONT_SUB = Font(name='微软雅黑', size=10.5, bold=False, color='808080')
        
        # 背景色
        self.CALC_BG = PatternFill(start_color='F0F0F0', end_color='F0F0F0', fill_type='solid')
        self.GROUP_BG = PatternFill(start_color='404040', end_color='404040', fill_type='solid')
        self.MAIN_FIELD_BG = PatternFill(start_color='0066CC', end_color='0066CC', fill_type='solid')
        self.SUB_FIELD_BG = PatternFill(start_color='C0C0C0', end_color='C0C0C0', fill_type='solid')
        
        # 对齐方式
        self.CENTER_ALIGN = Alignment(horizontal='center', vertical='center')
        self.LEFT_ALIGN = Alignment(horizontal='left', vertical='center')
        self.RIGHT_ALIGN = Alignment(horizontal='right', vertical='center')
        
        # 边框
        self.THIN_BORDER = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
        
        # 支持的账单类型
        self.SUPPORTED_BILL_TYPES = [
            "BUSINESS_BILL_TYPE_LONG",
            "BUSINESS_BILL_TYPE_SHORT", 
            "BUSINESS_BILL_TYPE_SALE",
            "BUSINESS_BILL_TYPE_IT",
            "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL"
        ]
    
    def create_workbook(self, filename: str):
        """创建Excel工作簿"""
        self.workbook = openpyxl.Workbook()
        # 删除默认工作表
        self.workbook.remove(self.workbook.active)
    
    def split_bill_data_by_type(self, bill_data):
        """根据账单类型拆分数据"""
        split_data = {}
        
        # 初始化每个账单类型的数据
        for bill_type in self.SUPPORTED_BILL_TYPES:
            split_data[bill_type] = {
                'statements': [],
                'total_bill_amount': Decimal('0'),
                'total_paid_amount': Decimal('0'),
                'total_unpaid_amount': Decimal('0')
            }
        
        # 遍历账单期结算列表
        for statement in bill_data.billPeriodStatementList:
            bill_type = statement.billType
            
            # 只处理支持的账单类型
            if bill_type in self.SUPPORTED_BILL_TYPES:
                split_data[bill_type]['statements'].append(statement)
                
                # 累计金额
                split_data[bill_type]['total_bill_amount'] += statement.billAmount
                split_data[bill_type]['total_paid_amount'] += statement.paidAmount
                split_data[bill_type]['total_unpaid_amount'] += statement.unPaidAmount
        
        return split_data
    
    def format_pay_status(self, pay_status: int) -> str:
        """格式化支付状态显示"""
        return PAY_STATUS_MAPPING.get(pay_status, f"未知({pay_status})")
    
    def format_bill_period_flag(self, detail) -> str:
        """格式化账单标志显示，根据复杂逻辑处理"""
        bill_period_flag = detail.billPeriodFlag if detail.billPeriodFlag else "BILL_PERIOD_FLAG_OLD"
        
        if bill_period_flag == "BILL_PERIOD_FLAG_NEW":
            # 新增设备，需要检查是否改配
            if hasattr(detail, 'isChangeAllocation') and detail.isChangeAllocation:
                if detail.isChangeAllocation == 1:  # COMMON_CONSTANT_YES
                    return "改配新增"
                elif detail.isChangeAllocation == 2:  # COMMON_TWO
                    return "维修换货新增"
                else:
                    return "新增设备"
            else:
                return "新增设备"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_DEPOSIT":
            return "押金金额"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_OLD":
            return "往期设备"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_RETURN":
            # 退货，需要检查退货单创建类型
            if hasattr(detail, 'returnOrderCreateType') and detail.returnOrderCreateType:
                if detail.returnOrderCreateType == 4:  # RETURN_ORDER_SOURCE_CHANGE_ALLOCATION
                    return "改配退回"
                elif (detail.returnOrderCreateType == 6 or  # RETURN_ORDER_SOURCE_REPAIR_REPLACE1
                      detail.returnOrderCreateType == 7):   # RETURN_ORDER_SOURCE_REPAIR_REPLACE2
                    return "维修换货退回"
                else:
                    return "退回设备"
            else:
                return "退回设备"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_ADJUST":
            return "调整项"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_OTHER":
            return "其他费用"
        
        elif bill_period_flag == "BILL_PERIOD_FLAG_IT_SERVICE":
            return "服务金额"
        
        else:
            return "往期设备"  # 默认值
    
    def format_value(self, value: Any, data_type: str = "String") -> str:
        """格式化值显示"""
        if value is None:
            return ""
        elif isinstance(value, datetime):
            return value.strftime("%Y/%m/%d")
        elif isinstance(value, date):
            return value.strftime("%Y/%m/%d")
        elif isinstance(value, Decimal):
            # 最多保持两位小数，如果是整数，则只显示整数
            float_val = float(value)
            if float_val == int(float_val):
                return f"{int(float_val)}"
            else:
                return f"{float_val:.2f}"
        elif isinstance(value, (list, tuple, set)):
            if len(value) == 0:
                return ""
            else:
                return ', '.join(str(item) for item in value)
        else:
            return str(value)
    
    def get_merge_start_cell(self, worksheet, cell):
        """获取合并区域的起始单元格，如果单元格不在合并区域中，返回原单元格"""
        try:
            # 检查单元格是否在合并区域中
            for merged_range in worksheet.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    # 找到合并区域，返回起始单元格
                    start_cell = worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                    return start_cell
            # 如果不在合并区域中，返回原单元格
            return cell
        except:
            # 如果出现任何错误，返回原单元格
            return cell
    
    def unmerge_cells_if_exists(self, worksheet, col, start_row, end_row):
        """如果存在合并区域，先取消合并"""
        try:
            # 检查是否存在包含指定范围的合并区域
            ranges_to_remove = []
            for merged_range in worksheet.merged_cells.ranges:
                # 检查是否与指定范围有重叠
                if (merged_range.min_col <= col <= merged_range.max_col and 
                    merged_range.min_row <= end_row and merged_range.max_row >= start_row):
                    ranges_to_remove.append(merged_range)
            
            # 取消找到的合并区域
            for range_to_remove in ranges_to_remove:
                worksheet.unmerge_cells(str(range_to_remove))
        except:
            # 如果出现任何错误，忽略
            pass
    
    def write_main_title(self, worksheet, bill_data, bill_type):
        """写入第1行 - 主标题行"""
        # 从JSON数据中获取客户名称和账单月份
        customer_name = bill_data.customerName if hasattr(bill_data, 'customerName') and bill_data.customerName else "未知客户"
        bill_month = bill_data.currentBillMonth.strftime('%Y年%m月') if bill_data.currentBillMonth else '未知月'
        
        # 获取账单类型描述
        bill_type_desc = Config.BILL_TYPE_MAPPING.get(bill_type, bill_type)
        
        # 按照新格式要求：客户名称 + 单元格内换行 + 账单标题
        title_text = f"{customer_name}\n{bill_month}-{bill_type_desc}"
        
        # 合并整个表头宽度（15列）
        start_col = 1
        end_col = 15
        
        cell = worksheet.cell(row=1, column=start_col)
        cell.value = title_text
        cell.font = self.MAIN_TITLE_FONT
        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
        
        # 合并单元格
        worksheet.merge_cells(f'{get_column_letter(start_col)}1:{get_column_letter(end_col)}1')
        
        # 调整行高以展示两行内容
        worksheet.row_dimensions[1].height = 55
        
        return 2  # 返回下一行的行号
    
    def write_summary_row(self, worksheet, bill_data, stats, current_row):
        """写入汇总行"""
        total_bill_amount = stats['total_bill_amount']
        total_paid_amount = stats['total_paid_amount']
        total_unpaid_amount = stats['total_unpaid_amount']
        bill_month_short = bill_data.currentBillMonth.strftime('%m月') if bill_data.currentBillMonth else '未知月'
        
        # 汇总文本格式
        summary_text = f"{bill_month_short}账单应付总金额: {total_bill_amount:,.2f}元, 已付: {total_paid_amount:,.2f}元, 未付金额: {total_unpaid_amount:,.2f}元"
        
        # 合并整个表头宽度（15列）
        start_col = 1
        end_col = 15
        
        cell = worksheet.cell(row=current_row, column=start_col)
        cell.value = summary_text
        cell.font = self.SUMMARY_FONT
        cell.alignment = Alignment(horizontal='left', vertical='center')
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
        
        # 合并单元格
        worksheet.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
        
        # 设置行高为30像素
        worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_calculation_row(self, worksheet, current_row):
        """写入计算逻辑行（金额描述文字）"""
        # 显示金额描述文字
        calc_descriptions = [
            "新增设备金额", "", "往期设备金额", "", "退回设备金额", "",
            "其他费用", "", "押金", "", "已付金额", "", "未付金额"
        ]
        
        col = 1
        for description in calc_descriptions:
            cell = worksheet.cell(row=current_row, column=col)
            cell.value = description
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='000000')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            col += 1
        
        # 设置行高为30像素
        worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_calculation_values_row(self, worksheet, statement, current_row):
        """写入计算逻辑数值行（对应金额数值和运算符）"""
        # 显示对应的数值和运算符（连续显示格式）
        calc_values = [
            statement.newEquipmentAmount,
            "+",
            statement.oldEquipmentAmount,
            "+",
            statement.returnEquipmentAmount,
            "+",
            statement.otherAmount,
            "+",
            statement.depositEquipmentAmount,
            "-",
            statement.paidAmount,
            "=",
            statement.unPaidAmount
        ]
        
        col = 1
        for value in calc_values:
            cell = worksheet.cell(row=current_row, column=col)
            if isinstance(value, (int, float, Decimal)):
                cell.value = self.format_value(value)
            else:
                cell.value = value
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='000000')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            col += 1
        
        # 设置行高为30像素
        worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_group_headers(self, worksheet, current_row):
        """写入字段分组行"""
        # 根据层级结构定义分组和对应的列范围
        groups = [
            ("账单信息", 1, 15),
            ("应付信息", 16, 22),
            ("客户信息", 23, 28),
            ("订单信息", 29, 32),
            ("商品信息", 33, 35),
            ("使用信息", 36, 38)
        ]
        
        for group_name, start_col, end_col in groups:
            cell = worksheet.cell(row=current_row, column=start_col)
            cell.value = group_name
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='808080')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            cell.border = Border(
                left=Side(style='thin', color='000000'),
                right=Side(style='thin', color='000000'),
                top=Side(style='thin', color='000000'),
                bottom=Side(style='thin', color='000000')
            )
            
            # 合并单元格
            if start_col != end_col:
                worksheet.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
        
        # 设置行高为30像素
        worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_field_headers(self, worksheet, current_row):
        """写入具体字段行"""
        # 根据字段映射定义所有字段
        all_fields = [
            # 账单信息（1-15列）
            "单号", "起租时间", "类别", "商品名", "配置/详情", "单价（元）", "数量", 
            "本期开始日", "本期结束日", "期数", "设备序列号", "应付金额", "支付状态", "是否续租", "账单标识",
            # 应付信息（16-22列）
            "出账金额", "退租金额", "调整金额", "优惠金额", "已付金额", "已退押金", "说明",
            # 客户信息（23-28列）
            "收件人", "省", "市", "区", "详细地址", "分子公司",
            # 订单信息（29-32列）
            "订单状态", "关联信息", "原订单", "退租日期",
            # 商品信息（33-35列）
            "成色", "租赁方式", "备注",
            # 使用信息（36-38列）
            "使用人", "使用组织", "使用备注"
        ]
        
        # 写入字段标题
        for col, field_name in enumerate(all_fields, 1):
            cell = worksheet.cell(row=current_row, column=col)
            cell.value = field_name
            
            # 根据字段位置设置不同样式
            if col <= 15:  # 账单信息字段（前15列）
                cell.font = Font(name='微软雅黑', size=10.5, bold=True, color='FFFFFF')
                cell.fill = PatternFill(start_color='0066CC', end_color='0066CC', fill_type='solid')
            else:  # 其他字段（后23列）
                cell.font = Font(name='微软雅黑', size=10.5, color='808080')
                cell.fill = PatternFill(start_color='C0C0C0', end_color='C0C0C0', fill_type='solid')
            
            cell.alignment = self.CENTER_ALIGN
            cell.border = self.THIN_BORDER
        
        # 设置行高为25像素
        worksheet.row_dimensions[current_row].height = 25
        
        return current_row + 1
    
    def write_data_rows(self, worksheet, statements, current_row):
        """写入数据行"""
        # 定义订单信息合并的列（支付状态、应付金额）
        order_merge_columns = [12, 13]  # 应付金额、支付状态
        
        # 定义支付信息相关列合并的列
        pay_info_merge_columns = [16, 17, 18, 19, 20, 21, 22]  # 出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金、说明
        
        # 定义需要取负值的列（优惠金额和调整金额）
        negate_columns = [18, 19]  # 调整金额(18)、优惠金额(19)
        
        # 用于跟踪合并状态
        merge_info = {}  # 存储每个合并组的起始行和行数
        
        for statement in statements:
            # 优先显示Excel明细列表
            details = statement.excelBillPeriodDetailList if statement.excelBillPeriodDetailList else statement.billPeriodDetailList
            
            for detail in details:
                # 处理单价逻辑：优先使用优惠单价，如果没有则使用原单价
                unit_amount = detail.unitAmount
                if detail.couponUnitAmount and detail.couponUnitAmount >= 0:
                    unit_amount = detail.couponUnitAmount
                if unit_amount is None:
                    unit_amount = 0
                
                # 根据租赁类型添加后缀
                rent_type_suffix = "/月" if hasattr(detail, 'rentType') and detail.rentType == 2 else "/天"
                unit_amount_display = f"{unit_amount}{rent_type_suffix}"
                
                # 处理本期结束日
                period_end_time = detail.periodEndTime
                
                # 处理期数逻辑：只有当phase和totalPhase都不为空时才显示
                phase_display = ""
                if detail.phase is not None and detail.totalPhase is not None:
                    phase_display = f"{detail.phase}/{detail.totalPhase}"
                
                # 账单信息数据 (15列)
                bill_data = [
                    detail.businessOrderNo,
                    detail.rentStartTime,
                    detail.categoryName,
                    detail.productName,
                    detail.productSkuName,
                    unit_amount_display,
                    detail.count,
                    detail.periodStartTime,
                    period_end_time,
                    phase_display,
                    ', '.join(str(item) for item in detail.serialNumberSet if item is not None) if detail.serialNumberSet else "",
                    detail.billAmount,
                    self.format_pay_status(detail.payStatus),
                    "是" if detail.isRelet == 1 else "否",
                    self.format_bill_period_flag(detail)
                ]
                
                # 应付信息数据 (7列)
                payment_data = [
                    detail.billStatementAmount,
                    detail.billReturnStatementAmount,
                    detail.correctAmount,
                    detail.couponAmount,
                    detail.paidAmount,
                    detail.returnAmount,
                    detail.payInfo if detail.payInfo else ''
                ]
                
                # 客户信息数据 (6列)
                customer_data = [
                    detail.consigneeName if detail.consigneeName else '',
                    detail.provinceName if detail.provinceName else '',
                    detail.cityName if detail.cityName else '',
                    detail.districtName if detail.districtName else '',
                    detail.address if detail.address else '',
                    detail.customerSubName if detail.customerSubName else ''
                ]
                
                # 订单信息数据 (4列)
                order_status = ORDER_STATUS_MAPPING.get(detail.orderStatus, f"未知({detail.orderStatus})")
                assoc_type = ASSOCIATION_CREATE_TYPE_MAPPING.get(detail.associationCreateType) if detail.associationCreateType else ''
                
                # 如果关联信息为空，则原订单号也为空
                origin_order_no = detail.originOrderNo if assoc_type else ''
                
                order_data = [
                    order_status,
                    assoc_type,
                    origin_order_no,
                    detail.returnTime
                ]
                
                # 商品信息数据 (3列)
                is_new = "全新" if detail.isNew == 1 else "次新"
                rent_mode = detail.rentModeDesc or RENT_MODE_MAPPING.get(detail.rentMode, '其他')
                
                product_data = [
                    is_new,
                    rent_mode,
                    detail.rentSceneMark if detail.rentSceneMark else ''
                ]
                
                # 使用信息数据 (3列)
                usage_data = [
                    detail.customerUser if detail.customerUser else '',
                    detail.customerOrganization if detail.customerOrganization else '',
                    detail.customerRemark if detail.customerRemark else ''
                ]
                
                # 合并所有数据
                all_data = bill_data + payment_data + customer_data + order_data + product_data + usage_data
                
                # 写入行数据
                for col, value in enumerate(all_data, 1):
                    cell = worksheet.cell(row=current_row, column=col)
                    
                    # 检查单元格是否属于合并区域，如果是，找到起始单元格
                    target_cell = self.get_merge_start_cell(worksheet, cell)
                    
                    # 特殊处理支付状态列
                    if col == 13:  # 支付状态列
                        pay_status_value = detail.payStatus
                        # 状态值转换逻辑
                        if pay_status_value == 0:  # STATEMENT_ORDER_STATUS_INIT
                            target_cell.fill = PatternFill(start_color='FFCC99', end_color='FFCC99', fill_type='solid')
                        else:
                            target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
                        
                        # 状态值转换
                        if pay_status_value == 20:  # STATEMENT_ORDER_STATUS_CORRECTED
                            pay_status_value = 8  # STATEMENT_ORDER_STATUS_NO
                        
                        # 获取状态描述
                        status_text = self.format_pay_status(pay_status_value)
                        target_cell.value = status_text
                    else:
                        # 特殊处理金额列（保持数值格式以便Excel求和）
                        amount_columns = [12, 16, 17, 18, 19, 20, 21]
                        if col in amount_columns:
                            if isinstance(value, (int, float, Decimal)):
                                # 对于需要取负值的列，进行特殊处理
                                if col in negate_columns:
                                    # 取绝对值后取负值
                                    abs_value = abs(float(value))
                                    target_cell.value = -abs_value
                                else:
                                    target_cell.value = float(value)
                            else:
                                target_cell.value = value
                        else:
                            target_cell.value = self.format_value(value)
                    
                    target_cell.font = Font(name='微软雅黑', size=9)
                    target_cell.alignment = self.CENTER_ALIGN
                    target_cell.border = self.THIN_BORDER
                    
                    # 特殊处理账单标志的背景色（只对账单标志列）
                    if col == 15:
                        if detail.billPeriodFlag == "BILL_PERIOD_FLAG_RETURN":
                            target_cell.fill = PatternFill(start_color='FFFF99', end_color='FFFF99', fill_type='solid')
                        elif detail.billPeriodFlag == "BILL_PERIOD_FLAG_NEW":
                            target_cell.fill = PatternFill(start_color='CCFFCC', end_color='CCFFCC', fill_type='solid')
                
                # 处理订单信息合并逻辑（支付状态、应付金额）
                if hasattr(detail, 'orderItemMerge') and detail.orderItemMerge:
                    if hasattr(detail, 'firstOrderItemMergeRecord') and detail.firstOrderItemMergeRecord:
                        # 这是订单信息合并组的第一行，记录合并信息
                        merge_row_count = getattr(detail, 'orderItemMergeRowCount', 1)
                        merge_info[current_row] = merge_row_count
                        
                        # 对订单信息相关列进行合并
                        for col in order_merge_columns:
                            if col <= len(all_data):
                                start_row = current_row
                                end_row = current_row + merge_row_count - 1
                                
                                # 先取消可能存在的合并
                                self.unmerge_cells_if_exists(worksheet, col, start_row, end_row)
                                
                                # 先设置主单元格的样式（值已经在上面设置过了）
                                main_cell = worksheet.cell(row=start_row, column=col)
                                
                                # 特殊处理支付状态列的背景色
                                if col == 13:  # 支付状态列
                                    pay_status_value = detail.payStatus
                                    if pay_status_value == 0:  # STATEMENT_ORDER_STATUS_INIT
                                        main_cell.fill = PatternFill(start_color='FFCC99', end_color='FFCC99', fill_type='solid')
                                    else:
                                        main_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
                                
                                # 确保样式正确
                                main_cell.font = Font(name='微软雅黑', size=9)
                                main_cell.alignment = self.CENTER_ALIGN
                                main_cell.border = self.THIN_BORDER
                                
                                # 然后合并单元格
                                worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
                
                # 处理支付信息相关列合并逻辑
                if hasattr(detail, 'payInfoMerge') and detail.payInfoMerge:
                    if hasattr(detail, 'firstPayInfoMergeRecord') and detail.firstPayInfoMergeRecord:
                        # 这是支付信息合并组的第一行
                        pay_info_merge_row_count = getattr(detail, 'payInfoMergeRowCount', 1)
                        
                        # 对支付信息相关列进行合并
                        for col in pay_info_merge_columns:
                            if col <= len(all_data):
                                start_row = current_row
                                end_row = current_row + pay_info_merge_row_count - 1
                                
                                # 先取消可能存在的合并
                                self.unmerge_cells_if_exists(worksheet, col, start_row, end_row)
                                
                                # 先设置主单元格的样式（值已经在上面设置过了）
                                main_cell = worksheet.cell(row=start_row, column=col)
                                main_cell.font = Font(name='微软雅黑', size=9)
                                main_cell.alignment = self.CENTER_ALIGN
                                main_cell.border = self.THIN_BORDER
                                
                                # 然后合并单元格
                                worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
                
                # 设置数据行高度为23像素
                worksheet.row_dimensions[current_row].height = 23
                
                current_row += 1
        
        return current_row
    
    def write_it_service_worksheet(self, worksheet, bill_data, it_service_data):
        """写入IT服务账单明细工作表"""
        # 写入主标题
        current_row = self.write_main_title(worksheet, bill_data, "BUSINESS_BILL_TYPE_IT")
        
        # 写入汇总行
        current_row = self.write_summary_row(worksheet, bill_data, it_service_data, current_row)
        
        # 写入详细汇总信息行
        current_row = self.write_it_service_detail_summary(worksheet, it_service_data, current_row)
        
        # 写入字段分组行
        current_row = self.write_it_service_group_headers(worksheet, current_row)
        
        # 写入字段标题行
        current_row = self.write_it_service_field_headers(worksheet, current_row)
        
        # 写入数据行
        current_row = self.write_it_service_data_rows(worksheet, it_service_data['statements'], current_row)
        
        # 自动调整列宽
        self.auto_adjust_column_width(worksheet)
    
    def write_it_service_detail_summary(self, worksheet, it_service_data, current_row):
        """写入IT服务详细汇总信息行"""
        # 获取第一个statement的汇总数据
        if it_service_data['statements']:
            statement = it_service_data['statements'][0]
            
            # 计算服务金额相关数据
            service_amount = statement.billAmount if hasattr(statement, 'billAmount') else 0
            paid_amount = statement.paidAmount if hasattr(statement, 'paidAmount') else 0
            unpaid_amount = statement.unPaidAmount if hasattr(statement, 'unPaidAmount') else 0
            
            # 根据文档显示IT服务账单明细的计算逻辑
            # 格式：服务金额 已付金额 未付金额
            calc_descriptions = [
                "服务金额", "", "已付金额", "", "未付金额"
            ]
            
            # 写入计算逻辑描述行
            for col in range(1, min(6, len(calc_descriptions) + 1)):
                cell = worksheet.cell(row=current_row, column=col)
                value = calc_descriptions[col - 1] if col - 1 < len(calc_descriptions) else ""
                cell.value = value
                cell.font = Font(name='微软雅黑', size=11, bold=True, color='000000')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            
            # 设置行高为30像素
            worksheet.row_dimensions[current_row].height = 30
            current_row += 1
            
            # 写入计算逻辑数值行
            calc_values = [
                service_amount, "-", paid_amount, "=", unpaid_amount
            ]
            
            for col in range(1, min(6, len(calc_values) + 1)):
                cell = worksheet.cell(row=current_row, column=col)
                value = calc_values[col - 1] if col - 1 < len(calc_values) else ""
                if isinstance(value, (int, float, Decimal)):
                    cell.value = self.format_value(value)
                else:
                    cell.value = value
                cell.font = Font(name='微软雅黑', size=11, bold=True, color='000000')
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            
            # 设置行高为30像素
            worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_it_service_group_headers(self, worksheet, current_row):
        """写入IT服务字段分组行"""
        # 根据IT服务账单明细的分组结构
        groups = [
            ("账单信息", 1, 10),
            ("应付信息", 11, 17),
            ("客户信息", 18, 23)
        ]
        
        # 写入分组标题
        for group_name, start_col, end_col in groups:
            # 合并分组单元格
            worksheet.merge_cells(f'{get_column_letter(start_col)}{current_row}:{get_column_letter(end_col)}{current_row}')
            
            # 设置分组标题
            cell = worksheet.cell(row=current_row, column=start_col)
            cell.value = group_name
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='808080')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            
            # 为整个合并区域设置完整的边框
            for col in range(start_col, end_col + 1):
                border_cell = worksheet.cell(row=current_row, column=col)
                
                # 设置边框
                border_parts = {}
                
                # 上边框和下边框（所有单元格都有）
                border_parts['top'] = Side(style='thin', color='000000')
                border_parts['bottom'] = Side(style='thin', color='000000')
                
                # 左边框（只有起始列）
                if col == start_col:
                    border_parts['left'] = Side(style='thin', color='000000')
                
                # 右边框（只有结束列）
                if col == end_col:
                    border_parts['right'] = Side(style='thin', color='000000')
                
                # 应用边框
                border_cell.border = Border(**border_parts)
        
        # 设置行高为30像素
        worksheet.row_dimensions[current_row].height = 30
        
        return current_row + 1
    
    def write_it_service_field_headers(self, worksheet, current_row):
        """写入IT服务字段标题行"""
        # 根据IT服务账单明细的字段定义
        field_headers = [
            # 账单信息字段（主要字段）
            "单号", "日期", "名称", "类别", "单价(元)", "数量", "应付金额", "支付状态", "是否续租", "账单标识",
            # 应付信息字段
            "出账金额", "退租金额", "调整金额", "优惠金额", "已付金额", "已退押金", "说明",
            # 客户信息字段
            "收件人", "省", "市", "区", "详细地址", "分子公司"
        ]
        
        # 写入字段标题
        for col, field_name in enumerate(field_headers, 1):
            cell = worksheet.cell(row=current_row, column=col)
            cell.value = field_name
            
            # 根据字段位置设置不同样式
            if col <= 10:  # 账单信息字段（前10列）
                cell.font = Font(name='微软雅黑', size=10.5, bold=True, color='FFFFFF')
                cell.fill = PatternFill(start_color='0066CC', end_color='0066CC', fill_type='solid')
            else:  # 其他字段
                cell.font = Font(name='微软雅黑', size=10.5, color='808080')
                cell.fill = PatternFill(start_color='C0C0C0', end_color='C0C0C0', fill_type='solid')
            
            cell.alignment = self.CENTER_ALIGN
            cell.border = self.THIN_BORDER
        
        # 设置行高为25像素
        worksheet.row_dimensions[current_row].height = 25
        
        return current_row + 1
    
    def write_it_service_data_rows(self, worksheet, statements, current_row):
        """写入IT服务数据行"""
        for statement in statements:
            # 优先显示Excel明细列表
            details = statement.excelBillPeriodDetailList if statement.excelBillPeriodDetailList else statement.billPeriodDetailList
            
            for detail in details:
                # 处理单价逻辑：优先使用优惠单价，如果没有则使用原单价
                unit_amount = detail.unitAmount
                if detail.couponUnitAmount and detail.couponUnitAmount >= 0:
                    unit_amount = detail.couponUnitAmount
                if unit_amount is None:
                    unit_amount = 0
                
                # 根据租赁类型添加后缀
                rent_type_suffix = "/月" if hasattr(detail, 'rentType') and detail.rentType == 2 else "/天"
                unit_amount_display = f"{unit_amount}{rent_type_suffix}"
                
                # 账单信息数据 (10列)
                bill_data = [
                    detail.businessOrderNo,
                    detail.rentStartTime,
                    detail.productName,
                    detail.categoryName,
                    unit_amount_display,
                    detail.count,
                    detail.billAmount,
                    self.format_pay_status(detail.payStatus),
                    "是" if detail.isRelet == 1 else "否",
                    self.format_bill_period_flag(detail)
                ]
                
                # 应付信息数据 (7列)
                payment_data = [
                    detail.billStatementAmount,
                    detail.billReturnStatementAmount,
                    detail.correctAmount,
                    detail.couponAmount,
                    detail.paidAmount,
                    detail.returnAmount,
                    detail.payInfo if detail.payInfo else ''
                ]
                
                # 客户信息数据 (6列)
                customer_data = [
                    detail.consigneeName if detail.consigneeName else '',
                    detail.provinceName if detail.provinceName else '',
                    detail.cityName if detail.cityName else '',
                    detail.districtName if detail.districtName else '',
                    detail.address if detail.address else '',
                    detail.customerSubName if detail.customerSubName else ''
                ]
                
                # 合并所有数据
                all_data = bill_data + payment_data + customer_data
                
                # 写入行数据
                for col, value in enumerate(all_data, 1):
                    cell = worksheet.cell(row=current_row, column=col)
                    
                    # 特殊处理金额列（保持数值格式以便Excel求和）
                    amount_columns = [7, 11, 12, 13, 14, 15, 16]  # 应付金额、出账金额、退租金额、调整金额、优惠金额、已付金额、已退押金
                    if col in amount_columns:
                        if isinstance(value, (int, float, Decimal)):
                            # 对于需要取负值的列，进行特殊处理
                            if col in [13, 14]:  # 调整金额、优惠金额
                                abs_value = abs(float(value))
                                cell.value = -abs_value
                            else:
                                cell.value = float(value)
                        else:
                            cell.value = value
                    else:
                        cell.value = self.format_value(value)
                    
                    cell.font = Font(name='微软雅黑', size=9)
                    cell.alignment = self.CENTER_ALIGN
                    cell.border = self.THIN_BORDER
                
                # 设置数据行高度为23像素
                worksheet.row_dimensions[current_row].height = 23
                
                current_row += 1
        
        return current_row
    
    def write_equipment_detail_worksheet(self, worksheet, bill_data, equipment_data):
        """写入设备明细工作表"""
        from excel_data.excel_write.equipment_detail_excel_write import EquipmentDetailExcelWriter
        
        # 创建设备明细写入器
        equipment_writer = EquipmentDetailExcelWriter()
        equipment_writer.workbook = self.workbook
        equipment_writer.worksheet = worksheet
        equipment_writer.current_row = 1
        
        # 计算设备汇总数据
        equipment_details = self.extract_equipment_details(equipment_data['statements'])
        initial_count = sum(detail.get('count', 0) for detail in equipment_details)
        return_count = len([detail for detail in equipment_details if detail.get('returnTime')])
        renting_count = len([detail for detail in equipment_details if detail.get('rentStatus') == 3])  # 租赁中状态
        
        # 准备设备明细数据
        equipment_result = {
            'success': True,
            'equipment_data': {
                'currentBillMonth': bill_data.currentBillMonth,
                'initial_count': initial_count,
                'return_count': return_count,
                'renting_count': renting_count,
                'bill_month_short': bill_data.currentBillMonth.strftime('%m月') if bill_data.currentBillMonth else '未知月'
            },
            'equipment_details': equipment_details
        }
        
        # 写入设备明细内容
        equipment_writer.write_main_title(equipment_result['equipment_data'])
        equipment_writer.write_summary_row(equipment_result['equipment_data'])
        equipment_writer.write_field_headers()
        equipment_writer.write_data_rows(equipment_result['equipment_details'])
        equipment_writer.auto_adjust_column_width()
    
    def extract_equipment_details(self, statements):
        """从账单数据中提取设备明细数据"""
        equipment_details = []
        
        for statement in statements:
            # 从accountOrderMonthEquipmentDetailList中提取设备明细
            if hasattr(statement, 'accountOrderMonthEquipmentDetailList') and statement.accountOrderMonthEquipmentDetailList:
                for detail in statement.accountOrderMonthEquipmentDetailList:
                    equipment_detail = {
                        'consigneeName': getattr(detail, 'consigneeName', ''),
                        'provinceName': getattr(detail, 'provinceName', ''),
                        'cityName': getattr(detail, 'cityName', ''),
                        'districtName': getattr(detail, 'districtName', ''),
                        'address': getattr(detail, 'address', ''),
                        'customerSubName': getattr(detail, 'customerSubName', ''),
                        'brandName': getattr(detail, 'brandName', ''),
                        'categoryName': getattr(detail, 'categoryName', ''),
                        'productName': getattr(detail, 'productName', ''),
                        'isNew': getattr(detail, 'isNewProduct', 0),
                        'rentModeDesc': getattr(detail, 'rentModeDesc', ''),
                        'productSkuName': getattr(detail, 'productSkuName', ''),
                        'serialNumberSet': getattr(detail, 'serialNumberSet', []),
                        'count': getattr(detail, 'rentingProductCount', 0),
                        'productSkuPrice': getattr(detail, 'productSkuPrice', 0),
                        'rentStatus': getattr(detail, 'rentStatus', 0),
                        'orderNo': getattr(detail, 'orderNo', ''),
                        'rentStartTime': getattr(detail, 'rentStartTime', None),
                        'expectReturnTime': getattr(detail, 'expectReturnTime', None),
                        'returnTime': getattr(detail, 'returnTime', None),
                        'rentLengthType': getattr(detail, 'rentLengthType', 0),
                        'orderSubCompanyId': getattr(detail, 'orderSubCompanyId', 0)
                    }
                    equipment_details.append(equipment_detail)
        
        # 如果没有设备明细数据，返回空列表
        return equipment_details
    
    def auto_adjust_column_width(self, worksheet):
        """自动调整列宽，确保露出完整的上一级标题"""
        # 定义分组和对应的列范围
        groups = [
            ("账单信息", 1, 15),
            ("应付信息", 16, 22),
            ("客户信息", 23, 28),
            ("订单信息", 29, 32),
            ("商品信息", 33, 35),
            ("使用信息", 36, 38)
        ]
        
        # 定义需要限制最大宽度的列（列索引从1开始）
        limited_width_columns = [1, 4, 5, 11]
        
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            col_num = column[0].column
            
            # 计算当前列的最大内容长度
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 检查当前列是否属于某个分组，如果是，需要考虑分组标题的长度
            for group_name, start_col, end_col in groups:
                if start_col <= col_num <= end_col and group_name:
                    # 如果分组标题比当前列内容更长，使用分组标题长度
                    group_title_length = len(group_name)
                    if group_title_length > max_length:
                        max_length = group_title_length
                    break
            
            # 设置合理的列宽，确保完整显示
            adjusted_width = min(max_length + 4, 23)
            
            # 对特定列限制最大宽度为23个字符
            if col_num in limited_width_columns:
                adjusted_width = min(adjusted_width, 23)
            
            # 数据区域最小留2个字符的边距
            adjusted_width = max(adjusted_width, max_length + 2)

            if adjusted_width > 23:
                adjusted_width = 23
            
            worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def write_total_view_worksheet(self, worksheet, bill_data, split_data):
        """写入账单总览工作表"""
        current_row = 1
        
        # 第1行 - 公司名称行
        cell = worksheet.cell(row=current_row, column=2)  # B1
        cell.value = bill_data.customerName if hasattr(bill_data, 'customerName') else "未知客户"
        cell.font = Font(name='微软雅黑', size=16, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        
        # 合并B1:E1
        worksheet.merge_cells('B1:E1')
        
        # 设置行高为55px
        worksheet.row_dimensions[current_row].height = 55
        current_row += 1
        
        # 第2行 - 空行（合并B2:E2，白色背景）
        cell = worksheet.cell(row=current_row, column=2)  # B2
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        worksheet.merge_cells('B2:E2')
        current_row += 1
        
        # 第3行 - 报告标题行
        bill_month = bill_data.currentBillMonth.strftime('%Y年%m月') if bill_data.currentBillMonth else '未知月'
        title_text = f"{bill_month}-账单总览"
        
        cell = worksheet.cell(row=current_row, column=2)  # B3
        cell.value = title_text
        cell.font = Font(name='微软雅黑', size=11, color='000000')
        cell.alignment = Alignment(horizontal='left', vertical='center')
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = self.THIN_BORDER
        worksheet.row_dimensions[current_row].height = 23
        
        # 合并B3:E3
        worksheet.merge_cells('B3:E3')
        
        # 设置行高
        worksheet.row_dimensions[current_row].height = 25
        current_row += 1
        
        # 第4行 - 主表格标题行
        headers = ["账单类型", "应付金额", "已付金额", "未付金额"]
        for col, header in enumerate(headers, 2):  # 从B列开始
            cell = worksheet.cell(row=current_row, column=col)
            cell.value = header
            cell.font = Font(name='微软雅黑', size=11, bold=True, color='000000')
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
            cell.border = self.THIN_BORDER
        
        # 设置行高
        worksheet.row_dimensions[current_row].height = 25
        current_row += 1
        
        # 第5-8行 - 账单类型数据行
        bill_types = [
            ("短租账单", "BUSINESS_BILL_TYPE_SHORT"),
            ("长租账单", "BUSINESS_BILL_TYPE_LONG"),
            ("销售账单", "BUSINESS_BILL_TYPE_SALE"),
            ("IT服务账单", "BUSINESS_BILL_TYPE_IT")
        ]
        
        total_bill_amount = Decimal('0')
        total_paid_amount = Decimal('0')
        total_unpaid_amount = Decimal('0')
        
        for bill_type_name, bill_type_key in bill_types:
            if bill_type_key in split_data and split_data[bill_type_key]['statements']:
                data = split_data[bill_type_key]
                
                # 账单类型名称
                cell = worksheet.cell(row=current_row, column=2)  # B列
                cell.value = bill_type_name
                cell.font = Font(name='微软雅黑', size=10, color='000000')
                cell.alignment = Alignment(horizontal='left', vertical='center')
                cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                
                # 应付金额
                cell = worksheet.cell(row=current_row, column=3)  # C列
                cell.value = float(data['total_bill_amount'])
                cell.font = Font(name='微软雅黑', size=10, color='000000')
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                
                # 已付金额
                cell = worksheet.cell(row=current_row, column=4)  # D列
                cell.value = float(data['total_paid_amount'])
                cell.font = Font(name='微软雅黑', size=10, color='000000')
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                
                # 未付金额
                cell = worksheet.cell(row=current_row, column=5)  # E列
                cell.value = float(data['total_unpaid_amount'])
                cell.font = Font(name='微软雅黑', size=10, color='000000')
                cell.alignment = Alignment(horizontal='right', vertical='center')
                cell.fill = PatternFill(start_color='D3D3D3', end_color='D3D3D3', fill_type='solid')
                
                # 设置行高为23px（约17.25磅）
                worksheet.row_dimensions[current_row].height = 23

                # 累计总金额
                total_bill_amount += data['total_bill_amount']
                total_paid_amount += data['total_paid_amount']
                total_unpaid_amount += data['total_unpaid_amount']
                
                current_row += 1
        
        # 第10行 - 合计行
        cell = worksheet.cell(row=current_row, column=2)  # B列
        cell.value = "合计"
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='left', vertical='center')
        cell.fill = PatternFill(start_color='99CCFF', end_color='99CCFF', fill_type='solid')
        
        # 应付金额合计
        cell = worksheet.cell(row=current_row, column=3)  # C列
        cell.value = float(total_bill_amount)
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='right', vertical='center')
        cell.fill = PatternFill(start_color='99CCFF', end_color='99CCFF', fill_type='solid')
        
        # 已付金额合计
        cell = worksheet.cell(row=current_row, column=4)  # D列
        cell.value = float(total_paid_amount)
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='right', vertical='center')
        cell.fill = PatternFill(start_color='99CCFF', end_color='99CCFF', fill_type='solid')
        
        # 未付金额合计（红色显示）
        cell = worksheet.cell(row=current_row, column=5)  # E列
        cell.value = float(total_unpaid_amount)
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='FF0000')
        cell.alignment = Alignment(horizontal='right', vertical='center')
        cell.fill = PatternFill(start_color='99CCFF', end_color='99CCFF', fill_type='solid')
        # 设置合计行行高为23px（约17.25磅）
        worksheet.row_dimensions[current_row].height = 23
        
        current_row += 1
        
        # 第10行 - 空白行
        current_row += 1
        
        # 第11行 - 收款账户信息标题行
        # 收款账户标题
        cell = worksheet.cell(row=current_row, column=2)  # B11
        cell.value = "收款账户"
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = self.THIN_BORDER
        worksheet.row_dimensions[current_row].height = 23  # 行高23px

        # 合并B11:C11
        worksheet.merge_cells(start_row=current_row, start_column=2, end_row=current_row, end_column=3)

        current_row += 1

        # 第12-14行 - 收款账户信息
        if hasattr(bill_data, 'shroffAccount') and bill_data.shroffAccount:
            account_info = [
                ("户名", bill_data.shroffAccount.accountName if hasattr(bill_data.shroffAccount, 'accountName') else ''),
                ("开户行", bill_data.shroffAccount.accountBank if hasattr(bill_data.shroffAccount, 'accountBank') else ''),
                ("账号", bill_data.shroffAccount.accountNo if hasattr(bill_data.shroffAccount, 'accountNo') else '')
            ]

            for label, value in account_info:
                # 标签
                cell = worksheet.cell(row=current_row, column=2)  # B列
                target_cell = self.get_merge_start_cell(worksheet, cell)
                target_cell.value = label
                target_cell.font = Font(name='微软雅黑', size=10, color='000000')
                target_cell.alignment = Alignment(horizontal='left', vertical='center')
                target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
                target_cell.border = self.THIN_BORDER

                # 值
                cell = worksheet.cell(row=current_row, column=3)  # C列
                target_cell = self.get_merge_start_cell(worksheet, cell)
                target_cell.value = value
                target_cell.font = Font(name='微软雅黑', size=10, color='000000')
                target_cell.alignment = Alignment(horizontal='left', vertical='center')
                target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
                target_cell.border = self.THIN_BORDER

                worksheet.row_dimensions[current_row].height = 23  # 行高23px
                current_row += 1

        # 第15-16行 - 空白行
        worksheet.row_dimensions[current_row].height = 23
        current_row += 1
        worksheet.row_dimensions[current_row].height = 23
        current_row += 1

        # 第17行 - 财务汇总信息标题行
        export_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        cell = worksheet.cell(row=current_row, column=2)  # B17
        cell.value = f"导出时间: {export_time}"
        cell.font = Font(name='微软雅黑', size=10, bold=True, color='000000')
        cell.alignment = Alignment(horizontal='center', vertical='center')
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = self.THIN_BORDER
        worksheet.row_dimensions[current_row].height = 23  # 行高23px

        # 合并B17:C17
        worksheet.merge_cells(start_row=current_row, start_column=2, end_row=current_row, end_column=3)

        current_row += 1

        # 第18-21行 - 财务汇总信息
        financial_data = []
        if hasattr(bill_data, 'customerAccount') and bill_data.customerAccount:
            previous_unpaid = getattr(bill_data.customerAccount, 'previousUnpaidAmount', 0)
            financial_data = [
                ("往期未支付", previous_unpaid),
                ("本期未付金额", total_unpaid_amount),
                ("累计未付金额", previous_unpaid + total_unpaid_amount),
                ("账户余额", getattr(bill_data.customerAccount, 'accountBalance', 0))
            ]

        for label, value in financial_data:
            # 标签
            cell = worksheet.cell(row=current_row, column=2)  # B列
            target_cell = self.get_merge_start_cell(worksheet, cell)
            target_cell.value = label
            target_cell.font = Font(name='微软雅黑', size=10, color='000000')
            target_cell.alignment = Alignment(horizontal='left', vertical='center')
            target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            target_cell.border = self.THIN_BORDER

            # 值
            cell = worksheet.cell(row=current_row, column=3)  # C列
            target_cell = self.get_merge_start_cell(worksheet, cell)
            target_cell.value = float(value)
            target_cell.font = Font(name='微软雅黑', size=10, color='000000')
            target_cell.alignment = Alignment(horizontal='center', vertical='center')
            target_cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            target_cell.border = self.THIN_BORDER

            worksheet.row_dimensions[current_row].height = 23  # 行高23px
            current_row += 1
        
        # 第22行 - 空白行
        current_row += 1
        
        # 第23-24行 - 备注信息
        notes = [
            "备注:如对账单明细金额有异议的,请在收到账单3个工作日内书面回复我司异议内容或将异议发送至我司经办人邮箱;",
            "否则视为贵司对账单无异议,认可我司账单内容。账单确认后请及时安排付款,回传水单谢谢!"
        ]
        
        for i, note in enumerate(notes):
            cell = worksheet.cell(row=current_row, column=2)  # B列
            cell.value = note
            cell.font = Font(name='微软雅黑', size=11, color='000000')
            cell.alignment = Alignment(horizontal='left', vertical='center')
            cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
            
            # 合并B-E列
            worksheet.merge_cells(f'B{current_row}:E{current_row}')
            
            # 设置行高为25px
            worksheet.row_dimensions[current_row].height = 25
            
            current_row += 1
        
        # 自动调整列宽
        self.auto_adjust_total_view_column_width(worksheet, bill_data)
        
        # 设置B1:E24范围内所有没有背景的单元格为白色背景
        self.set_white_background_for_unset_cells(worksheet, 1, 24, 2, 5)
    
    def auto_adjust_total_view_column_width(self, worksheet, bill_data):
        """动态调整账单总览工作表的列宽"""
        # 设置各列的宽度
        column_widths = {
            'A': 23,   # 备注区域
            'B': 36,   # 账单类型名称
            'C': 36,   # 应付金额
            'D': 36,   # 已付金额
            'E': 36,   # 未付金额
            'F': 36,   # 财务汇总
            'G': 23    # 备注区域
        }
        
        # 设置列宽
        for column_letter, width in column_widths.items():
            worksheet.column_dimensions[column_letter].width = width
    
    def set_white_background_for_unset_cells(self, worksheet, start_row, end_row, start_col, end_col):
        """设置指定范围内所有没有背景的单元格为白色背景"""
        white_fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        
        for row in range(start_row, end_row + 1):
            for col in range(start_col, end_col + 1):
                cell = worksheet.cell(row=row, column=col)
                
                # 检查单元格是否已经有背景色设置
                if cell.fill.start_color.rgb is None or cell.fill.start_color.rgb == '00000000':
                    # 如果没有背景色，设置为白色
                    cell.fill = white_fill
    
    def write_bill_data_to_excel(self, bill_result: Dict[str, Any], output_filename: str):
        """将账单数据写入Excel文件"""
        if not bill_result['success']:
            raise ValueError(f"获取失败: {bill_result.get('error', '未知错误')}")
        
        bill_data = bill_result['bill_data']
        
        # 创建Excel文件
        self.create_workbook(output_filename)
        
        # 根据账单类型拆分数据
        split_data = self.split_bill_data_by_type(bill_data)
        
        # 首先创建账单总览工作表（如果有数据的话）
        has_bill_data = any(split_data[bill_type]['statements'] for bill_type in self.SUPPORTED_BILL_TYPES)
        if has_bill_data:
            # 创建账单总览工作表
            worksheet = self.workbook.create_sheet(title="账单总览")
            self.write_total_view_worksheet(worksheet, bill_data, split_data)
        
        # 为每个支持的账单类型创建工作表（按特定顺序）
        # 确保IT服务账单明细在租赁设备明细前面
        bill_type_order = [
            "BUSINESS_BILL_TYPE_LONG",
            "BUSINESS_BILL_TYPE_SHORT", 
            "BUSINESS_BILL_TYPE_SALE",
            "BUSINESS_BILL_TYPE_IT",
            "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL"
        ]
        
        for bill_type in bill_type_order:
            if split_data[bill_type]['statements']:  # 只创建有数据的工作表
                # 创建工作表
                worksheet = self.workbook.create_sheet(title=Config.BILL_TYPE_MAPPING.get(bill_type, bill_type))
                
                # 特殊处理IT服务账单明细工作表
                if bill_type == "BUSINESS_BILL_TYPE_IT":
                    self.write_it_service_worksheet(worksheet, bill_data, split_data[bill_type])
                # 特殊处理设备明细工作表
                elif bill_type == "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL":
                    self.write_equipment_detail_worksheet(worksheet, bill_data, split_data[bill_type])
                else:
                    # 写入标准账单工作表内容（长租、短租、销售）
                    current_row = self.write_main_title(worksheet, bill_data, bill_type)
                    current_row = self.write_summary_row(worksheet, bill_data, split_data[bill_type], current_row)
                    
                    # 如果有多个statement，使用第一个进行汇总计算
                    if split_data[bill_type]['statements']:
                        first_statement = split_data[bill_type]['statements'][0]
                        current_row = self.write_calculation_row(worksheet, current_row)
                        current_row = self.write_calculation_values_row(worksheet, first_statement, current_row)
                    
                    current_row = self.write_group_headers(worksheet, current_row)
                    current_row = self.write_field_headers(worksheet, current_row)
                    current_row = self.write_data_rows(worksheet, split_data[bill_type]['statements'], current_row)
                    
                    # 自动调整列宽
                    self.auto_adjust_column_width(worksheet)
        
        # 如果没有创建任何工作表，创建一个默认工作表
        if len(self.workbook.sheetnames) == 0:
            worksheet = self.workbook.create_sheet(title="账单明细")
            worksheet.cell(row=1, column=1, value="无数据")
        
        # 保存文件
        self.workbook.save(output_filename)
        print(f"多工作表Excel文件已保存: {output_filename}")


def export_bill_data_to_multi_sheet_excel(customer_no: str, bill_month: str, output_filename: Optional[str] = None, export_bill_account_template_id: int = 1):
    """导出账单数据到多工作表Excel文件"""
    from excel_data.example_usage import get_bill_data_for_customer
    
    # 获取账单数据
    bill_result = get_bill_data_for_customer(customer_no, bill_month, export_bill_account_template_id)
    
    # 生成输出文件名
    if not output_filename:
        output_filename = f"账单明细_{customer_no}_{bill_month}_多工作表.xlsx"
    
    # 写入Excel
    writer = MultiSheetBillDataExcelWriter()
    writer.write_bill_data_to_excel(bill_result, output_filename)
    
    return output_filename


if __name__ == "__main__":
    # 测试导出功能
    customer_no = "LXCC-1000-********-03101"
    bill_month = "2025-07"
    
    try:
        output_file = export_bill_data_to_multi_sheet_excel(customer_no, bill_month)
        print(f"导出成功: {output_file}")
    except Exception as e:
        print(f"导出失败: {e}") 