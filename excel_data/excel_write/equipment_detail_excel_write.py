"""
设备明细Excel输出模块
根据 equipment_detail_style_description.md 规则实现Excel输出功能
"""

import os
from datetime import datetime, date
from decimal import Decimal
from typing import Any, Dict, List, Optional
import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet

from excel_data.field_mapping import RENT_MODE_MAPPING, ORDER_STATUS_MAPPING
from excel_data.config import Config


class EquipmentDetailExcelWriter:
    """设备明细Excel写入器"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        self.current_row = 1
        
        # 定义样式常量
        self.MAIN_TITLE_FONT = Font(name='微软雅黑', size=16, bold=True, color='000000')
        self.SUMMARY_FONT = Font(name='微软雅黑', size=12, bold=False, color='000000')
        self.SUMMARY_KEY_FONT = Font(name='微软雅黑', size=12, bold=False, color='FF0000')  # 红色关键数字
        self.FIELD_FONT = Font(name='微软雅黑', size=10.5, bold=True, color='FFFFFF')
        self.DATA_FONT = Font(name='微软雅黑', size=9, bold=False, color='000000')
        
        # 背景色
        self.FIELD_BG = PatternFill(start_color='0066CC', end_color='0066CC', fill_type='solid')  # 蓝色
        self.DATA_BG = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')  # 白色
        
        # 对齐方式
        self.CENTER_ALIGN = Alignment(horizontal='center', vertical='center')
        self.LEFT_ALIGN = Alignment(horizontal='left', vertical='center')
        self.RIGHT_ALIGN = Alignment(horizontal='right', vertical='center')
        
        # 边框
        self.THIN_BORDER = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
    
    def create_workbook(self, filename: str):
        """创建Excel工作簿"""
        self.workbook = openpyxl.Workbook()
        self.worksheet = self.workbook.active
        self.worksheet.title = "租赁设备明细"
        self.current_row = 1
    
    def format_value(self, value: Any) -> str:
        """格式化值显示"""
        if value is None:
            return ""
        elif isinstance(value, datetime):
            return value.strftime("%Y/%m/%d")
        elif isinstance(value, date):
            return value.strftime("%Y/%m/%d")
        elif isinstance(value, Decimal):
            # 最多保持两位小数，如果是整数，则只显示整数
            float_val = float(value)
            if float_val == int(float_val):
                return f"{int(float_val)}"
            else:
                return f"{float_val:.2f}"
        elif isinstance(value, (list, tuple, set)):
            if len(value) == 0:
                return ""
            else:
                return ', '.join(str(item) for item in value)
        else:
            return str(value)
    
    def write_main_title(self, equipment_data):
        """写入第1行 - 主标题行"""
        # 处理不同的数据格式
        if hasattr(equipment_data, 'currentBillMonth'):
            # 如果是对象格式
            bill_month = equipment_data.currentBillMonth.strftime('%Y年%m月') if equipment_data.currentBillMonth else '未知月'
        elif isinstance(equipment_data, dict) and 'currentBillMonth' in equipment_data:
            # 如果是字典格式
            current_bill_month = equipment_data['currentBillMonth']
            if current_bill_month:
                bill_month = current_bill_month.strftime('%Y年%m月')
            else:
                bill_month = '未知月'
        else:
            # 默认值
            bill_month = '未知月'
        
        title_text = f"{bill_month}-租赁设备明细"
        
        # 合并整个表头宽度（22列）
        start_col = 1
        end_col = 22
        
        cell = self.worksheet.cell(row=self.current_row, column=start_col)
        cell.value = title_text
        cell.font = self.MAIN_TITLE_FONT
        cell.alignment = self.CENTER_ALIGN
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = self.THIN_BORDER
        
        # 合并单元格
        self.worksheet.merge_cells(f'{get_column_letter(start_col)}{self.current_row}:{get_column_letter(end_col)}{self.current_row}')
        
        # 设置行高
        self.worksheet.row_dimensions[self.current_row].height = 30
        
        self.current_row += 1
    
    def write_summary_row(self, equipment_data):
        """写入第2行 - 汇总信息行"""
        # 处理不同的数据格式
        if isinstance(equipment_data, dict):
            # 如果是字典格式
            initial_count = equipment_data.get('initial_count', 0)
            return_count = equipment_data.get('return_count', 0)
            renting_count = equipment_data.get('renting_count', 0)
            bill_month_short = equipment_data.get('bill_month_short', '未知月')
        else:
            # 如果是对象格式或其他格式
            initial_count = getattr(equipment_data, 'initial_count', 0)
            return_count = getattr(equipment_data, 'return_count', 0)
            renting_count = getattr(equipment_data, 'renting_count', 0)
            bill_month_short = getattr(equipment_data, 'bill_month_short', '未知月')
        
        summary_text = f"{bill_month_short}期初数量:{initial_count},退货数量:{return_count},在租数量:{renting_count}"
        
        # 特殊处理：在租数量用红色显示
        # 这里需要特殊处理，但由于openpyxl的限制，我们先用普通文本
        # 实际实现中可以通过Rich Text来实现部分文字的颜色
        
        # 合并整个表头宽度（22列）
        start_col = 1
        end_col = 22
        
        cell = self.worksheet.cell(row=self.current_row, column=start_col)
        cell.value = summary_text
        cell.font = self.SUMMARY_FONT
        cell.alignment = self.LEFT_ALIGN
        cell.fill = PatternFill(start_color='FFFFFF', end_color='FFFFFF', fill_type='solid')
        cell.border = self.THIN_BORDER
        
        # 合并单元格
        self.worksheet.merge_cells(f'{get_column_letter(start_col)}{self.current_row}:{get_column_letter(end_col)}{self.current_row}')
        
        # 设置行高
        self.worksheet.row_dimensions[self.current_row].height = 30
        
        self.current_row += 1
    
    def write_field_headers(self):
        """写入第3行 - 字段标题行"""
        # 根据equipment_detail_style_description.md定义的22列字段
        field_names = [
            "收件人", "省", "市", "区", "详细地址", "子公司",
            "品牌", "类别", "商品名", "成色", "租赁方式", "配置",
            "设备序列号", "数量", "设备价值", "在租状态",
            "单号", "起租日期", "预计归还日期", "退租日期", "类型", "归属公司"
        ]
        
        # 写入字段标题
        for col, field_name in enumerate(field_names, 1):
            cell = self.worksheet.cell(row=self.current_row, column=col)
            cell.value = field_name
            cell.font = self.FIELD_FONT
            cell.alignment = self.CENTER_ALIGN
            cell.fill = self.FIELD_BG
            cell.border = self.THIN_BORDER
        
        # 设置行高
        self.worksheet.row_dimensions[self.current_row].height = 25
        
        self.current_row += 1
    
    def write_data_rows(self, equipment_details):
        """写入数据行"""
        for detail in equipment_details:
            # 基础信息数据 (6列)
            basic_data = [
                detail.get('consigneeName', ''),
                detail.get('provinceName', ''),
                detail.get('cityName', ''),
                detail.get('districtName', ''),
                detail.get('address', ''),
                detail.get('customerSubName', '')
            ]
            
            # 设备信息数据 (9列)
            is_new = "全新" if detail.get('isNew', 0) == 1 else "次新"
            rent_mode = detail.get('rentModeDesc') or RENT_MODE_MAPPING.get(detail.get('rentMode', 0), '其他')
            
            equipment_data = [
                detail.get('brandName', ''),
                detail.get('categoryName', ''),
                detail.get('productName', ''),
                is_new,
                rent_mode,
                detail.get('productSkuName', ''),
                ', '.join(str(item) for item in detail.get('serialNumberSet', []) if item is not None),
                detail.get('count', 0),
                detail.get('productSkuPrice', 0)
            ]
            
            # 租赁信息数据 (7列)
            rent_status = self.format_rent_status(detail.get('rentStatus', 0))
            
            rental_data = [
                rent_status,
                detail.get('orderNo', ''),
                detail.get('rentStartTime'),
                detail.get('expectReturnTime'),
                detail.get('returnTime'),
                self.format_rent_length_type(detail.get('rentLengthType', 0)),
                self.format_order_sub_company(detail.get('orderSubCompanyId', 1))
            ]
            
            # 合并所有数据
            all_data = basic_data + equipment_data + rental_data
            
            # 写入行数据
            for col, value in enumerate(all_data, 1):
                cell = self.worksheet.cell(row=self.current_row, column=col)
                cell.value = self.format_value(value)
                cell.font = self.DATA_FONT
                cell.alignment = self.CENTER_ALIGN
                cell.fill = self.DATA_BG
                cell.border = self.THIN_BORDER
                
                # 特殊处理数值列的对齐方式
                if col in [14, 15]:  # 数量、设备价值列
                    cell.alignment = self.RIGHT_ALIGN
                elif col in [2, 3, 4, 5]:  # 地址相关列
                    cell.alignment = self.LEFT_ALIGN
            
            # 设置数据行高度
            self.worksheet.row_dimensions[self.current_row].height = 23
            
            self.current_row += 1
    
    def format_rent_status(self, rent_status: int) -> str:
        """格式化租赁状态"""
        return Config.RENT_STATUS_MAPPING.get(rent_status, f"未知({rent_status})")
    
    def format_order_type(self, order_type: int) -> str:
        """格式化订单类型"""
        order_type_mapping = {
            1: "租赁订单",
            2: "销售订单",
            3: "服务订单"
        }
        return order_type_mapping.get(order_type, f"未知({order_type})")
    
    def format_order_sub_company(self, order_sub_company_id: int) -> str:
        """格式化归属公司"""
        return Config.ORDER_SUB_COMPANY_MAPPING.get(order_sub_company_id, f"未知({order_sub_company_id})")
    
    def format_rent_length_type(self, rent_length_type: int) -> str:
        """格式化租赁时长类型"""
        return Config.RENT_LENGTH_TYPE_MAPPING.get(rent_length_type, f"未知({rent_length_type})")
    
    def auto_adjust_column_width(self):
        """自动调整列宽"""
        # 定义需要特殊处理的列
        address_columns = [2, 3, 4, 5]  # 地址相关列
        serial_number_column = 13  # 设备序列号列
        
        for column in self.worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            col_num = column[0].column
            
            # 计算当前列的最大内容长度
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            # 设置合理的列宽
            adjusted_width = max_length + 2
            
            # 地址相关列适当加宽
            if col_num in address_columns:
                adjusted_width = max(adjusted_width, 12)
            
            # 设备序列号列适当加宽
            if col_num == serial_number_column:
                adjusted_width = max(adjusted_width, 15)
            
            # 限制最大宽度
            adjusted_width = min(adjusted_width, 30)
            
            self.worksheet.column_dimensions[column_letter].width = adjusted_width
    
    def write_equipment_data_to_excel(self, equipment_result: Dict[str, Any], output_filename: str):
        """将设备明细数据写入Excel文件"""
        if not equipment_result['success']:
            raise ValueError(f"获取失败: {equipment_result.get('error', '未知错误')}")
        
        equipment_data = equipment_result['equipment_data']
        equipment_details = equipment_result.get('equipment_details', [])
        
        # 创建Excel文件
        self.create_workbook(output_filename)
        
        # 按顺序写入各个部分
        self.write_main_title(equipment_data)
        self.write_summary_row(equipment_data)
        self.write_field_headers()
        self.write_data_rows(equipment_details)
        
        # 自动调整列宽
        self.auto_adjust_column_width()
        
        # 保存文件
        self.workbook.save(output_filename)
        print(f"设备明细Excel文件已保存: {output_filename}")


def export_equipment_data_to_excel(customer_no: str, bill_month: str, output_filename: Optional[str] = None, export_bill_account_template_id: int = 1):
    """导出设备明细数据到Excel文件"""
    from excel_data.example_usage import get_equipment_data_for_customer
    
    # 获取设备明细数据
    equipment_result = get_equipment_data_for_customer(customer_no, bill_month, export_bill_account_template_id)
    
    # 生成输出文件名
    if not output_filename:
        output_filename = f"设备明细_{customer_no}_{bill_month}.xlsx"
    
    # 写入Excel
    writer = EquipmentDetailExcelWriter()
    writer.write_equipment_data_to_excel(equipment_result, output_filename)
    
    return output_filename


if __name__ == "__main__":
    # 测试导出功能
    customer_no = "LXCC-1000-********-03101"
    bill_month = "2025-07"
    
    try:
        output_file = export_equipment_data_to_excel(customer_no, bill_month)
        print(f"导出成功: {output_file}")
    except Exception as e:
        print(f"导出失败: {e}") 