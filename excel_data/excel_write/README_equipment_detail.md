# 设备明细Excel导出功能

## 功能概述

根据 `equipment_detail_style_description.md` 的规范，实现了 `BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL` 类型的设备明细Excel导出功能。

## 实现文件

### 1. 核心实现文件

- **`equipment_detail_excel_write.py`**: 设备明细Excel写入器
  - `EquipmentDetailExcelWriter`: 设备明细Excel写入器类
  - `export_equipment_data_to_excel()`: 独立的设备明细导出函数

### 2. 集成文件

- **`default_excel_write.py`**: 添加了 `export_equipment_detail_to_excel()` 函数
- **`multi_sheet_excel_write.py`**: 支持设备明细工作表的多工作表导出

### 3. 测试文件

- **`test_equipment_detail.py`**: 设备明细独立导出测试
- **`test_multi_sheet_with_equipment.py`**: 包含设备明细的多工作表导出测试

## 样式规范

### 1. 工作表结构

- **第1行**: 主标题行（完整跨列合并，22列）
  - 内容：`{账单月份}-租赁设备明细`
  - 样式：16pt加粗黑色字体，居中对齐

- **第2行**: 汇总信息行（整行合并，22列）
  - 内容：`{月份}期初数量:{数量},退货数量:{数量},在租数量:{数量}`
  - 样式：12pt常规黑色字体，左对齐
  - 特殊：在租数量用红色显示

- **第3行**: 字段标题行（无合并，22列）
  - 样式：10.5pt加粗白色字体，蓝色背景，居中对齐

- **第4行开始**: 数据行
  - 样式：9pt常规黑色字体，白色背景，居中对齐

### 2. 字段映射

| Excel列 | 字段名 | 数据类型 | 说明 |
|---------|--------|----------|------|
| 1 | consigneeName | String | 收件人姓名 |
| 2 | provinceName | String | 省份名称 |
| 3 | cityName | String | 城市名称 |
| 4 | districtName | String | 区县名称 |
| 5 | address | String | 详细地址 |
| 6 | customerSubName | String | 子公司名称 |
| 7 | brandName | String | 设备品牌 |
| 8 | categoryName | String | 设备类别 |
| 9 | productName | String | 商品名称 |
| 10 | isNew | Integer | 设备成色（1-全新，0-次新） |
| 11 | rentModeDesc | String | 租赁方式描述 |
| 12 | productSkuName | String | 商品SKU配置 |
| 13 | serialNumberSet | List<String> | 设备序列号集合 |
| 14 | count | Integer | 设备数量 |
| 15 | productSkuPrice | Decimal | 设备价值 |
| 16 | rentStatus | Integer | 租赁状态 |
| 17 | orderNo | String | 订单号 |
| 18 | rentStartTime | Date | 租赁开始时间 |
| 19 | expectReturnTime | Date | 预期归还时间 |
| 20 | returnTime | Date | 实际退租时间 |
| 21 | orderType | Integer | 订单类型 |
| 22 | subsidiaryId | Integer | 归属公司ID |

## 使用方法

### 1. 独立导出设备明细

```python
from excel_data.excel_write.default_excel_write import export_equipment_detail_to_excel

# 导出设备明细Excel
output_file = export_equipment_detail_to_excel(
    customer_no="LXCC-1000-********-02686",
    bill_month="2025-07"
)
```

### 2. 多工作表导出（包含设备明细）

```python
from excel_data.excel_write.multi_sheet_excel_write import export_bill_data_to_multi_sheet_excel

# 导出多工作表Excel（包含设备明细）
output_file = export_bill_data_to_multi_sheet_excel(
    customer_no="LXCC-1000-********-02686",
    bill_month="2025-07"
)
```

## 测试方法

### 1. 测试独立设备明细导出

```bash
cd excel_data
python test_equipment_detail.py
```

### 2. 测试多工作表导出

```bash
cd excel_data
python test_multi_sheet_with_equipment.py
```

## 特殊处理

### 1. 数据提取

设备明细数据从 `BillPeriodStatement.accountOrderMonthEquipmentDetailList` 中提取，包含：
- 基础信息（收件人、地址等）
- 设备信息（品牌、类别、配置等）
- 租赁信息（状态、订单、日期等）

### 2. 样式处理

- 汇总行中的关键数字（在租数量）用红色突出显示
- 地址相关列使用左对齐
- 数值列（数量、设备价值）使用右对齐
- 设备序列号列适当加宽

### 3. 数据格式

- 日期格式：yyyy/MM/dd
- 数值格式：整数或保留两位小数
- 状态字段：显示描述文本而非数字代码

## 注意事项

1. 目前使用模拟数据进行测试，实际使用时需要连接真实的API
2. 汇总行中的红色数字显示需要进一步优化（可能需要使用Rich Text）
3. 设备明细数据需要从账单数据中正确提取
4. 列宽自动调整算法已针对设备明细进行了优化

## 后续优化

1. 实现真实的设备明细数据获取API
2. 优化汇总行中部分文字的颜色显示
3. 添加更多的数据验证和错误处理
4. 优化性能和内存使用 