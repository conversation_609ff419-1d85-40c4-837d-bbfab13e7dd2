"""
测试多工作表Excel导出功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from excel_data.excel_write.multi_sheet_excel_write import export_bill_data_to_multi_sheet_excel


def test_multi_sheet_excel_export():
    """测试多工作表Excel导出功能"""
    customer_no = "LXCC-1000-********-03101"
    bill_month = "2025-07"
    export_bill_account_template_id = 2
    
    try:
        print("开始测试多工作表Excel导出...")
        output_file = export_bill_data_to_multi_sheet_excel(customer_no, bill_month,None,export_bill_account_template_id)
        print(f"多工作表Excel导出成功: {output_file}")
        
        # 检查生成的文件
        if os.path.exists(output_file):
            print(f"文件已生成: {output_file}")
            file_size = os.path.getsize(output_file)
            print(f"文件大小: {file_size} 字节")
        else:
            print("错误: 文件未生成")
            
    except Exception as e:
        print(f"多工作表Excel导出失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_multi_sheet_excel_export() 