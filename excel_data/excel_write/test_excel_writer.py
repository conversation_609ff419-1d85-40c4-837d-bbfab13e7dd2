"""
Excel输出功能测试脚本
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from default_excel_write import export_bill_data_to_excel


def test_excel_export():
    """测试Excel导出功能"""
    print("开始测试Excel导出功能...")
    
    # 测试参数
    customer_no = "LXCC-1000-********-03101"
    bill_month = "2025-06"
    export_bill_account_template_id = 2
    
    try:
        # 执行导出
        start_time = datetime.now()
        output_file = export_bill_data_to_excel(customer_no, bill_month, None, export_bill_account_template_id)
        end_time = datetime.now()
        
        # 检查文件是否存在
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print("导出成功!")
            print(f"输出文件: {output_file}")
            print(f"文件大小: {file_size:,} 字节")
            print(f"耗时: {(end_time - start_time).total_seconds():.2f} 秒")
            
            # 显示文件路径
            abs_path = os.path.abspath(output_file)
            print(f"绝对路径: {abs_path}")
        else:
            print("导出失败: 文件未生成")
            
    except Exception as e:
        print(f"导出失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    # 测试基本导出功能
    test_excel_export()

    
    print("\n测试完成!") 