"""
OMS API使用示例
演示如何使用OMS接口获取账单数据
"""

import os
import sys
import json
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from oms_api_client import OMSApiClient, BillPeriodStatementStatistics
from config import current_config


class BillDataProcessor:
    """账单数据处理器"""
    
    def __init__(self, oms_client: OMSApiClient):
        self.oms_client = oms_client
    
    def get_customer_bill_data(self, customer_no: str, bill_month: str, export_bill_account_template_id: int = None) -> BillPeriodStatementStatistics:
        """
        获取客户账单数据
        
        Args:
            customer_no: 客户编号
            bill_month: 账单月份 (YYYY-MM)
            export_bill_account_template_id: 模板ID (默认1)
            
        Returns:
            BillPeriodStatementStatistics: 账单数据
        """
        print(f"正在获取客户 {customer_no} 的 {bill_month} 账单数据...")
        
        try:
            bill_data = self.oms_client.get_bill_data(
                customer_no=customer_no,
                current_bill_month=bill_month,
                export_bill_account_template_id=export_bill_account_template_id
            )
            
            print(f"账单数据获取成功!")
            return bill_data
            
        except Exception as e:
            print(f"获取账单数据失败: {str(e)}")
            raise
    
    def print_bill_summary(self, bill_data: BillPeriodStatementStatistics):
        """打印账单摘要信息"""
        print("\n" + "="*60)
        print("账单摘要信息")
        print("="*60)
        
        print(f"客户名称: {bill_data.customerName}")
        print(f"客户编号: {bill_data.customerNo}")
        print(f"账单月份: {bill_data.currentBillMonth.strftime('%Y年%m月')}")
        print(f"出帐日: {bill_data.billDateOfPayment.strftime('%Y年%m月%d日')}")
        
        # 账户信息
        print(f"\n账户信息:")
        print(f"  账户余额: ¥{bill_data.customerAccount.customerBalanceAmount:,.2f}")
        print(f"  累计未付: ¥{bill_data.customerAccount.totalUnPaidAmount:,.2f}")
        print(f"  尚未需支付: ¥{bill_data.customerAccount.totalNeedPayAmount:,.2f}")
        
        # 收款账户信息
        print(f"\n收款账户信息:")
        print(f"  账户名: {bill_data.collectionAccount.accountName}")
        print(f"  开户银行: {bill_data.collectionAccount.accountBank}")
        print(f"  账号: {bill_data.collectionAccount.accountNo}")
        
        # 账单统计
        total_bill_amount = Decimal('0.00')
        total_paid_amount = Decimal('0.00')
        total_unpaid_amount = Decimal('0.00')
        
        for i, statement in enumerate(bill_data.billPeriodStatementList):
            print(f"\n账单统计 {i+1}:")
            print(f"  业务模式: {statement.businessMode}")
            print(f"  账单类型: {current_config.BILL_TYPE_MAPPING.get(statement.billType, statement.billType)}")
            print(f"  应付金额: ¥{statement.billAmount:,.2f}")
            print(f"  已付金额: ¥{statement.paidAmount:,.2f}")
            print(f"  未付金额: ¥{statement.unPaidAmount:,.2f}")
            print(f"  新增设备金额: ¥{statement.newEquipmentAmount:,.2f}")
            print(f"  往期设备金额: ¥{statement.oldEquipmentAmount:,.2f}")
            print(f"  退回设备金额: ¥{statement.returnEquipmentAmount:,.2f}")
            print(f"  其他费用: ¥{statement.otherAmount:,.2f}")
            print(f"  调整项金额: ¥{statement.adjustmentEquipmentAmount:,.2f}")
            print(f"  服务金额: ¥{statement.itServiceAmount:,.2f}")
            print(f"  押金金额: ¥{statement.depositEquipmentAmount:,.2f}")
            print(f"  优惠金额: ¥{statement.couponAmount:,.2f}")
            print(f"  期初数量: {statement.periodStartEquipmentCount}")
            print(f"  退货数量: {statement.returnEquipmentCount}")
            print(f"  在租数量: {statement.rentingEquipmentCount}")
            
            total_bill_amount += statement.billAmount
            total_paid_amount += statement.paidAmount
            total_unpaid_amount += statement.unPaidAmount
        
        print(f"\n总计:")
        print(f"  总应付金额: ¥{total_bill_amount:,.2f}")
        print(f"  总已付金额: ¥{total_paid_amount:,.2f}")
        print(f"  总未付金额: ¥{total_unpaid_amount:,.2f}")
    
    def print_bill_details(self, bill_data: BillPeriodStatementStatistics, max_details: int = 5):
        """打印账单明细信息"""
        print(f"\n" + "="*60)
        print("账单明细信息")
        print("="*60)
        
        for i, statement in enumerate(bill_data.billPeriodStatementList):
            print(f"\n账单 {i+1} 明细 (显示前{max_details}条):")
            print("-" * 40)
            
            # 优先显示 excelBillPeriodDetailList
            if statement.excelBillPeriodDetailList:
                details = statement.excelBillPeriodDetailList[:max_details]
                print(f"Excel明细列表 (共{len(statement.excelBillPeriodDetailList)}条):")
            else:
                details = statement.billPeriodDetailList[:max_details]
                print(f"普通明细列表 (共{len(statement.billPeriodDetailList)}条):")
            
            for j, detail in enumerate(details):
                print(f"明细 {j+1}:")
                print(f"  业务单号: {detail.businessOrderNo}")
                print(f"  商品名称: {detail.productName}")
                print(f"  商品类别: {detail.categoryName}")
                print(f"  商品配置: {detail.productSkuName}")
                print(f"  数量: {detail.count}")
                print(f"  单价: ¥{detail.unitAmount:,.2f}")
                print(f"  优惠单价: ¥{detail.couponUnitAmount:,.2f}")
                print(f"  应付金额: ¥{detail.billAmount:,.2f}")
                print(f"  已付金额: ¥{detail.paidAmount:,.2f}")
                print(f"  支付状态: {current_config.PAY_STATUS_MAPPING.get(detail.payStatus, '未知')}")
                print(f"  期数: {detail.phase}/{detail.totalPhase}")
                print(f"  起租时间: {detail.rentStartTime.strftime('%Y-%m-%d') if detail.rentStartTime else 'N/A'}")
                print(f"  租赁方式: {current_config.RENT_MODE_MAPPING.get(detail.rentMode, '未知')}")
                print(f"  收货人: {detail.consigneeName}")
                print(f"  收货地址: {detail.provinceName}{detail.cityName}{detail.districtName}{detail.address}")
                print(f"  优惠金额: ¥{detail.couponAmount:,.2f}")
                print(f"  优惠说明: {detail.couponInfo}")
                
                if detail.serialNumberSet:
                    print(f"  设备序列号: {', '.join(detail.serialNumberSet)}")
                
                print()

    def _convert_to_dict(self, obj):
        """将对象转换为字典格式"""
        if hasattr(obj, '__dict__'):
            result = {}
            for key, value in obj.__dict__.items():
                if isinstance(value, (list, tuple)):
                    result[key] = [self._convert_to_dict(item) for item in value]
                elif isinstance(value, Decimal):
                    result[key] = float(value)
                elif isinstance(value, datetime):
                    result[key] = value.isoformat()
                elif hasattr(value, '__dict__'):
                    result[key] = self._convert_to_dict(value)
                else:
                    result[key] = value
            return result
        elif isinstance(obj, (list, tuple)):
            return [self._convert_to_dict(item) for item in obj]
        elif isinstance(obj, Decimal):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj
    
    def get_bill_statistics(self, bill_data: BillPeriodStatementStatistics) -> Dict[str, Any]:
        """获取账单统计信息"""
        stats = {
            'customer_info': {
                'customer_no': bill_data.customerNo,
                'customer_name': bill_data.customerName,
                'bill_month': bill_data.currentBillMonth.strftime('%Y-%m'),
                'bill_date_of_payment': bill_data.billDateOfPayment.strftime('%Y-%m-%d')
            },
            'account_info': {
                'balance_amount': float(bill_data.customerAccount.customerBalanceAmount),
                'total_unpaid_amount': float(bill_data.customerAccount.totalUnPaidAmount),
                'total_need_pay_amount': float(bill_data.customerAccount.totalNeedPayAmount)
            },
            'collection_account': {
                'account_name': bill_data.collectionAccount.accountName,
                'account_bank': bill_data.collectionAccount.accountBank,
                'account_no': bill_data.collectionAccount.accountNo
            },
            'bill_summary': {
                'total_statements': len(bill_data.billPeriodStatementList),
                'total_bill_amount': 0.0,
                'total_paid_amount': 0.0,
                'total_unpaid_amount': 0.0,
                'total_new_equipment_amount': 0.0,
                'total_old_equipment_amount': 0.0,
                'total_return_equipment_amount': 0.0,
                'total_other_amount': 0.0,
                'total_adjustment_amount': 0.0,
                'total_service_amount': 0.0,
                'total_deposit_amount': 0.0,
                'total_coupon_amount': 0.0,
                'total_equipment_count': 0,
                'total_return_count': 0,
                'total_renting_count': 0
            },
            'detail_summary': {
                'total_details': 0,
                'details_by_status': {},
                'details_by_category': {},
                'details_by_rent_mode': {}
            }
        }
        
        # 统计账单信息
        for statement in bill_data.billPeriodStatementList:
            stats['bill_summary']['total_bill_amount'] += float(statement.billAmount)
            stats['bill_summary']['total_paid_amount'] += float(statement.paidAmount)
            stats['bill_summary']['total_unpaid_amount'] += float(statement.unPaidAmount)
            stats['bill_summary']['total_new_equipment_amount'] += float(statement.newEquipmentAmount)
            stats['bill_summary']['total_old_equipment_amount'] += float(statement.oldEquipmentAmount)
            stats['bill_summary']['total_return_equipment_amount'] += float(statement.returnEquipmentAmount)
            stats['bill_summary']['total_other_amount'] += float(statement.otherAmount)
            stats['bill_summary']['total_adjustment_amount'] += float(statement.adjustmentEquipmentAmount)
            stats['bill_summary']['total_service_amount'] += float(statement.itServiceAmount)
            stats['bill_summary']['total_deposit_amount'] += float(statement.depositEquipmentAmount)
            stats['bill_summary']['total_coupon_amount'] += float(statement.couponAmount)
            stats['bill_summary']['total_equipment_count'] += statement.periodStartEquipmentCount
            stats['bill_summary']['total_return_count'] += statement.returnEquipmentCount
            stats['bill_summary']['total_renting_count'] += statement.rentingEquipmentCount
            
            # 统计明细信息
            for detail in statement.excelBillPeriodDetailList:
                stats['detail_summary']['total_details'] += 1
                
                # 按支付状态统计
                status = current_config.PAY_STATUS_MAPPING.get(detail.payStatus, '未知')
                stats['detail_summary']['details_by_status'][status] = \
                    stats['detail_summary']['details_by_status'].get(status, 0) + 1
                
                # 按商品类别统计
                category = detail.categoryName or '未分类'
                stats['detail_summary']['details_by_category'][category] = \
                    stats['detail_summary']['details_by_category'].get(category, 0) + 1
                
                # 按租赁方式统计
                rent_mode = current_config.RENT_MODE_MAPPING.get(detail.rentMode, '未知')
                stats['detail_summary']['details_by_category'][rent_mode] = \
                    stats['detail_summary']['details_by_rent_mode'].get(rent_mode, 0) + 1
        
        return stats


def main():
    """主函数 - 演示如何使用OMS API"""
    print("OMS API 账单数据获取示例")
    print("="*50)
    
    # 创建OMS客户端
    oms_config = current_config.get_oms_config()
    oms_client = OMSApiClient(
        base_url=oms_config['base_url']
    )
    
    # 创建数据处理器
    processor = BillDataProcessor(oms_client)
    
    # 示例客户数据
    test_customers = [
        {"customer_no": "LXCC-1000-20241219-03101", "bill_month": "2025-07"}
    ]
    
    results = []
    
    for i, customer in enumerate(test_customers):
        print(f"\n{'='*20} 处理客户 {i+1} {'='*20}")
        
        try:
            # 获取账单数据
            bill_data = processor.get_customer_bill_data(
                customer['customer_no'], 
                customer['bill_month']
            )
            
            # 打印账单摘要
            processor.print_bill_summary(bill_data)
            
            # 打印账单明细
            # processor.print_bill_details(bill_data, max_details=3)
            
            # 获取统计信息
            stats = processor.get_bill_statistics(bill_data)
            print(f"\n统计信息:")
            print(f"  总明细数量: {stats['detail_summary']['total_details']}")
            print(f"  总应付金额: ¥{stats['bill_summary']['total_bill_amount']:,.2f}")
            print(f"  总已付金额: ¥{stats['bill_summary']['total_paid_amount']:,.2f}")
            print(f"  总未付金额: ¥{stats['bill_summary']['total_unpaid_amount']:,.2f}")
            

            
            # 将结果添加到列表中
            results.append({
                'customer_info': customer,
                'bill_data': bill_data,
                'statistics': stats,
                'success': True
            })
            
        except Exception as e:
            print(f"处理客户 {customer['customer_no']} 失败: {str(e)}")
            results.append({
                'customer_info': customer,
                'bill_data': None,
                'statistics': None,
                'success': False,
                'error': str(e)
            })
            continue
    
    print(f"\n{'='*50}")
    print("示例执行完成!")
    
    return results


def get_bill_data_for_customer(customer_no: str, bill_month: str, export_bill_account_template_id: int = 1):
    """获取指定客户的账单数据"""
    print(f"获取客户 {customer_no} 的 {bill_month} 账单数据...")
    
    # 创建OMS客户端
    oms_config = current_config.get_oms_config()
    oms_client = OMSApiClient(
        base_url=oms_config['base_url']
    )
    
    # 创建数据处理器
    processor = BillDataProcessor(oms_client)
    
    try:
        # 获取账单数据
        bill_data = processor.get_customer_bill_data(customer_no, bill_month, export_bill_account_template_id)

        # 打印账单摘要
        processor.print_bill_summary(bill_data)

        # 打印账单明细
        # processor.print_bill_details(bill_data, max_details=3)

        # 获取统计信息
        stats = processor.get_bill_statistics(bill_data)
        print(f"\n统计信息:")
        print(f"  总明细数量: {stats['detail_summary']['total_details']}")
        print(f"  总应付金额: ¥{stats['bill_summary']['total_bill_amount']:,.2f}")
        print(f"  总已付金额: ¥{stats['bill_summary']['total_paid_amount']:,.2f}")
        print(f"  总未付金额: ¥{stats['bill_summary']['total_unpaid_amount']:,.2f}")
        
        return {
            'bill_data': bill_data,
            'statistics': stats,
            'success': True
        }
        
    except Exception as e:
        print(f"获取账单数据失败: {str(e)}")
        return {
            'bill_data': None,
            'statistics': None,
            'success': False,
            'error': str(e)
        }


def get_equipment_data_for_customer(customer_no: str, bill_month: str, export_bill_account_template_id: int = 1):
    """获取客户设备明细数据"""
    # 这里应该调用实际的API获取设备明细数据
    # 目前返回模拟数据用于测试
    
    from datetime import datetime
    from decimal import Decimal
    
    # 模拟设备明细数据
    equipment_data = {
        'currentBillMonth': datetime(2025, 7, 1),
        'initial_count': 166,
        'return_count': 19,
        'renting_count': 147,
        'bill_month_short': '07月'
    }
    
    # 模拟设备明细列表
    equipment_details = [
        {
            'consigneeName': '张三',
            'provinceName': '广东省',
            'cityName': '深圳市',
            'districtName': '南山区',
            'address': '科技园路1号',
            'customerSubName': '深圳分公司',
            'brandName': '联想',
            'categoryName': '笔记本电脑',
            'productName': 'ThinkPad X1 Carbon',
            'isNew': 1,
            'rentModeDesc': '固定租期',
            'productSkuName': 'i7-1165G7/16GB/512GB',
            'serialNumberSet': ['SN001', 'SN002'],
            'count': 2,
            'productSkuPrice': Decimal('5000.00'),
            'rentStatus': 3,
            'orderNo': 'ORDER001',
            'rentStartTime': datetime(2025, 1, 1),
            'expectReturnTime': datetime(2025, 12, 31),
            'returnTime': None,
            'orderType': 1,
            'subsidiaryId': 1001
        },
        {
            'consigneeName': '李四',
            'provinceName': '北京市',
            'cityName': '北京市',
            'districtName': '朝阳区',
            'address': '建国门外大街1号',
            'customerSubName': '北京分公司',
            'brandName': '戴尔',
            'categoryName': '台式机',
            'productName': 'OptiPlex 7090',
            'isNew': 0,
            'rentModeDesc': '即租即还',
            'productSkuName': 'i5-10500/8GB/256GB',
            'serialNumberSet': ['SN003'],
            'count': 1,
            'productSkuPrice': Decimal('3000.00'),
            'rentStatus': 3,
            'orderNo': 'ORDER002',
            'rentStartTime': datetime(2025, 2, 1),
            'expectReturnTime': datetime(2025, 8, 1),
            'returnTime': None,
            'orderType': 1,
            'subsidiaryId': 1002
        }
    ]
    
    return {
        'success': True,
        'equipment_data': equipment_data,
        'equipment_details': equipment_details
    }


if __name__ == "__main__":
    get_bill_data_for_customer("LXCC-1000-20241219-03101","2025-07")