"""
moban.xlsx占位符分析器
重新读取moban.xlsx，识别所有占位符，并根据指定数据源填充
"""

import openpyxl
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime


class MobanAnalyzer:
    """moban.xlsx分析器"""
    
    def __init__(self, moban_path: str, data_path: str):
        self.moban_path = moban_path
        self.data_path = data_path
        self.bill_data = self._load_data()
        self.placeholders_found = []
        
    def _load_data(self) -> Dict[str, Any]:
        """加载数据文件"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 成功加载数据文件: {self.data_path}")
            return data
        except Exception as e:
            print(f"❌ 加载数据文件失败: {e}")
            return {}
    
    def analyze_moban_placeholders(self):
        """分析moban.xlsx中的所有占位符"""
        try:
            workbook = openpyxl.load_workbook(self.moban_path)
            print(f"✅ 成功加载Excel文件: {self.moban_path}")
            
            print(f"\n📋 工作表列表:")
            for i, sheet_name in enumerate(workbook.sheetnames, 1):
                print(f"  {i}. {sheet_name}")
            
            # 分析每个工作表
            for worksheet in workbook.worksheets:
                self._analyze_worksheet_placeholders(worksheet)
            
            # 显示分析结果
            self._show_analysis_results()
            
        except Exception as e:
            print(f"❌ 分析Excel文件失败: {e}")
    
    def _analyze_worksheet_placeholders(self, worksheet):
        """分析单个工作表的占位符"""
        print(f"\n📋 分析工作表: {worksheet.title}")
        
        worksheet_placeholders = []
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 查找各种格式的占位符
                    placeholders = self._find_all_placeholder_types(cell.value)
                    
                    for placeholder in placeholders:
                        placeholder_info = {
                            'worksheet': worksheet.title,
                            'cell': cell.coordinate,
                            'original_text': cell.value,
                            'placeholder': placeholder['full_match'],
                            'field_name': placeholder['field_name'],
                            'type': placeholder['type']
                        }
                        worksheet_placeholders.append(placeholder_info)
                        self.placeholders_found.append(placeholder_info)
        
        if worksheet_placeholders:
            print(f"  找到 {len(worksheet_placeholders)} 个占位符:")
            for ph in worksheet_placeholders:
                print(f"    {ph['cell']}: {ph['placeholder']} ({ph['type']})")
        else:
            print(f"  未找到占位符")
    
    def _find_all_placeholder_types(self, text: str) -> List[Dict[str, str]]:
        """查找所有类型的占位符"""
        placeholders = []
        
        # 1. 标准占位符 ${fieldName}
        pattern1 = r'\$\{([^}]+)\}'
        matches = re.finditer(pattern1, text)
        for match in matches:
            placeholders.append({
                'full_match': match.group(0),
                'field_name': match.group(1),
                'type': 'standard'
            })
        
        # 2. 备选占位符 #{fieldName}
        pattern2 = r'#\{([^}]+)\}'
        matches = re.finditer(pattern2, text)
        for match in matches:
            placeholders.append({
                'full_match': match.group(0),
                'field_name': match.group(1),
                'type': 'alternative'
            })
        
        # 3. 路径占位符 ${billPeriodStatementList[0].fieldName}
        pattern3 = r'\$\{([a-zA-Z_]+)\[(\d+)\]\.([a-zA-Z_]+)\}'
        matches = re.finditer(pattern3, text)
        for match in matches:
            placeholders.append({
                'full_match': match.group(0),
                'field_name': f"{match.group(1)}[{match.group(2)}].{match.group(3)}",
                'type': 'path_indexed'
            })
        
        # 4. 循环占位符 ${@billPeriodDetailList.fieldName}
        pattern4 = r'\$\{@([a-zA-Z_]+)\.([a-zA-Z_]+)\}'
        matches = re.finditer(pattern4, text)
        for match in matches:
            placeholders.append({
                'full_match': match.group(0),
                'field_name': f"@{match.group(1)}.{match.group(2)}",
                'type': 'loop'
            })
        
        # 5. billType特定占位符 ${@long.billPeriodDetailList.fieldName}
        pattern5 = r'\$\{@([a-z]+)\.([a-zA-Z_]+)\.([a-zA-Z_]+)\}'
        matches = re.finditer(pattern5, text)
        for match in matches:
            placeholders.append({
                'full_match': match.group(0),
                'field_name': f"@{match.group(1)}.{match.group(2)}.{match.group(3)}",
                'type': 'billtype_loop'
            })
        
        return placeholders
    
    def _show_analysis_results(self):
        """显示分析结果"""
        print(f"\n📊 占位符分析结果:")
        print(f"   总计找到 {len(self.placeholders_found)} 个占位符")
        
        # 按类型分组
        by_type = {}
        for ph in self.placeholders_found:
            ph_type = ph['type']
            if ph_type not in by_type:
                by_type[ph_type] = []
            by_type[ph_type].append(ph)
        
        for ph_type, placeholders in by_type.items():
            print(f"\n   {ph_type} 类型 ({len(placeholders)}个):")
            for ph in placeholders:
                print(f"     {ph['worksheet']}.{ph['cell']}: {ph['placeholder']}")
    
    def create_data_mapper(self):
        """创建数据映射器"""
        return MobanDataMapper(self.bill_data)
    
    def process_moban_with_data(self, output_path: str):
        """处理moban.xlsx并填充数据"""
        try:
            workbook = openpyxl.load_workbook(self.moban_path)
            data_mapper = self.create_data_mapper()
            
            print(f"\n🔄 开始处理占位符替换...")
            
            total_replaced = 0
            
            for worksheet in workbook.worksheets:
                replaced_count = self._process_worksheet_placeholders(worksheet, data_mapper)
                total_replaced += replaced_count
            
            workbook.save(output_path)
            print(f"✅ 处理完成: {output_path}")
            print(f"📊 共替换 {total_replaced} 个占位符")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def _process_worksheet_placeholders(self, worksheet, data_mapper):
        """处理单个工作表的占位符"""
        print(f"\n📋 处理工作表: {worksheet.title}")
        
        replaced_count = 0
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    original_value = cell.value
                    new_value = self._replace_cell_placeholders(original_value, data_mapper)
                    
                    if new_value != original_value:
                        cell.value = new_value
                        replaced_count += 1
                        print(f"  {cell.coordinate}: {original_value} → {new_value}")
        
        print(f"  替换了 {replaced_count} 个占位符")
        return replaced_count
    
    def _replace_cell_placeholders(self, text: str, data_mapper) -> str:
        """替换单元格中的占位符"""
        result = text
        
        # 1. 替换标准占位符 ${fieldName}
        pattern1 = r'\$\{([^}]+)\}'
        result = re.sub(pattern1, lambda m: str(data_mapper.get_field_value(m.group(1))), result)
        
        # 2. 替换备选占位符 #{fieldName}
        pattern2 = r'#\{([^}]+)\}'
        result = re.sub(pattern2, lambda m: str(data_mapper.get_field_value(m.group(1))), result)
        
        # 3. 替换路径占位符 ${billPeriodStatementList[0].fieldName}
        pattern3 = r'\$\{([a-zA-Z_]+)\[(\d+)\]\.([a-zA-Z_]+)\}'
        result = re.sub(pattern3, lambda m: str(data_mapper.get_path_value(m.group(1), int(m.group(2)), m.group(3))), result)
        
        return result


class MobanDataMapper:
    """moban数据映射器"""
    
    def __init__(self, bill_data: Dict[str, Any]):
        self.bill_data = bill_data
        
        # 状态映射
        self.pay_status_mapping = {
            0: "未支付", 4: "部分支付", 8: "已支付", 
            16: "无需支付", 20: "已支付"
        }
    
    def get_field_value(self, field_name: str) -> Any:
        """获取字段值"""
        try:
            # 基础客户信息
            if field_name == 'customerName':
                return self.bill_data.get('customerName', '')
            elif field_name == 'customerNo':
                return self.bill_data.get('customerNo', '')
            elif field_name == 'customerId':
                return self.bill_data.get('customerId', '')
            
            # 时间信息
            elif field_name == 'currentBillMonth':
                return self._format_date(self.bill_data.get('currentBillMonth', ''))
            elif field_name == 'accountTime':
                return self._format_month(self.bill_data.get('currentBillMonth', ''))
            elif field_name == 'time' or field_name == 'currentTime':
                return self._get_current_time()
            
            # 金额信息
            elif field_name == 'totalAmount' or field_name == 'totalBillAmount':
                return self._get_total_amount('billAmount')
            elif field_name == 'paidAmount' or field_name == 'totalPaidAmount':
                return self._get_total_amount('paidAmount')
            elif field_name == 'unPaidAmount' or field_name == 'totalUnPaidAmount':
                return self._get_total_amount('unPaidAmount')
            elif field_name == 'needAmount':
                return self._get_total_amount('billAmount')
            elif field_name == 'payAmount':
                return self._get_total_amount('paidAmount')
            elif field_name == 'unPayAmount':
                return self._get_total_amount('unPaidAmount')
            
            # 设备金额
            elif field_name == 'newEquipmentAmount':
                return self._get_total_amount('newEquipmentAmount')
            elif field_name == 'oldEquipmentAmount':
                return self._get_total_amount('oldEquipmentAmount')
            elif field_name == 'returnEquipmentAmount':
                return self._get_total_amount('returnEquipmentAmount')
            elif field_name == 'depositEquipmentAmount':
                return self._get_total_amount('depositEquipmentAmount')
            elif field_name == 'otherAmount':
                return self._get_total_amount('otherAmount')
            
            # 收款账户信息
            elif field_name == 'accountName':
                return self.bill_data.get('collectionAccount', {}).get('accountName', '')
            elif field_name == 'accountBank':
                return self.bill_data.get('collectionAccount', {}).get('accountBank', '')
            elif field_name == 'accountNo':
                return self.bill_data.get('collectionAccount', {}).get('accountNo', '')
            
            # 客户账户信息
            elif field_name == 'customerBalanceAmount':
                return self.bill_data.get('customerAccount', {}).get('customerBalanceAmount', 0)
            elif field_name == 'totalNeedPayAmount':
                return self.bill_data.get('customerAccount', {}).get('totalNeedPayAmount', 0)
            
            # 明细信息（取第一条）
            elif field_name == 'productName':
                return self._get_first_detail_field('productName')
            elif field_name == 'categoryName':
                return self._get_first_detail_field('categoryName')
            elif field_name == 'businessOrderNo':
                return self._get_first_detail_field('businessOrderNo')
            elif field_name == 'consigneeName':
                return self._get_first_detail_field('consigneeName')
            elif field_name == 'provinceName':
                return self._get_first_detail_field('provinceName')
            elif field_name == 'cityName':
                return self._get_first_detail_field('cityName')
            elif field_name == 'payStatus':
                status = self._get_first_detail_field('payStatus')
                return self._format_pay_status(status)
            
            else:
                return field_name  # 如果找不到，返回原字段名
                
        except Exception as e:
            print(f"获取字段 {field_name} 时出错: {e}")
            return field_name
    
    def get_path_value(self, list_name: str, index: int, field_name: str) -> Any:
        """获取路径值"""
        try:
            if list_name == 'billPeriodStatementList':
                bill_list = self.bill_data.get('billPeriodStatementList', [])
                if index < len(bill_list):
                    return bill_list[index].get(field_name, '')
            return ''
        except Exception as e:
            print(f"获取路径值 {list_name}[{index}].{field_name} 时出错: {e}")
            return ''
    
    def _get_total_amount(self, field_name: str) -> float:
        """计算总金额"""
        total = 0.0
        bill_list = self.bill_data.get('billPeriodStatementList', [])
        for bill in bill_list:
            value = bill.get(field_name, 0)
            if isinstance(value, (int, float)):
                total += float(value)
        return total
    
    def _get_first_detail_field(self, field_name: str) -> Any:
        """获取第一个明细的字段值"""
        bill_list = self.bill_data.get('billPeriodStatementList', [])
        for bill in bill_list:
            detail_list = bill.get('billPeriodDetailList', [])
            if detail_list:
                return detail_list[0].get(field_name, '')
        return ''
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期"""
        if not date_str:
            return ''
        try:
            if 'T' in date_str:
                return date_str.split('T')[0]
            return date_str
        except:
            return date_str
    
    def _format_month(self, date_str: str) -> str:
        """格式化月份"""
        if not date_str:
            return ''
        try:
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                if '-' in date_part:
                    year, month, day = date_part.split('-')
                    return f"{year}年{month}月"
            return date_str
        except:
            return date_str
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def _format_pay_status(self, status: Any) -> str:
        """格式化支付状态"""
        if isinstance(status, (int, float)):
            return self.pay_status_mapping.get(int(status), f"状态{status}")
        return str(status) if status else ""


def main():
    """主函数"""
    print("🔄 moban.xlsx占位符分析和数据填充")
    print("=" * 60)
    
    # 文件路径
    moban_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\moban.xlsx"
    data_path = "excel_data/bill_data_LXCC-1000-20240115-02686_2025-07.json"
    output_path = "moban_filled_output.xlsx"
    
    # 创建分析器
    analyzer = MobanAnalyzer(moban_path, data_path)
    
    # 分析占位符
    analyzer.analyze_moban_placeholders()
    
    # 处理数据填充
    success = analyzer.process_moban_with_data(output_path)
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📁 输入文件: {moban_path}")
        print(f"📁 数据文件: {data_path}")
        print(f"📁 输出文件: {output_path}")
    else:
        print(f"\n❌ 处理失败！")


if __name__ == "__main__":
    main()
