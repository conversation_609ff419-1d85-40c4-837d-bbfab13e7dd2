"""
统一的moban.xlsx处理器
使用新的测试数据文件 bill_data_LXCC-1000-20241219-03101_2025-06.json
"""

import openpyxl
import json
import re
import os
from typing import Dict, Any, List, Optional
from datetime import datetime
from openpyxl.styles import Font, Border, Alignment, PatternFill, Side
from openpyxl.utils import get_column_letter
from copy import copy

# 导入字段映射定义
try:
    from excel_data.field_mapping import (
        ORDER_STATUS_MAPPING, 
        ASSOCIATION_CREATE_TYPE_MAPPING, 
        RENT_MODE_MAPPING, 
        PAY_STATUS_MAPPING, 
        RENT_SCENE_MAPPING, 
        BILL_PERIOD_FLAG_MAPPING
    )
except ImportError:
    # 如果导入失败，使用默认映射
    ORDER_STATUS_MAPPING = {}
    ASSOCIATION_CREATE_TYPE_MAPPING = {}
    RENT_MODE_MAPPING = {}
    PAY_STATUS_MAPPING = {0: "未支付", 4: "部分支付", 8: "已支付", 16: "无需支付", 20: "已支付"}
    RENT_SCENE_MAPPING = {}
    BILL_PERIOD_FLAG_MAPPING = {}


class UnifiedMobanProcessor:
    """统一的moban处理器"""
    
    def __init__(self, moban_path: str, data_path: str):
        self.moban_path = self._find_file_path(moban_path)
        self.data_path = self._find_file_path(data_path)
        self.bill_data = self._load_data()
        
        # 定义统一的边框样式
        self.THIN_BORDER = Border(
            left=Side(style='thin', color='000000'),
            right=Side(style='thin', color='000000'),
            top=Side(style='thin', color='000000'),
            bottom=Side(style='thin', color='000000')
        )
        
        # billType映射
        self.bill_type_mapping = {
            "BUSINESS_BILL_TYPE_LONG": "long",
            "BUSINESS_BILL_TYPE_SHORT": "short", 
            "BUSINESS_BILL_TYPE_SALE": "sale",
            "BUSINESS_BILL_TYPE_IT": "it",
            "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL": "equipment",
            "BUSINESS_BILL_TYPE_FEES": "fees"
        }
        
        # billType中文描述映射
        self.bill_type_descriptions = {
            "long": "长租账单",
            "short": "短租账单",
            "sale": "销售账单",
            "it": "IT服务账单",
            "equipment": "设备明细",
            "fees": "费用明细"
        }
        
        # 按billType分组数据
        self.bill_groups = self._group_bills_by_type()
    
    def _find_file_path(self, file_path: str) -> str:
        """查找文件的实际路径"""
        possible_paths = [
            file_path,
            os.path.abspath(file_path),
            f"C:/Users/<USER>/PycharmProjects/CustomeExcel/{file_path}",
            os.path.join(os.getcwd(), file_path),
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        return file_path
        
    def _load_data(self) -> Dict[str, Any]:
        """加载数据文件"""
        try:
            print(f"🔍 加载数据文件: {self.data_path}")
            
            if not os.path.exists(self.data_path):
                print(f"❌ 文件不存在: {self.data_path}")
                return {}
            
            with open(self.data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 成功加载数据文件")
            return data
        except Exception as e:
            print(f"❌ 加载数据文件失败: {e}")
            return {}
    
    def _group_bills_by_type(self) -> Dict[str, Dict[str, Any]]:
        """按billType分组账单数据"""
        groups = {}
        
        if not self.bill_data:
            return groups
        
        # 尝试不同的数据结构路径
        bill_list = []
        
        # 路径1: 直接在根级别
        if 'billPeriodStatementList' in self.bill_data:
            bill_list = self.bill_data.get('billPeriodStatementList', [])
            print(f"📊 从根级别找到 {len(bill_list)} 条账单记录")
        
        # 路径2: 在resultMap.data中
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_list = data.get('billPeriodStatementList', [])
            print(f"📊 从resultMap.data找到 {len(bill_list)} 条账单记录")
        
        for bill in bill_list:
            bill_type = bill.get('billType', 'UNKNOWN')
            short_name = self.bill_type_mapping.get(bill_type, bill_type.lower())
            groups[short_name] = bill
            
        return groups
    
    def process_moban(self, output_path: str):
        """处理moban.xlsx"""
        try:
            if not os.path.exists(self.moban_path):
                print(f"❌ Excel模板文件不存在: {self.moban_path}")
                return False
                
            workbook = openpyxl.load_workbook(self.moban_path)
            print(f"✅ 成功加载Excel文件: {self.moban_path}")
            
            # 显示数据统计
            self._show_data_statistics()
            
            # 删除没有数据的工作表
            self._remove_empty_worksheets(workbook)
            
            # 处理所有工作表
            self._process_all_worksheets(workbook)
            
            # 确保输出路径可写
            if os.path.exists(output_path):
                try:
                    os.remove(output_path)
                except:
                    base, ext = os.path.splitext(output_path)
                    output_path = f"{base}_{datetime.now().strftime('%H%M%S')}{ext}"
            
            workbook.save(output_path)
            print(f"✅ 处理完成: {output_path}")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def _process_all_worksheets(self, workbook):
        """处理所有工作表，根据工作表名称自动选择处理方式"""
        print(f"\n📋 开始处理所有工作表")
        print(f"📊 找到 {len(workbook.sheetnames)} 个工作表: {workbook.sheetnames}")
        
        for worksheet in workbook.worksheets:
            worksheet_name = worksheet.title
            print(f"\n--- 处理工作表: {worksheet_name} ---")
            
            # 检查是否是特殊处理的工作表（子类可以重写此方法）
            if self._is_special_worksheet(worksheet_name):
                # 特殊处理
                self._process_special_worksheet(worksheet, worksheet_name)
            else:
                # 标准处理
                self._process_worksheet(worksheet)
    
    def _is_special_worksheet(self, worksheet_name: str) -> bool:
        """
        判断是否是特殊处理的工作表
        子类可以重写此方法来定义特殊工作表
        """
        # 默认没有特殊工作表，所有工作表都使用标准处理
        return False
    
    def _process_special_worksheet(self, worksheet, worksheet_name: str):
        """
        处理特殊工作表
        子类可以重写此方法来实现特殊处理逻辑
        """
        # 默认实现：使用标准处理
        self._process_worksheet(worksheet)
    
    def _show_data_statistics(self):
        """显示数据统计"""
        print(f"\n📊 数据统计:")

        # 尝试从不同位置获取客户信息
        customer_name = self._get_customer_info('customerName')
        current_bill_month = self._get_customer_info('currentBillMonth')

        print(f"   客户名称: {customer_name}")
        print(f"   账单月份: {self._format_timestamp(current_bill_month)}")

        for bill_type, bill_data in self.bill_groups.items():
            excel_detail_count = len(bill_data.get('excelBillPeriodDetailList', []))
            detail_count = len(bill_data.get('billPeriodDetailList', []))
            print(f"   {bill_type}: excelBillPeriodDetailList={excel_detail_count}条, billPeriodDetailList={detail_count}条")

    def _get_customer_info(self, field_name: str) -> str:
        """获取客户信息"""
        # 尝试不同的路径
        paths = [
            self.bill_data.get(field_name),  # 根级别
            self.bill_data.get('resultMap', {}).get('data', {}).get(field_name),  # resultMap.data中
            self.bill_data.get('data', {}).get(field_name),  # data中
        ]

        for value in paths:
            if value is not None:
                return value

        return 'N/A'
    
    def _get_bill_date_of_payment(self) -> Any:
        """获取账单付款日期"""
        # 尝试从不同路径获取 billDateOfPayment
        paths = [
            self.bill_data.get('billDateOfPayment'),
            self.bill_data.get('resultMap', {}).get('data', {}).get('billDateOfPayment'),
            self.bill_data.get('data', {}).get('billDateOfPayment')
        ]
        
        for value in paths:
            if value is not None:
                return value
        
        return None
    
    def _remove_empty_worksheets(self, workbook):
        """删除没有数据的工作表"""
        try:
            available_bill_types = self._get_available_bill_types()
            print(f"\n🗂️  检查工作表是否有对应数据:")
            print(f"  可用的账单类型: {available_bill_types}")
            
            # 账单类型与工作表名称的映射关系
            worksheet_mappings = {
                'long': ['长租', '长租账单', '长租明细', 'long'],
                'short': ['短租', '短租账单', '短租明细', 'short'], 
                'sale': ['销售', '销售账单', '销售明细', 'sale'],
                'it': ['IT', 'IT服务', 'IT账单', 'IT明细', 'it'],
                'equipment': ['设备', '设备明细', '设备账单', 'equipment'],
                'fees': ['费用', '费用明细', '费用账单', 'fees']
            }
            
            worksheets_to_remove = []
            
            for worksheet in list(workbook.worksheets):
                worksheet_name = worksheet.title
                should_remove = False
                matched_bill_type = None
                
                # 检查工作表是否对应某个账单类型
                for bill_type, patterns in worksheet_mappings.items():
                    for pattern in patterns:
                        if pattern in worksheet_name:
                            matched_bill_type = bill_type
                            # 如果该账单类型没有数据，标记为删除
                            if bill_type not in available_bill_types:
                                should_remove = True
                                print(f"  ❌ 工作表 '{worksheet_name}' 对应账单类型 '{bill_type}' 无数据，将被删除")
                            else:
                                print(f"  ✅ 工作表 '{worksheet_name}' 对应账单类型 '{bill_type}' 有数据，保留")
                            break
                    if matched_bill_type:
                        break
                
                if not matched_bill_type:
                    print(f"  ℹ️  工作表 '{worksheet_name}' 不对应特定账单类型，保留")
                
                if should_remove:
                    worksheets_to_remove.append(worksheet)
            
            # 删除没有数据的工作表
            for worksheet in worksheets_to_remove:
                print(f"  🗑️  删除工作表: {worksheet.title}")
                workbook.remove(worksheet)
                
            if worksheets_to_remove:
                print(f"  ✅ 共删除 {len(worksheets_to_remove)} 个工作表")
            else:
                print(f"  ✅ 无需删除工作表")
                
        except Exception as e:
            print(f"  ❌ 删除空工作表时出错: {e}")

    def _format_timestamp(self, timestamp) -> str:
        """格式化时间戳"""
        if not timestamp or timestamp == 'N/A':
            return 'N/A'

        try:
            if isinstance(timestamp, (int, float)):
                dt = datetime.fromtimestamp(timestamp / 1000)
                return dt.strftime("%Y-%m-%d")
            return str(timestamp)
        except:
            return str(timestamp)
    
    def _process_worksheet(self, worksheet):
        """处理单个工作表"""
        print(f"\n📋 处理工作表: {worksheet.title}")
        
        try:
            # 如果是账单总览工作表，先处理账单类型行的删除
            if self._is_bill_summary_worksheet(worksheet):
                self._process_bill_summary_rows(worksheet)
            
            # 查找循环模板行
            loop_templates = self._find_loop_templates(worksheet)
            
            if loop_templates:
                print(f"  找到 {len(loop_templates)} 个循环模板")
                
                # 从后往前处理，避免行号变化影响
                for template_info in reversed(loop_templates):
                    self._process_loop_template(worksheet, template_info)
            
            # 处理非循环占位符
            self._process_non_loop_placeholders(worksheet)
            
        except Exception as e:
            print(f"  ❌ 处理工作表 {worksheet.title} 时出错: {e}")
    
    def _find_loop_templates(self, worksheet) -> List[Dict[str, Any]]:
        """查找循环模板行"""
        templates = []
        
        for row_num in range(1, worksheet.max_row + 1):
            for col_num in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                if cell.value and isinstance(cell.value, str):
                    # 查找循环占位符 ${@billType.listName.fieldName}
                    match = re.search(r'\$\{@([a-z]+)\.([a-zA-Z_]+)\.([a-zA-Z_]+)\}', cell.value)
                    if match:
                        bill_type = match.group(1)
                        list_name = match.group(2)
                        
                        # 检查是否已经记录了这一行
                        existing = next((t for t in templates if t['row_num'] == row_num), None)
                        if not existing:
                            templates.append({
                                'row_num': row_num,
                                'bill_type': bill_type,
                                'list_name': list_name,
                                'template_data': self._get_row_data_with_styles(worksheet, row_num)
                            })
                        break
        
        return templates
    
    def _get_row_data_with_styles(self, worksheet, row_num: int) -> List[Dict[str, Any]]:
        """获取指定行的数据和样式"""
        row_data = []
        for col_num in range(1, worksheet.max_column + 1):
            cell = worksheet.cell(row=row_num, column=col_num)
            
            try:
                cell_info = {
                    'value': str(cell.value) if cell.value is not None else "",
                    'font': copy(cell.font) if cell.font else None,
                    'border': copy(cell.border) if cell.border else None,
                    'fill': copy(cell.fill) if cell.fill else None,
                    'alignment': copy(cell.alignment) if cell.alignment else None,
                    'number_format': cell.number_format,
                    'row_height': worksheet.row_dimensions[row_num].height,
                    'col_width': worksheet.column_dimensions[openpyxl.utils.get_column_letter(col_num)].width
                }
            except Exception as e:
                cell_info = {
                    'value': str(cell.value) if cell.value is not None else "",
                    'font': None, 'border': None, 'fill': None, 'alignment': None,
                    'number_format': None, 'row_height': None, 'col_width': None
                }
            
            row_data.append(cell_info)
        
        return row_data
    
    def _process_loop_template(self, worksheet, template_info: Dict[str, Any]):
        """处理循环模板"""
        try:
            row_num = template_info['row_num']
            bill_type = template_info['bill_type']
            list_name = template_info['list_name']
            template_data = template_info['template_data']
            
            # 获取循环数据
            loop_data = self._get_loop_data(bill_type, list_name)
            
            print(f"  处理第{row_num}行循环模板: {bill_type}.{list_name} ({len(loop_data)}条数据)")
            
            if len(loop_data) > 0:
                print(f"  将复制样式到新增的 {len(loop_data)} 行")
            
            # 获取原始行高
            original_row_height = worksheet.row_dimensions[row_num].height
            
            # 删除模板行
            worksheet.delete_rows(row_num)
            
            # 为每条数据创建新行，保持样式
            current_row = row_num
            for i, data_item in enumerate(loop_data):
                try:
                    self._create_styled_loop_row(worksheet, current_row, template_data, data_item, bill_type, list_name, original_row_height)
                    current_row += 1
                except Exception as e:
                    print(f"    警告: 创建第{i+1}行数据时出错: {e}")
                    current_row += 1
                    
        except Exception as e:
            print(f"  ❌ 处理循环模板时出错: {e}")
    
    def _create_styled_loop_row(self, worksheet, row_num: int, template_data: List[Dict[str, Any]],
                               data_item: Dict[str, Any], bill_type: str, list_name: str, row_height: float):
        """创建带样式的循环行"""

        # 调试：检查目标订单的数据
        if data_item.get('businessOrderNo') == 'LXO-20250721-4001-00314':
            print(f"    🎯 处理目标订单行数据 (第{row_num}行):")
            print(f"      businessOrderNo: {data_item.get('businessOrderNo')}")
            print(f"      rentStartTime: {data_item.get('rentStartTime', 'N/A')}")
            print(f"      rentEndTime: {data_item.get('rentEndTime', 'N/A')}")
            print(f"      rentMode: {data_item.get('rentMode', 'N/A')}")
            print(f"      rentAmount: {data_item.get('rentAmount', 'N/A')}")
            print(f"      rentPeriod: {data_item.get('rentPeriod', 'N/A')}")
            print(f"      rentUnitPrice: {data_item.get('rentUnitPrice', 'N/A')}")

        try:
            if row_height:
                worksheet.row_dimensions[row_num].height = row_height
        except:
            pass
        
        # 第一步：分析模板行中的占位符，确定需要合并的列
        merge_column_info = self._analyze_template_placeholders(template_data, bill_type, list_name)
        
        # 第二步：应用合并逻辑（在设置单元格值之前）
        try:
            self._apply_cell_merge_logic(worksheet, data_item, row_num, merge_column_info)
        except Exception as e:
            print(f"      警告: 应用合并逻辑时出错: {e}")
        
        # 第三步：设置单元格值和样式
        for col_num, cell_info in enumerate(template_data, 1):
            try:
                cell = worksheet.cell(row=row_num, column=col_num)
                
                # 检查单元格是否属于合并区域，如果是，找到起始单元格
                target_cell = self._get_merge_start_cell(worksheet, cell)
                
                # 设置值（使用target_cell）
                if cell_info['value']:
                    new_value = self._replace_loop_placeholders(cell_info['value'], data_item, bill_type, list_name)
                    target_cell.value = new_value
                
                # 复制样式（使用target_cell）
                if cell_info['font']:
                    target_cell.font = cell_info['font']
                if cell_info['border']:
                    target_cell.border = cell_info['border']
                if cell_info['fill']:
                    target_cell.fill = cell_info['fill']
                if cell_info['alignment']:
                    target_cell.alignment = cell_info['alignment']
                if cell_info['number_format']:
                    target_cell.number_format = cell_info['number_format']
                
                # 设置列宽
                col_letter = openpyxl.utils.get_column_letter(col_num)
                if cell_info['col_width']:
                    worksheet.column_dimensions[col_letter].width = cell_info['col_width']
                    
            except Exception as e:
                print(f"      警告: 设置单元格 {row_num},{col_num} 时出错: {e}")
        
        # 无需再次应用合并逻辑
    
    def _get_loop_data(self, bill_type: str, list_name: str) -> List[Dict[str, Any]]:
        """获取循环数据"""
        if bill_type in self.bill_groups:
            bill_data = self.bill_groups[bill_type]
            return bill_data.get(list_name, [])
        return []
    
    def _replace_loop_placeholders(self, text: str, data_item: Dict[str, Any], 
                                  bill_type: str, list_name: str) -> str:
        """替换循环占位符"""
        try:
            text = str(text) if text is not None else ""
            pattern = rf'\$\{{@{bill_type}\.{list_name}\.([a-zA-Z_]+)\}}'
            
            def replace_func(match):
                try:
                    field_name = match.group(1)
                    value = data_item.get(field_name, '')

                    # 调试：检查目标订单的字段替换
                    if data_item.get('businessOrderNo') == 'LXO-20250721-4001-00314' and field_name in ['rentStartTime', 'rentEndTime', 'rentMode', 'rentAmount', 'rentPeriod', 'rentUnitPrice']:
                        print(f"      🔄 替换字段 {field_name}: {value}")

                    if field_name == 'payStatus':
                        return self._format_pay_status(value)
                    elif field_name == 'serialNumberSet':
                        # 处理设备序列号集合：将列表转换为逗号分隔的字符串
                        return self._format_serial_number_set(value)
                    elif field_name == 'orderStatus':
                        # 订单状态映射
                        return self._format_order_status(value)
                    elif field_name == 'associationCreateType':
                        # 关联创建类型映射
                        return self._format_association_create_type(value)
                    elif field_name == 'rentMode':
                        # 租赁方式映射（优先使用 rentModeDesc）
                        return self._format_rent_mode(data_item.get('rentModeDesc'), value)
                    elif field_name == 'isNew':
                        # 成色映射
                        return self._format_is_new(value)
                    elif field_name == 'billPeriodFlag':
                        # 账单标志映射（需要整个data_item进行复杂判断）
                        return self._format_bill_period_flag(data_item)
                    elif field_name == 'isRelet':
                        # 是否续租
                        return "是" if value == 1 else "否"
                    elif field_name in ['billAmount', 'billStatementAmount', 'billReturnStatementAmount', 
                                      'paidAmount', 'returnAmount']:
                        # 标准金额字段：保持数值格式，保留2位小数
                        return self._format_amount_field(value, False)
                    elif field_name in ['correctAmount', 'couponAmount']:
                        # 需要取负值的金额字段：调整金额、优惠金额
                        return self._format_amount_field(value, True)
                    elif field_name == 'payInfo':
                        # 支付说明字段：返回字符串，空值处理
                        return str(value) if value else ""
                    elif 'Amount' in field_name:
                        # 其他金额字段的通用处理
                        return self._format_amount_field(value, False)
                    elif 'Time' in field_name and value:
                        formatted_result = self._format_date(value)
                        # 调试：检查时间字段的格式化结果
                        if data_item.get('businessOrderNo') == 'LXO-20250721-4001-00314' and field_name in ['rentStartTime', 'rentEndTime']:
                            print(f"      📅 格式化时间字段 {field_name}: {value} -> {formatted_result}")
                        return formatted_result
                    
                    return str(value) if value is not None else ""
                except Exception as e:
                    return match.group(0)
            
            result = re.sub(pattern, replace_func, text)
            return result
            
        except Exception as e:
            return str(text) if text is not None else ""
    
    def _process_non_loop_placeholders(self, worksheet):
        """处理非循环占位符"""
        replaced_count = 0
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    try:
                        original_value = cell.value
                        new_value = self._replace_standard_placeholders(original_value)
                        
                        if new_value != original_value:
                            cell.value = new_value
                            replaced_count += 1
                    except Exception as e:
                        pass
        
        if replaced_count > 0:
            print(f"  替换了 {replaced_count} 个非循环占位符")
    
    def _replace_standard_placeholders(self, text: str) -> str:
        """替换标准占位符"""
        result = text
        result = re.sub(r'\$\{([^}]+)\}', self._replace_field_placeholder, result)
        result = re.sub(r'#\{([^}]+)\}', self._replace_field_placeholder, result)
        return result
    
    def _replace_field_placeholder(self, match) -> str:
        """替换字段占位符"""
        field_name = match.group(1)
        value = self._get_field_value(field_name)
        return str(value) if value is not None else match.group(0)
    
    def _get_field_value(self, field_name: str) -> Any:
        """获取字段值"""
        try:
            if field_name == 'customerName':
                return self._get_customer_info('customerName')
            elif field_name == 'customerNo':
                return self._get_customer_info('customerNo')
            elif field_name == 'currentBillMonth':
                # 尝试从 billDateOfPayment 字段获取账单月份
                timestamp = self._get_bill_date_of_payment()
                return self._format_timestamp_to_chinese_month(timestamp)
            elif field_name == 'accountTime':
                timestamp = self._get_customer_info('currentBillMonth')
                return self._format_timestamp_to_month(timestamp)
            elif field_name in ['time', 'currentTime']:
                return self._get_current_time()
            elif field_name in ['totalAmount', 'totalBillAmount', 'needAmount']:
                return self._get_total_amount('billAmount')
            elif field_name in ['paidAmount', 'totalPaidAmount', 'payAmount']:
                return self._get_total_amount('paidAmount')
            elif field_name in ['unPaidAmount', 'totalUnPaidAmount', 'unPayAmount', 'totalUnpayAmount']:
                return self._get_total_amount('unPaidAmount')
            elif field_name.startswith('shroffAccount.'):
                # 处理 shroffAccount.accountName, shroffAccount.accountBank, shroffAccount.accountNo
                account_field = field_name.split('.', 1)[1]
                return self._get_account_info(account_field)
            elif field_name.startswith('collectionAccount.'):
                # 处理 collectionAccount.accountName, collectionAccount.accountBank, collectionAccount.accountNo
                account_field = field_name.split('.', 1)[1]
                return self._get_collection_account_info(account_field)
            elif field_name.startswith('customerAccount.'):
                # 处理 customerAccount.totalUnPaidAmount 等
                customer_field = field_name.split('.', 1)[1]
                return self._get_customer_account_info(customer_field)
            elif field_name == 'histioryTotalUnpayAmount':
                # 计算历史未付金额 = customerAccount.totalUnPaidAmount - totalUnPaidAmount
                return self._calculate_history_unpaid_amount()
            elif '.' in field_name:
                return self._get_billtype_field_value(field_name)
            elif field_name == 'accountName':
                return self._get_account_info('accountName')
            elif field_name == 'accountBank':
                return self._get_account_info('accountBank')
            elif field_name == 'accountNo':
                return self._get_account_info('accountNo')
            else:
                return field_name
        except Exception as e:
            return field_name

    def _get_account_info(self, field_name: str) -> str:
        """获取收款账户信息（shroffAccount）"""
        # 尝试不同的路径，主要是 shroffAccount
        paths = [
            # shroffAccount 路径
            self.bill_data.get('shroffAccount', {}).get(field_name),
            self.bill_data.get('resultMap', {}).get('data', {}).get('shroffAccount', {}).get(field_name),
            self.bill_data.get('data', {}).get('shroffAccount', {}).get(field_name),
        ]

        for value in paths:
            if value is not None:
                return str(value)

        # 如果都没找到，打印调试信息
        print(f"  警告: 找不到shroffAccount字段 '{field_name}'")
        return ''
    
    def _get_collection_account_info(self, field_name: str) -> str:
        """获取收款账户信息（collectionAccount）"""
        # 尝试不同的路径，主要是 collectionAccount
        paths = [
            # collectionAccount 路径
            self.bill_data.get('collectionAccount', {}).get(field_name),
            self.bill_data.get('resultMap', {}).get('data', {}).get('collectionAccount', {}).get(field_name),
            self.bill_data.get('data', {}).get('collectionAccount', {}).get(field_name),
        ]

        for value in paths:
            if value is not None:
                return str(value)

        # 如果都没找到，打印调试信息
        print(f"  警告: 找不到collectionAccount字段 '{field_name}'")
        return ''
    
    def _get_customer_account_info(self, field_name: str) -> Any:
        """获取客户账户信息"""
        # 尝试不同的路径
        paths = [
            self.bill_data.get('customerAccount', {}).get(field_name),
            self.bill_data.get('resultMap', {}).get('data', {}).get('customerAccount', {}).get(field_name),
            self.bill_data.get('data', {}).get('customerAccount', {}).get(field_name),
        ]

        for value in paths:
            if value is not None:
                # 对于金额类字段，格式化为浮点数
                if 'Amount' in field_name or 'amount' in field_name:
                    try:
                        return float(value)
                    except (ValueError, TypeError):
                        return value
                return value

        # 如果都没找到，打印调试信息
        print(f"  警告: 找不到客户账户字段 '{field_name}'")
        return 0 if 'Amount' in field_name or 'amount' in field_name else ''
    
    def _calculate_history_unpaid_amount(self) -> float:
        """计算历史未付金额 = customerAccount.totalUnPaidAmount - totalUnPaidAmount"""
        try:
            # 获取客户账户总未付金额
            customer_total_unpaid = self._get_customer_account_info('totalUnPaidAmount')
            
            # 获取当前账单总未付金额
            current_total_unpaid = self._get_total_amount('unPaidAmount')
            
            # 计算历史未付金额
            history_unpaid = float(customer_total_unpaid) - float(current_total_unpaid)
            
            # 保留两位小数
            history_unpaid = round(history_unpaid, 2)
            
            print(f"  历史未付金额计算: {customer_total_unpaid} - {current_total_unpaid} = {history_unpaid}")
            
            return history_unpaid
        except Exception as e:
            print(f"  警告: 计算历史未付金额失败: {e}")
            return 0.0
    
    def _get_billtype_field_value(self, field_name: str) -> Any:
        """获取billType特定字段值"""
        try:
            parts = field_name.split('.')
            if len(parts) >= 2:
                bill_type = parts[0]
                field = '.'.join(parts[1:])
                
                if bill_type in self.bill_groups:
                    bill_data = self.bill_groups[bill_type]
                    
                    if field == 'billType':
                        # 返回中文描述而不是大写的英文
                        return self.bill_type_descriptions.get(bill_type, bill_type.upper())
                    elif field in bill_data:
                        return bill_data[field]
            
            return field_name
        except Exception as e:
            return field_name
    
    def _get_total_amount(self, field_name: str) -> float:
        """计算总金额"""
        total = 0.0
        for bill_type, bill_data in self.bill_groups.items():
            value = bill_data.get(field_name, 0)
            if isinstance(value, (int, float)):
                total += float(value)
        return total
    
    def _get_available_bill_types(self) -> set:
        """获取有数据的账单类型"""
        available_types = set()
        
        # 从 billPeriodStatementList 中获取有数据的账单类型
        bill_list = []
        
        # 尝试不同的数据结构路径
        if 'billPeriodStatementList' in self.bill_data:
            bill_list = self.bill_data.get('billPeriodStatementList', [])
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_list = data.get('billPeriodStatementList', [])
        
        for bill in bill_list:
            bill_type = bill.get('billType', '')
            if bill_type:
                # 转换为短名称
                short_name = self.bill_type_mapping.get(bill_type, bill_type.lower())
                available_types.add(short_name)
        
        return available_types
    
    def _is_bill_summary_worksheet(self, worksheet) -> bool:
        """判断是否是账单总览工作表"""
        # 通过工作表名称判断
        title = worksheet.title.lower()
        return '总览' in title or 'summary' in title or '汇总' in title
    
    def _process_bill_summary_rows(self, worksheet):
        """处理账单总览工作表的账单类型行"""
        try:
            available_bill_types = self._get_available_bill_types()
            print(f"  可用的账单类型: {available_bill_types}")
            
            # 定义所有可能的账单类型占位符模式
            bill_type_patterns = [
                ('long', ['${long.billType}', '长租账单', '长租']),
                ('short', ['${short.billType}', '短租账单', '短租']),
                ('sale', ['${sale.billType}', '销售账单', '销售']),
                ('it', ['${it.billType}', 'IT服务账单', 'IT服务']),
                ('equipment', ['${equipment.billType}', '设备明细', '设备'])
            ]
            
            # 记录需要处理的账单类型行
            bill_type_rows = {}
            
            # 扫描所有行，识别账单类型行
            for row_num in range(1, min(worksheet.max_row + 1, 11)):  # 限制在前10行内
                row_has_bill_type = False
                bill_type_found = None
                
                for col_num in range(1, worksheet.max_column + 1):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    if cell.value and isinstance(cell.value, str):
                        cell_value = cell.value.strip()
                        
                        # 检查是否包含账单类型占位符或关键词
                        for bill_type, patterns in bill_type_patterns:
                            for pattern in patterns:
                                if pattern in cell_value:
                                    row_has_bill_type = True
                                    bill_type_found = bill_type
                                    break
                            if row_has_bill_type:
                                break
                    
                    if row_has_bill_type:
                        break
                
                # 记录账单类型行
                if row_has_bill_type:
                    bill_type_rows[row_num] = {
                        'bill_type': bill_type_found,
                        'has_data': bill_type_found in available_bill_types
                    }
            
            # 处理每个账单类型行的占位符
            for row_num, row_info in bill_type_rows.items():
                bill_type = row_info['bill_type']
                has_data = row_info['has_data']
                
                if has_data:
                    print(f"  ✅ 第{row_num}行 ({bill_type}) 有数据，正常填充")
                    # 正常处理占位符
                    self._process_bill_type_row_placeholders(worksheet, row_num, bill_type, True)
                else:
                    print(f"  📝 第{row_num}行 ({bill_type}) 无数据，填充空值")
                    # 将占位符替换为空值
                    self._process_bill_type_row_placeholders(worksheet, row_num, bill_type, False)
            
        except Exception as e:
            print(f"  ❌ 处理账单总览行时出错: {e}")
    
    def _process_bill_type_row_placeholders(self, worksheet, row_num: int, bill_type: str, has_data: bool):
        """处理账单类型行的占位符"""
        try:
            for col_num in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                if cell.value and isinstance(cell.value, str):
                    original_value = cell.value
                    
                    if has_data:
                        # 有数据时正常替换占位符
                        new_value = self._replace_bill_type_placeholders(original_value, bill_type)
                    else:
                        # 无数据时将占位符替换为空值
                        new_value = self._replace_bill_type_placeholders_with_empty(original_value, bill_type)
                    
                    if new_value != original_value:
                        cell.value = new_value
                        
        except Exception as e:
            print(f"    处理第{row_num}行占位符时出错: {e}")
    
    def _replace_bill_type_placeholders(self, text: str, bill_type: str) -> str:
        """替换账单类型占位符（有数据的情况）"""
        try:
            # 替换 ${billType.field} 格式的占位符
            pattern = rf'\$\{{{bill_type}\.([a-zA-Z_]+)\}}'
            
            def replace_func(match):
                field_name = match.group(1)
                if field_name == 'billType':
                    # 返回中文描述
                    return self.bill_type_descriptions.get(bill_type, bill_type.upper())
                else:
                    # 从对应的账单数据中获取字段值
                    if bill_type in self.bill_groups:
                        bill_data = self.bill_groups[bill_type]
                        value = bill_data.get(field_name, '')
                        if field_name in ['billAmount', 'paidAmount', 'unPaidAmount'] and isinstance(value, (int, float)):
                            return str(value)
                        return str(value) if value is not None else ''
                    return ''
            
            result = re.sub(pattern, replace_func, text)
            return result
            
        except Exception as e:
            return text
    
    def _replace_bill_type_placeholders_with_empty(self, text: str, bill_type: str) -> str:
        """替换账单类型占位符为空值（无数据的情况）"""
        try:
            # 替换所有 ${billType.field} 格式的占位符为空字符串
            pattern = rf'\$\{{{bill_type}\.([a-zA-Z_]+)\}}'
            result = re.sub(pattern, '', text)
            
            # 替换可能的固定文本（如果整行都是占位符）
            if text.strip() == f'${{{bill_type}.billType}}':
                return ''
            
            return result
            
        except Exception as e:
            return text
    
    def _format_timestamp_to_date(self, timestamp) -> str:
        """将时间戳格式化为日期"""
        if not timestamp or timestamp == 'N/A':
            return ''
        try:
            if isinstance(timestamp, (int, float)):
                dt = datetime.fromtimestamp(timestamp / 1000)
                return dt.strftime("%Y-%m-%d")
            elif isinstance(timestamp, str) and 'T' in timestamp:
                return timestamp.split('T')[0]
            return str(timestamp)
        except:
            return str(timestamp) if timestamp else ""

    def _format_timestamp_to_month(self, timestamp) -> str:
        """将时间戳格式化为月份"""
        if not timestamp or timestamp == 'N/A':
            return ''
        try:
            if isinstance(timestamp, (int, float)):
                dt = datetime.fromtimestamp(timestamp / 1000)
                return dt.strftime("%Y年%m月")
            elif isinstance(timestamp, str) and 'T' in timestamp:
                date_part = timestamp.split('T')[0]
                if '-' in date_part:
                    year, month, day = date_part.split('-')
                    return f"{year}年{month}月"
            return str(timestamp)
        except:
            return str(timestamp) if timestamp else ""

    def _format_date(self, date_str: str) -> str:
        """格式化日期（兼容旧方法）"""
        return self._format_timestamp_to_date(date_str)

    def _format_month(self, date_str: str) -> str:
        """格式化月份（兼容旧方法）"""
        return self._format_timestamp_to_month(date_str)
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def _format_timestamp_to_chinese_month(self, timestamp: Any) -> str:
        """将时间戳转换为中文月份格式 (YYYY年MM月)"""
        try:
            if timestamp is None:
                return ""
            
            # 处理Unix时间戳（毫秒）
            if isinstance(timestamp, (int, float)):
                # 如果是毫秒时间戳，转换为秒
                if timestamp > 1e10:  # 毫秒时间戳
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                return dt.strftime("%Y年%m月")
            
            # 处理ISO日期字符串
            if isinstance(timestamp, str):
                # 处理 "2025-07-01T00:00:00" 格式
                if "T" in timestamp:
                    dt = datetime.fromisoformat(timestamp.replace("Z", "").replace("T", " "))
                    return dt.strftime("%Y年%m月")
                # 处理 "2025-07-01" 格式
                elif "-" in timestamp:
                    dt = datetime.strptime(timestamp, "%Y-%m-%d")
                    return dt.strftime("%Y年%m月")
            
            return str(timestamp)
        except Exception as e:
            print(f"    时间戳格式化错误: {e}")
            return str(timestamp) if timestamp else ""
    
    def _format_pay_status(self, status: Any) -> str:
        """格式化支付状态"""
        try:
            if isinstance(status, (int, float)):
                return PAY_STATUS_MAPPING.get(int(status), f"状态{status}")
            return str(status) if status else ""
        except:
            return str(status) if status else ""
    
    def _format_order_status(self, status: Any) -> str:
        """格式化订单状态"""
        try:
            if isinstance(status, (int, float)):
                return ORDER_STATUS_MAPPING.get(int(status), f"未知({status})")
            return str(status) if status else ""
        except:
            return str(status) if status else ""
    
    def _format_association_create_type(self, assoc_type: Any) -> str:
        """格式化关联创建类型"""
        try:
            if assoc_type is None or assoc_type == '':
                return ""
            if isinstance(assoc_type, (int, float)):
                return ASSOCIATION_CREATE_TYPE_MAPPING.get(int(assoc_type), f"未知({assoc_type})")
            return str(assoc_type)
        except:
            return str(assoc_type) if assoc_type else ""
    
    def _format_rent_mode(self, rent_mode_desc: Any, rent_mode: Any) -> str:
        """格式化租赁方式（优先使用描述，否则使用映射）"""
        try:
            # 优先使用 rentModeDesc
            if rent_mode_desc:
                return str(rent_mode_desc)
            
            # 否则使用映射
            if isinstance(rent_mode, (int, float)):
                return RENT_MODE_MAPPING.get(int(rent_mode), '其他')
            
            return str(rent_mode) if rent_mode else "其他"
        except:
            return "其他"
    
    def _format_is_new(self, is_new: Any) -> str:
        """格式化成色"""
        try:
            if isinstance(is_new, (int, float)):
                return "全新" if int(is_new) == 1 else "次新"
            return str(is_new) if is_new else "次新"
        except:
            return "次新"
    
    def _format_bill_period_flag(self, detail: Dict[str, Any]) -> str:
        """格式化账单标志显示，根据复杂逻辑处理"""
        try:
            bill_period_flag = detail.get('billPeriodFlag') or "BILL_PERIOD_FLAG_OLD"
            
            if bill_period_flag == "BILL_PERIOD_FLAG_NEW":
                # 新增设备，需要检查是否改配
                if detail.get('isChangeAllocation'):
                    if detail.get('isChangeAllocation') == 1:  # COMMON_CONSTANT_YES
                        return "改配新增"
                    elif detail.get('isChangeAllocation') == 2:  # COMMON_TWO
                        return "维修换货新增"
                    else:
                        return "新增设备"
                else:
                    return "新增设备"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_DEPOSIT":
                return "押金金额"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_OLD":
                return "往期设备"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_RETURN":
                # 退货，需要检查退货单创建类型
                return_order_create_type = detail.get('returnOrderCreateType')
                if return_order_create_type:
                    if return_order_create_type == 4:  # RETURN_ORDER_SOURCE_CHANGE_ALLOCATION
                        return "改配退回"
                    elif (return_order_create_type == 6 or  # RETURN_ORDER_SOURCE_REPAIR_REPLACE1
                          return_order_create_type == 7):   # RETURN_ORDER_SOURCE_REPAIR_REPLACE2
                        return "维修换货退回"
                    else:
                        return "退回设备"
                else:
                    return "退回设备"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_ADJUST":
                return "调整项"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_OTHER":
                return "其他费用"
            
            elif bill_period_flag == "BILL_PERIOD_FLAG_IT_SERVICE":
                return "服务金额"
            
            else:
                return BILL_PERIOD_FLAG_MAPPING.get(bill_period_flag, "往期设备")
                
        except Exception as e:
            return "往期设备"
    
    def _format_serial_number_set(self, serial_numbers: Any) -> str:
        """格式化设备序列号集合"""
        try:
            # 如果是列表或类似的可迭代对象
            if hasattr(serial_numbers, '__iter__') and not isinstance(serial_numbers, str):
                # 过滤掉 None 值，转换为字符串，并用逗号连接
                valid_numbers = [str(item) for item in serial_numbers if item is not None]
                return ', '.join(valid_numbers)
            # 如果是单个值
            elif serial_numbers is not None:
                return str(serial_numbers)
            # 如果是 None 或空
            else:
                return ""
        except Exception as e:
            # 出错时返回原始值的字符串形式
                         return str(serial_numbers) if serial_numbers is not None else ""
    
    def _analyze_template_placeholders(self, template_data: List[Dict[str, Any]], bill_type: str, list_name: str) -> Dict[str, List[int]]:
        """分析模板行中的占位符，确定需要合并的列"""
        merge_info = {
            'order_merge_columns': [],    # 订单信息合并列（应付金额、支付状态）
            'pay_info_merge_columns': []  # 支付信息合并列（出账金额等）
        }
        
        # 定义需要合并的占位符模式
        order_merge_fields = ['billAmount', 'payStatus']  # 订单信息合并字段
        pay_info_merge_fields = ['billStatementAmount', 'billReturnStatementAmount', 'correctAmount', 
                               'couponAmount', 'paidAmount', 'returnAmount', 'payInfo']  # 支付信息合并字段
        
        # 分析每一列的占位符
        for col_num, cell_info in enumerate(template_data, 1):
            cell_value = str(cell_info.get('value', ''))
            
            if cell_value and '${@' in cell_value:
                # 提取占位符中的字段名
                import re
                pattern = rf'\$\{{@{bill_type}\.{list_name}\.([a-zA-Z_]+)\}}'
                matches = re.findall(pattern, cell_value)
                
                for field_name in matches:
                    # 检查是否是订单信息合并字段
                    if field_name in order_merge_fields:
                        if col_num not in merge_info['order_merge_columns']:
                            merge_info['order_merge_columns'].append(col_num)
                    
                    # 检查是否是支付信息合并字段
                    if field_name in pay_info_merge_fields:
                        if col_num not in merge_info['pay_info_merge_columns']:
                            merge_info['pay_info_merge_columns'].append(col_num)
        
        return merge_info
    
    def _format_amount_field(self, value: Any, negate: bool = False) -> str:
        """格式化金额字段"""
        try:
            if value is None or value == '':
                return "0"
            
            # 转换为浮点数
            if isinstance(value, (int, float)):
                float_value = float(value)
            else:
                float_value = float(str(value))
            
            # 如果需要取负值（调整金额、优惠金额）
            if negate:
                # 取绝对值后取负值
                float_value = -abs(float_value)
            
            # 智能格式化：如果是整数则显示整数，否则最多保留2位小数
            if float_value == int(float_value):
                # 是整数，直接显示整数
                return str(int(float_value))
            else:
                # 有小数，保留2位小数后去掉末尾的0
                formatted = f"{float_value:.2f}"
                # 去掉末尾的0和小数点（如果小数部分全是0）
                # 如果小数部分不是.00，则保留两位小数，否则显示整数
                if formatted.endswith('.00'):
                    formatted = str(int(float_value))
                return formatted
            
        except (ValueError, TypeError):
            # 转换失败时返回原始值的字符串形式
            return str(value) if value is not None else "0"
    
    def _get_merge_start_cell(self, worksheet, cell):
        """获取合并区域的起始单元格，如果单元格不在合并区域中，返回原单元格"""
        try:
            # 检查单元格是否在合并区域中
            for merged_range in worksheet.merged_cells.ranges:
                if cell.coordinate in merged_range:
                    # 找到合并区域，返回起始单元格
                    start_cell = worksheet.cell(row=merged_range.min_row, column=merged_range.min_col)
                    return start_cell
            # 如果不在合并区域中，返回原单元格
            return cell
        except:
            # 如果出现任何错误，返回原单元格
            return cell
    
    def _unmerge_cells_if_exists(self, worksheet, col, start_row, end_row):
        """如果存在合并区域，先取消合并"""
        try:
            # 检查是否存在包含指定范围的合并区域
            ranges_to_remove = []
            for merged_range in worksheet.merged_cells.ranges:
                # 检查是否与指定范围有重叠
                if (merged_range.min_col <= col <= merged_range.max_col and 
                    merged_range.min_row <= end_row and merged_range.max_row >= start_row):
                    ranges_to_remove.append(merged_range)
            
            # 取消找到的合并区域
            for range_to_remove in ranges_to_remove:
                worksheet.unmerge_cells(str(range_to_remove))
        except:
            # 如果出现任何错误，忽略
            pass
    
    def _ensure_merge_cell_border(self, worksheet, col, start_row, end_row):
        """确保合并单元格有正确的边框"""
        try:
            # 简化策略：只确保主单元格（起始单元格）有正确的边框
            main_cell = worksheet.cell(row=start_row, column=col)
            main_cell.border = self.THIN_BORDER
                
        except Exception as e:
            print(f"    设置合并边框失败: {e}")

    def _apply_cell_merge_logic(self, worksheet, detail, current_row, merge_column_info):
        """应用单元格合并逻辑"""
        try:
            # 从占位符分析结果获取需要合并的列
            order_merge_columns = merge_column_info.get('order_merge_columns', [])
            pay_info_merge_columns = merge_column_info.get('pay_info_merge_columns', [])
            
            # 处理订单信息合并逻辑
            if (detail.get('orderItemMerge') and detail.get('firstOrderItemMergeRecord')):
                
                merge_row_count = detail.get('orderItemMergeRowCount', 1)
                if merge_row_count > 1 and order_merge_columns:
                    print(f"    应用订单合并: 起始行{current_row}, 合并{merge_row_count}行, 列{order_merge_columns}, 产品: {detail.get('productName', 'N/A')}")
                    
                    # 对订单信息相关列进行合并
                    for col in order_merge_columns:
                        start_row = current_row
                        end_row = current_row + merge_row_count - 1
                        
                        # 先取消可能存在的合并
                        self._unmerge_cells_if_exists(worksheet, col, start_row, end_row)
                        
                        # 确保主单元格有正确的边框（在合并前）
                        self._ensure_merge_cell_border(worksheet, col, start_row, end_row)
                        
                        # 合并单元格
                        worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
            
            # 处理支付信息相关列合并逻辑
            if (detail.get('payInfoMerge') and detail.get('firstPayInfoMergeRecord')):
                
                pay_info_merge_row_count = detail.get('payInfoMergeRowCount', 1)
                if pay_info_merge_row_count > 1 and pay_info_merge_columns:
                    print(f"    应用支付信息合并: 起始行{current_row}, 合并{pay_info_merge_row_count}行, 列{pay_info_merge_columns}")
                    
                    # 对支付信息相关列进行合并
                    for col in pay_info_merge_columns:
                        start_row = current_row
                        end_row = current_row + pay_info_merge_row_count - 1
                        
                        # 先取消可能存在的合并
                        self._unmerge_cells_if_exists(worksheet, col, start_row, end_row)
                        
                        # 确保主单元格有正确的边框（在合并前）
                        self._ensure_merge_cell_border(worksheet, col, start_row, end_row)
                        
                        # 合并单元格
                        worksheet.merge_cells(f'{get_column_letter(col)}{start_row}:{get_column_letter(col)}{end_row}')
            
        except Exception as e:
            print(f"    警告: 应用合并逻辑时出错: {e}")

 
def main():
    """主函数"""
    print("🔄 统一的moban.xlsx处理器")
    print("=" * 60)
    print("📁 使用新的测试数据文件: bill_data_LXCC-1000-20241219-03101_2025-06.json")
    
    # 文件路径
    moban_path = "moban_enhanced.xlsx"
    data_path = "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json"
    
    # 生成带时间戳的输出文件名
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"unified_moban_output_{current_time}.xlsx"
    
    # 创建处理器
    processor = UnifiedMobanProcessor(moban_path, data_path)
    
    if processor.bill_data:
        # 处理数据填充
        success = processor.process_moban(output_path)
        
        if success:
            print(f"\n🎉 处理完成！")
            print(f"📁 Excel模板: {processor.moban_path}")
            print(f"📁 数据文件: {processor.data_path}")
            print(f"📁 输出文件: {output_path}")
        else:
            print(f"\n❌ 处理失败！")
    else:
        print(f"\n❌ 数据文件加载失败！")


if __name__ == "__main__":
    main()
