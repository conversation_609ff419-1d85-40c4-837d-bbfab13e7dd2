"""
moban.xlsx占位符增强器
根据multi_sheet_excel_write.py中的字段映射逻辑，为moban.xlsx补充缺失的占位符
"""

import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import os


class MobanPlaceholderEnhancer:
    """moban.xlsx占位符增强器"""
    
    def __init__(self, moban_path: str):
        self.moban_path = moban_path
        self.workbook = None
        
        # 根据multi_sheet_excel_write.py定义的完整字段映射
        self.complete_field_mapping = {
            # 账单信息（1-15列）
            'bill_info': [
                "businessOrderNo",      # 单号
                "rentStartTime",        # 起租时间
                "categoryName",         # 类别
                "productName",          # 商品名
                "productSkuName",       # 配置/详情
                "unitAmount",           # 单价（元）
                "count",                # 数量
                "periodStartTime",      # 本期开始日
                "periodEndTime",        # 本期结束日
                "phase",                # 期数
                "serialNumberSet",      # 设备序列号
                "billAmount",           # 应付金额
                "payStatus",            # 支付状态
                "isRelet",              # 是否续租
                "billPeriodFlag"        # 账单标识
            ],
            # 应付信息（16-22列）
            'payment_info': [
                "billStatementAmount",      # 出账金额
                "billReturnStatementAmount", # 退租金额
                "correctAmount",            # 调整金额
                "couponAmount",             # 优惠金额
                "paidAmount",               # 已付金额
                "returnAmount",             # 已退押金
                "payInfo"                   # 说明
            ],
            # 客户信息（23-28列）
            'customer_info': [
                "consigneeName",        # 收件人
                "provinceName",         # 省
                "cityName",             # 市
                "districtName",         # 区
                "address",              # 详细地址
                "customerSubName"       # 分子公司
            ],
            # 订单信息（29-32列）
            'order_info': [
                "orderStatus",          # 订单状态
                "associationCreateType", # 关联信息
                "originOrderNo",        # 原订单
                "returnTime"            # 退租日期
            ],
            # 商品信息（33-35列）
            'product_info': [
                "isNew",                # 成色
                "rentMode",             # 租赁方式
                "rentSceneMark"         # 备注
            ],
            # 使用信息（36-38列）
            'usage_info': [
                "customerUser",         # 使用人
                "customerOrganization", # 使用组织
                "customerRemark"        # 使用备注
            ]
        }
        
        # IT服务账单的字段映射（简化版）
        self.it_service_field_mapping = {
            # 账单信息字段（主要字段）
            'bill_info': [
                "businessOrderNo",      # 单号
                "rentStartTime",        # 日期
                "productName",          # 名称
                "categoryName",         # 类别
                "unitAmount",           # 单价(元)
                "count",                # 数量
                "billAmount",           # 应付金额
                "payStatus",            # 支付状态
                "isRelet",              # 是否续租
                "billPeriodFlag"        # 账单标识
            ],
            # 应付信息字段
            'payment_info': [
                "billStatementAmount",      # 出账金额
                "billReturnStatementAmount", # 退租金额
                "correctAmount",            # 调整金额
                "couponAmount",             # 优惠金额
                "paidAmount",               # 已付金额
                "returnAmount",             # 已退押金
                "payInfo"                   # 说明
            ],
            # 客户信息字段
            'customer_info': [
                "consigneeName",        # 收件人
                "provinceName",         # 省
                "cityName",             # 市
                "districtName",         # 区
                "address",              # 详细地址
                "customerSubName"       # 分子公司
            ]
        }
    
    def load_workbook(self):
        """加载工作簿"""
        try:
            self.workbook = openpyxl.load_workbook(self.moban_path)
            print(f"✅ 成功加载工作簿: {self.moban_path}")
            return True
        except Exception as e:
            print(f"❌ 加载工作簿失败: {e}")
            return False
    
    def enhance_worksheet_placeholders(self, worksheet_name: str, bill_type: str):
        """增强指定工作表的占位符"""
        if worksheet_name not in self.workbook.sheetnames:
            print(f"❌ 工作表 {worksheet_name} 不存在")
            return False
        
        worksheet = self.workbook[worksheet_name]
        print(f"\n📋 增强工作表: {worksheet_name}")
        
        # 根据工作表类型选择字段映射
        if worksheet_name == "IT服务账单明细":
            field_mapping = self.it_service_field_mapping
        else:
            field_mapping = self.complete_field_mapping
        
        # 查找循环占位符行（通常在第6行）
        loop_row = self._find_loop_placeholder_row(worksheet, bill_type)
        
        if loop_row:
            print(f"  找到循环占位符行: 第{loop_row}行")
            self._enhance_loop_placeholders(worksheet, loop_row, bill_type, field_mapping)
        else:
            print(f"  未找到循环占位符行，创建新的占位符行")
            self._create_loop_placeholder_row(worksheet, bill_type, field_mapping)
        
        return True
    
    def _find_loop_placeholder_row(self, worksheet, bill_type: str) -> int:
        """查找循环占位符行"""
        for row_num in range(1, worksheet.max_row + 1):
            for col_num in range(1, worksheet.max_column + 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                if cell.value and isinstance(cell.value, str):
                    if f"@{bill_type}.excelBillPeriodDetailList" in cell.value:
                        return row_num
        return None
    
    def _enhance_loop_placeholders(self, worksheet, row_num: int, bill_type: str, field_mapping: dict):
        """增强循环占位符行"""
        col_num = 1
        
        # 遍历所有字段分组
        for group_name, fields in field_mapping.items():
            for field in fields:
                placeholder = f"${{@{bill_type}.excelBillPeriodDetailList.{field}}}"
                
                cell = worksheet.cell(row=row_num, column=col_num)
                
                # 如果单元格为空或者不包含占位符，则添加占位符
                if not cell.value or not isinstance(cell.value, str) or '@' not in cell.value:
                    cell.value = placeholder
                    print(f"    添加占位符 {get_column_letter(col_num)}{row_num}: {placeholder}")
                
                col_num += 1
    
    def _create_loop_placeholder_row(self, worksheet, bill_type: str, field_mapping: dict):
        """创建新的循环占位符行"""
        # 在第6行创建占位符行
        row_num = 6
        col_num = 1
        
        # 遍历所有字段分组
        for group_name, fields in field_mapping.items():
            for field in fields:
                placeholder = f"${{@{bill_type}.excelBillPeriodDetailList.{field}}}"
                
                cell = worksheet.cell(row=row_num, column=col_num)
                cell.value = placeholder
                print(f"    创建占位符 {get_column_letter(col_num)}{row_num}: {placeholder}")
                
                col_num += 1
    
    def enhance_account_overview_placeholders(self):
        """增强账单总览工作表的占位符"""
        if "账单总览" not in self.workbook.sheetnames:
            print("❌ 账单总览工作表不存在")
            return False
        
        worksheet = self.workbook["账单总览"]
        print(f"\n📋 增强工作表: 账单总览")
        
        # 补充缺失的占位符
        missing_placeholders = [
            # 基础信息
            ("B2", "${customerName}"),
            ("B3", "${currentBillMonth}"),
            
            # 金额信息
            ("D5", "${needAmount}"),
            ("F5", "${payAmount}"),
            ("H5", "${unPayAmount}"),
            
            # 时间和账户信息
            ("F8", "${time}"),
            ("G9", "${totalUnpayAmount}"),
            ("C9", "${accountName}"),
            ("C10", "${accountBank}"),
            ("C11", "${accountNo}"),
            
            # 各类型账单金额
            ("D6", "${short.billAmount}"),
            ("F6", "${short.paidAmount}"),
            ("H6", "${short.unPaidAmount}"),
            
            ("D7", "${long.billAmount}"),
            ("F7", "${long.paidAmount}"),
            ("H7", "${long.unPaidAmount}"),
            
            ("D8", "${sale.billAmount}"),
            ("F8", "${sale.paidAmount}"),
            ("H8", "${sale.unPaidAmount}"),
            
            ("D9", "${it.billAmount}"),
            ("F9", "${it.paidAmount}"),
            ("H9", "${it.unPaidAmount}"),
        ]
        
        for cell_ref, placeholder in missing_placeholders:
            try:
                cell = worksheet[cell_ref]
                # 检查是否是合并单元格
                if hasattr(cell, 'value') and not isinstance(cell, openpyxl.cell.MergedCell):
                    if not cell.value or not isinstance(cell.value, str) or '${' not in cell.value:
                        cell.value = placeholder
                        print(f"    添加占位符 {cell_ref}: {placeholder}")
                else:
                    print(f"    跳过合并单元格 {cell_ref}")
            except Exception as e:
                print(f"    警告: 处理单元格 {cell_ref} 时出错: {e}")
        
        return True
    
    def enhance_it_service_worksheet(self):
        """增强IT服务账单明细工作表"""
        if "IT服务账单明细" not in self.workbook.sheetnames:
            print("❌ IT服务账单明细工作表不存在")
            return False
        
        worksheet = self.workbook["IT服务账单明细"]
        print(f"\n📋 增强工作表: IT服务账单明细")
        
        # 添加基础占位符
        basic_placeholders = [
            ("A1", "${customerName}\n${currentBillMonth}-${it.billType}"),
            ("A2", "${currentBillMonth}账单应付总金额：${it.billAmount}元，已付：${it.paidAmount}元，未付金额：${it.unPaidAmount}元"),
            ("A4", "${it.billAmount}"),
            ("C4", "${it.paidAmount}"),
            ("E4", "${it.unPaidAmount}"),
        ]
        
        for cell_ref, placeholder in basic_placeholders:
            try:
                cell = worksheet[cell_ref]
                if hasattr(cell, 'value') and not isinstance(cell, openpyxl.cell.MergedCell):
                    if not cell.value or not isinstance(cell.value, str) or '${' not in cell.value:
                        cell.value = placeholder
                        print(f"    添加占位符 {cell_ref}: {placeholder}")
                else:
                    print(f"    跳过合并单元格 {cell_ref}")
            except Exception as e:
                print(f"    警告: 处理单元格 {cell_ref} 时出错: {e}")
        
        # 添加循环占位符
        self.enhance_worksheet_placeholders("IT服务账单明细", "it")
        
        return True
    
    def enhance_equipment_detail_worksheet(self):
        """增强租赁设备明细工作表"""
        if "租赁设备明细" not in self.workbook.sheetnames:
            print("❌ 租赁设备明细工作表不存在")
            return False
        
        worksheet = self.workbook["租赁设备明细"]
        print(f"\n📋 增强工作表: 租赁设备明细")
        
        # 添加基础占位符
        basic_placeholders = [
            ("A1", "${customerName}\n${currentBillMonth}-租赁设备明细"),
            ("A2", "${currentBillMonth}期初设备：${initialCount}台，退回设备：${returnCount}台，租赁中设备：${rentingCount}台"),
        ]
        
        for cell_ref, placeholder in basic_placeholders:
            try:
                cell = worksheet[cell_ref]
                if hasattr(cell, 'value') and not isinstance(cell, openpyxl.cell.MergedCell):
                    if not cell.value or not isinstance(cell.value, str) or '${' not in cell.value:
                        cell.value = placeholder
                        print(f"    添加占位符 {cell_ref}: {placeholder}")
                else:
                    print(f"    跳过合并单元格 {cell_ref}")
            except Exception as e:
                print(f"    警告: 处理单元格 {cell_ref} 时出错: {e}")
        
        # 设备明细字段（根据multi_sheet_excel_write.py中的设备明细字段）
        equipment_fields = [
            "consigneeName", "provinceName", "cityName", "districtName", "address", "customerSubName",
            "brandName", "categoryName", "productName", "isNew", "rentModeDesc", "productSkuName",
            "serialNumberSet", "count", "productSkuPrice", "rentStatus", "orderNo", "rentStartTime",
            "expectReturnTime", "returnTime", "rentLengthType"
        ]
        
        # 在第4行添加设备明细循环占位符
        row_num = 4
        for col_num, field in enumerate(equipment_fields, 1):
            placeholder = f"${{@equipment.accountOrderMonthEquipmentDetailList.{field}}}"
            cell = worksheet.cell(row=row_num, column=col_num)
            if not cell.value or not isinstance(cell.value, str) or '@' not in cell.value:
                cell.value = placeholder
                print(f"    添加占位符 {get_column_letter(col_num)}{row_num}: {placeholder}")
        
        return True
    
    def save_enhanced_workbook(self, output_path: str = None):
        """保存增强后的工作簿"""
        if not output_path:
            base, ext = os.path.splitext(self.moban_path)
            output_path = f"{base}_enhanced{ext}"
        
        try:
            self.workbook.save(output_path)
            print(f"\n✅ 增强后的工作簿已保存: {output_path}")
            return True
        except Exception as e:
            print(f"\n❌ 保存工作簿失败: {e}")
            return False
    
    def enhance_all_worksheets(self):
        """增强所有工作表的占位符"""
        if not self.load_workbook():
            return False
        
        # 增强账单总览
        self.enhance_account_overview_placeholders()
        
        # 增强各类型账单明细工作表
        bill_types = [
            ("长租账单明细", "long"),
            ("短租账单明细", "short"),
            ("销售账单明细", "sale"),
        ]
        
        for worksheet_name, bill_type in bill_types:
            self.enhance_worksheet_placeholders(worksheet_name, bill_type)
        
        # 增强IT服务账单明细
        self.enhance_it_service_worksheet()
        
        # 增强租赁设备明细
        self.enhance_equipment_detail_worksheet()
        
        # 保存增强后的工作簿
        return self.save_enhanced_workbook()


def main():
    """主函数"""
    print("🔧 moban.xlsx占位符增强器")
    print("=" * 60)
    print("📋 根据multi_sheet_excel_write.py中的字段映射逻辑补充缺失的占位符")
    
    moban_path = "moban.xlsx"
    
    # 创建增强器
    enhancer = MobanPlaceholderEnhancer(moban_path)
    
    # 增强所有工作表
    success = enhancer.enhance_all_worksheets()
    
    if success:
        print(f"\n🎉 占位符增强完成！")
        print(f"📁 原文件: {moban_path}")
        print(f"📁 增强文件: moban_enhanced.xlsx")
        print(f"\n💡 现在可以使用增强后的模板进行数据填充了！")
    else:
        print(f"\n❌ 占位符增强失败！")


if __name__ == "__main__":
    main()
