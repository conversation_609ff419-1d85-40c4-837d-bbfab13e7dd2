"""
增强的moban.xlsx处理器
支持更完整的占位符映射，包括billType特定的字段和循环占位符
"""

import openpyxl
import json
import re
from typing import Dict, Any, List, Optional
from datetime import datetime


class EnhancedMobanProcessor:
    """增强的moban处理器"""
    
    def __init__(self, moban_path: str, data_path: str):
        self.moban_path = moban_path
        self.data_path = data_path
        self.bill_data = self._load_data()
        
        # billType映射
        self.bill_type_mapping = {
            "BUSINESS_BILL_TYPE_LONG": "long",
            "BUSINESS_BILL_TYPE_SHORT": "short", 
            "BUSINESS_BILL_TYPE_SALE": "sale",
            "BUSINESS_BILL_TYPE_IT": "it",
            "BUSINESS_BILL_TYPE_EQUIPMENT_DETAIL": "equipment",
            "BUSINESS_BILL_TYPE_FEES": "fees"
        }
        
        # 按billType分组数据
        self.bill_groups = self._group_bills_by_type()
        
    def _load_data(self) -> Dict[str, Any]:
        """加载数据文件"""
        try:
            with open(self.data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"✅ 成功加载数据文件: {self.data_path}")
            return data
        except Exception as e:
            print(f"❌ 加载数据文件失败: {e}")
            return {}
    
    def _group_bills_by_type(self) -> Dict[str, Dict[str, Any]]:
        """按billType分组账单数据"""
        groups = {}
        bill_list = self.bill_data.get('billPeriodStatementList', [])
        
        for bill in bill_list:
            bill_type = bill.get('billType', 'UNKNOWN')
            short_name = self.bill_type_mapping.get(bill_type, bill_type.lower())
            groups[short_name] = bill
            
        return groups
    
    def process_moban_enhanced(self, output_path: str):
        """增强处理moban.xlsx"""
        try:
            workbook = openpyxl.load_workbook(self.moban_path)
            print(f"✅ 成功加载Excel文件: {self.moban_path}")
            
            total_replaced = 0
            
            for worksheet in workbook.worksheets:
                replaced_count = self._process_worksheet_enhanced(worksheet)
                total_replaced += replaced_count
            
            workbook.save(output_path)
            print(f"✅ 处理完成: {output_path}")
            print(f"📊 共替换 {total_replaced} 个占位符")
            
            return True
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def _process_worksheet_enhanced(self, worksheet):
        """增强处理单个工作表"""
        print(f"\n📋 处理工作表: {worksheet.title}")
        
        replaced_count = 0
        
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    original_value = cell.value
                    new_value = self._replace_all_placeholders(original_value)
                    
                    if new_value != original_value:
                        cell.value = new_value
                        replaced_count += 1
                        print(f"  {cell.coordinate}: {original_value[:50]}... → {new_value[:50]}...")
        
        print(f"  替换了 {replaced_count} 个占位符")
        return replaced_count
    
    def _replace_all_placeholders(self, text: str) -> str:
        """替换所有类型的占位符"""
        result = text
        
        # 1. 标准占位符 ${fieldName}
        result = re.sub(r'\$\{([^}]+)\}', self._replace_standard_placeholder, result)
        
        # 2. 备选占位符 #{fieldName}
        result = re.sub(r'#\{([^}]+)\}', self._replace_standard_placeholder, result)
        
        return result
    
    def _replace_standard_placeholder(self, match) -> str:
        """替换标准占位符"""
        field_name = match.group(1)
        value = self._get_field_value(field_name)
        return str(value) if value is not None else match.group(0)
    
    def _get_field_value(self, field_name: str) -> Any:
        """获取字段值"""
        try:
            # 基础客户信息
            if field_name == 'customerName':
                return self.bill_data.get('customerName', '')
            elif field_name == 'customerNo':
                return self.bill_data.get('customerNo', '')
            elif field_name == 'customerId':
                return self.bill_data.get('customerId', '')
            
            # 时间信息
            elif field_name == 'currentBillMonth':
                return self._format_date(self.bill_data.get('currentBillMonth', ''))
            elif field_name == 'accountTime':
                return self._format_month(self.bill_data.get('currentBillMonth', ''))
            elif field_name == 'time' or field_name == 'currentTime':
                return self._get_current_time()
            
            # 总计金额信息
            elif field_name in ['totalAmount', 'totalBillAmount', 'needAmount']:
                return self._get_total_amount('billAmount')
            elif field_name in ['paidAmount', 'totalPaidAmount', 'payAmount']:
                return self._get_total_amount('paidAmount')
            elif field_name in ['unPaidAmount', 'totalUnPaidAmount', 'unPayAmount', 'totalUnpayAmount']:
                return self._get_total_amount('unPaidAmount')
            
            # 总计设备金额
            elif field_name == 'newEquipmentAmount':
                return self._get_total_amount('newEquipmentAmount')
            elif field_name == 'oldEquipmentAmount':
                return self._get_total_amount('oldEquipmentAmount')
            elif field_name == 'returnEquipmentAmount':
                return self._get_total_amount('returnEquipmentAmount')
            elif field_name == 'depositEquipmentAmount':
                return self._get_total_amount('depositEquipmentAmount')
            elif field_name == 'otherAmount':
                return self._get_total_amount('otherAmount')
            
            # billType特定字段 (如 long.billAmount)
            elif '.' in field_name:
                return self._get_billtype_field_value(field_name)
            
            # 收款账户信息
            elif field_name == 'accountName':
                return self.bill_data.get('collectionAccount', {}).get('accountName', '')
            elif field_name == 'accountBank':
                return self.bill_data.get('collectionAccount', {}).get('accountBank', '')
            elif field_name == 'accountNo':
                return self.bill_data.get('collectionAccount', {}).get('accountNo', '')
            
            # 客户账户信息
            elif field_name == 'customerBalanceAmount':
                return self.bill_data.get('customerAccount', {}).get('customerBalanceAmount', 0)
            elif field_name == 'totalNeedPayAmount':
                return self.bill_data.get('customerAccount', {}).get('totalNeedPayAmount', 0)
            
            # 明细信息（取第一条）
            elif field_name == 'productName':
                return self._get_first_detail_field('productName')
            elif field_name == 'categoryName':
                return self._get_first_detail_field('categoryName')
            elif field_name == 'businessOrderNo':
                return self._get_first_detail_field('businessOrderNo')
            elif field_name == 'consigneeName':
                return self._get_first_detail_field('consigneeName')
            elif field_name == 'provinceName':
                return self._get_first_detail_field('provinceName')
            elif field_name == 'cityName':
                return self._get_first_detail_field('cityName')
            elif field_name == 'payStatus':
                status = self._get_first_detail_field('payStatus')
                return self._format_pay_status(status)
            
            else:
                return field_name  # 如果找不到，返回原字段名
                
        except Exception as e:
            print(f"获取字段 {field_name} 时出错: {e}")
            return field_name
    
    def _get_billtype_field_value(self, field_name: str) -> Any:
        """获取billType特定字段值"""
        try:
            parts = field_name.split('.')
            if len(parts) >= 2:
                bill_type = parts[0]
                field = '.'.join(parts[1:])
                
                # 处理循环占位符 @long.excelBillPeriodDetailList.fieldName
                if field_name.startswith('@'):
                    return self._handle_loop_placeholder(field_name)
                
                # 处理billType字段 long.billAmount
                if bill_type in self.bill_groups:
                    bill_data = self.bill_groups[bill_type]
                    
                    if field == 'billType':
                        return bill_type.upper()
                    elif field in bill_data:
                        return bill_data[field]
                    else:
                        # 尝试从明细中获取
                        detail_list = bill_data.get('billPeriodDetailList', [])
                        if detail_list and field in detail_list[0]:
                            return detail_list[0][field]
            
            return field_name
            
        except Exception as e:
            print(f"获取billType字段 {field_name} 时出错: {e}")
            return field_name
    
    def _handle_loop_placeholder(self, field_name: str) -> str:
        """处理循环占位符"""
        # 对于循环占位符，返回第一条数据或提示信息
        if 'excelBillPeriodDetailList' in field_name:
            parts = field_name.split('.')
            if len(parts) >= 3:
                bill_type = parts[0][1:]  # 去掉@符号
                list_name = parts[1]
                field = parts[2]
                
                if bill_type in self.bill_groups:
                    bill_data = self.bill_groups[bill_type]
                    detail_list = bill_data.get('billPeriodDetailList', [])
                    if detail_list:
                        value = detail_list[0].get(field, '')
                        if field == 'payStatus':
                            return self._format_pay_status(value)
                        elif field == 'rentStartTime':
                            return self._format_date(value)
                        return str(value)
        
        return f"[循环数据:{field_name}]"
    
    def _get_total_amount(self, field_name: str) -> float:
        """计算总金额"""
        total = 0.0
        bill_list = self.bill_data.get('billPeriodStatementList', [])
        for bill in bill_list:
            value = bill.get(field_name, 0)
            if isinstance(value, (int, float)):
                total += float(value)
        return total
    
    def _get_first_detail_field(self, field_name: str) -> Any:
        """获取第一个明细的字段值"""
        bill_list = self.bill_data.get('billPeriodStatementList', [])
        for bill in bill_list:
            detail_list = bill.get('billPeriodDetailList', [])
            if detail_list:
                return detail_list[0].get(field_name, '')
        return ''
    
    def _format_date(self, date_str: str) -> str:
        """格式化日期"""
        if not date_str:
            return ''
        try:
            if 'T' in date_str:
                return date_str.split('T')[0]
            return date_str
        except:
            return date_str
    
    def _format_month(self, date_str: str) -> str:
        """格式化月份"""
        if not date_str:
            return ''
        try:
            if 'T' in date_str:
                date_part = date_str.split('T')[0]
                if '-' in date_part:
                    year, month, day = date_part.split('-')
                    return f"{year}年{month}月"
            return date_str
        except:
            return date_str
    
    def _get_current_time(self) -> str:
        """获取当前时间"""
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def _format_pay_status(self, status: Any) -> str:
        """格式化支付状态"""
        status_mapping = {0: "未支付", 4: "部分支付", 8: "已支付", 16: "无需支付", 20: "已支付"}
        if isinstance(status, (int, float)):
            return status_mapping.get(int(status), f"状态{status}")
        return str(status) if status else ""


def main():
    """主函数"""
    print("🔄 增强的moban.xlsx处理器")
    print("=" * 60)
    
    # 文件路径
    moban_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\moban.xlsx"
    data_path = "excel_data/bill_data_LXCC-1000-20240115-02686_2025-07.json"
    output_path = "moban_enhanced_output.xlsx"
    
    # 创建处理器
    processor = EnhancedMobanProcessor(moban_path, data_path)
    
    # 处理数据填充
    success = processor.process_moban_enhanced(output_path)
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📁 输入文件: {moban_path}")
        print(f"📁 数据文件: {data_path}")
        print(f"📁 输出文件: {output_path}")
        
        print(f"\n📊 数据统计:")
        print(f"   客户名称: {processor.bill_data.get('customerName', 'N/A')}")
        print(f"   账单月份: {processor.bill_data.get('currentBillMonth', 'N/A')}")
        print(f"   账单数量: {len(processor.bill_data.get('billPeriodStatementList', []))}")
        
        # 显示billType分布
        print(f"\n📋 billType分布:")
        for bill_type, bill_data in processor.bill_groups.items():
            detail_count = len(bill_data.get('billPeriodDetailList', []))
            print(f"   {bill_type}: {detail_count}条明细")
    else:
        print(f"\n❌ 处理失败！")


if __name__ == "__main__":
    main()
