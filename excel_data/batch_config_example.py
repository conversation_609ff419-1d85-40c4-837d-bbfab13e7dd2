"""
批量工作表配置使用示例
演示如何使用WorksheetConfig对象批量处理多个工作表
"""

from template_enhancer_v2 import TemplateEnhancerV2, WorksheetConfig
import os


def main():
    """主函数 - 演示批量配置的使用"""
    
    # 创建模板增强器
    enhancer = TemplateEnhancerV2()
    
    print("🚀 批量工作表配置示例")
    print("=" * 60)
    
    # 创建工作表配置列表
    worksheet_configs = [
        # 长租账单配置
        WorksheetConfig(
            worksheet_name="长租账单-往期",
            header_row_num=1,
            data_start_row_num=2
        ),
        WorksheetConfig(
            worksheet_name="长租账单-新增", 
            header_row_num=1,
            data_start_row_num=2
        ),

        
        # 设备明细配置
        WorksheetConfig(
            worksheet_name="租赁设备明细",
            header_row_num=3,
            data_start_row_num=4   # 数据从第3行开始
        )

    ]
    
    print(f"📋 配置了 {len(worksheet_configs)} 个工作表:")
    for i, config in enumerate(worksheet_configs, 1):
        print(f"   {i}. {config.worksheet_name} (标题行:{config.header_row_num}, 数据行:{config.data_start_row_num})")
    
    # 模板文件路径
    template_path = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC_test.xlsx"
    
    if os.path.exists(template_path):
        print(f"\n📄 开始处理模板: {template_path}")
        
        # 使用配置对象批量处理
        success = enhancer.enhance_worksheets_with_configs(
            template_path=template_path,
            worksheet_configs=worksheet_configs,
            output_path="batch_enhanced_template.xlsx"
        )
        
        if success:
            print(f"\n✅ 批量处理成功完成!")
        else:
            print(f"\n❌ 批量处理失败")
    else:
        print(f"\n⚠️  模板文件不存在: {template_path}")
        print(f"请确保模板文件路径正确")


def create_config_from_list():
    """从列表创建配置的辅助函数"""
    
    # 可以定义一个简单的配置列表
    config_list = [
        ("长租账单-往期", 1, 2),
        ("长租账单-新增", 1, 2), 
        ("短租账单明细", 2, 3),
        ("设备明细", 1, 3)
    ]
    
    # 转换为WorksheetConfig对象
    configs = [
        WorksheetConfig(name, header_row, data_row)
        for name, header_row, data_row in config_list
    ]
    
    return configs


def validate_configs(configs):
    """验证配置的有效性"""
    print(f"\n🔍 验证配置...")
    
    for i, config in enumerate(configs, 1):
        try:
            # 验证参数（WorksheetConfig的__post_init__会自动验证）
            print(f"   ✅ 配置{i}: {config.worksheet_name} - 验证通过")
        except ValueError as e:
            print(f"   ❌ 配置{i}: {config.worksheet_name} - 验证失败: {e}")
            return False
    
    return True


if __name__ == "__main__":
    main()
    
    # 演示其他用法
    print(f"\n" + "="*60)
    print("💡 其他用法示例:")
    
    # 从列表创建配置
    print(f"\n1. 从列表创建配置:")
    configs = create_config_from_list()
    for config in configs:
        print(f"   - {config.worksheet_name}")
    
    # 验证配置
    print(f"\n2. 验证配置:")
    validate_configs(configs)
    
    print(f"\n📖 使用方法总结:")
    print(f"   1. 创建WorksheetConfig对象列表")
    print(f"   2. 调用enhancer.enhance_worksheets_with_configs()")
    print(f"   3. 系统会自动遍历处理每个配置的工作表") 