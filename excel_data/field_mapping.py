"""
Excel列和字段对应关系映射
基于 convert.mdc 文档
"""

from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class ExcelColumnMapping:
    """Excel列映射信息"""
    column_name: str  # Excel列名
    field_name: str   # 数据字段名
    data_type: str    # 数据类型
    description: str  # 说明
    special_handling: str = ""  # 特殊处理


class BillDataFieldMapping:
    """账单数据字段映射"""
    
    # 账单信息（主要字段）
    BILL_INFO_COLUMNS = [
        ExcelColumnMapping("单号", "businessOrderNo", "String", "业务订单号"),
        ExcelColumnMapping("起租时间", "rentStartTime", "Date", "租赁开始时间"),
        ExcelColumnMapping("类别", "categoryName", "String", "商品类别名称"),
        ExcelColumnMapping("商品名", "productName", "String", "商品名称"),
        ExcelColumnMapping("配置/详情", "productSkuName", "String", "商品SKU名称"),
        ExcelColumnMapping("单价（元）", "unitAmount", "BigDecimal", "单价金额", "有优惠券时使用couponUnitAmount"),
        ExcelColumnMapping("数量", "count", "Integer", "商品数量"),
        ExcelColumnMapping("本期开始日", "statementStartTime", "Date", "账单期开始时间", "根据isBillingDetailsStatistics判断"),
        ExcelColumnMapping("本期结束日", "statementEndTime", "Date", "账单期结束时间", "根据isBillingDetailsStatistics判断"),
        ExcelColumnMapping("期数", "phase", "Integer", "期数信息", "显示格式：当前期数/总期数"),
        ExcelColumnMapping("设备序列号", "serialNumberSet", "Set<String>", "设备序列号集合", "用逗号分隔显示"),
        ExcelColumnMapping("应付金额", "billAmount", "BigDecimal", "应付金额", "支持合并单元格显示"),
        ExcelColumnMapping("支付状态", "payStatus", "Integer", "支付状态", "支持合并单元格，有特殊样式处理"),
        ExcelColumnMapping("是否续租", "isRelet", "Integer", "是否续租标识", "显示为'是'或'否'"),
        ExcelColumnMapping("账单标识", "billPeriodFlag", "BillPeriodFlag", "账单期标识", "根据不同类型显示不同文本和颜色"),
    ]
    
    # 应付信息
    PAYMENT_INFO_COLUMNS = [
        ExcelColumnMapping("出账金额", "billStatementAmount", "BigDecimal", "出账金额", "支持合并单元格"),
        ExcelColumnMapping("退租金额", "billReturnStatementAmount", "BigDecimal", "退租金额", "支持合并单元格"),
        ExcelColumnMapping("调整金额", "correctAmount", "BigDecimal", "调整金额", "支持合并单元格，显示为负数"),
        ExcelColumnMapping("优惠金额", "discountedAmount", "BigDecimal", "优惠金额", "支持合并单元格，显示为负数"),
        ExcelColumnMapping("已付金额", "paidAmount", "BigDecimal", "已付金额", "支持合并单元格"),
        ExcelColumnMapping("已退押金", "returnAmount", "BigDecimal", "已退押金金额", "支持合并单元格"),
        ExcelColumnMapping("说明", "payInfo", "String", "支付说明信息", "支持合并单元格"),
    ]
    
    # 客户信息
    CUSTOMER_INFO_COLUMNS = [
        ExcelColumnMapping("收件人", "consigneeName", "String", "收件人姓名"),
        ExcelColumnMapping("省", "provinceName", "String", "省份名称"),
        ExcelColumnMapping("市", "cityName", "String", "城市名称"),
        ExcelColumnMapping("区", "districtName", "String", "区县名称"),
        ExcelColumnMapping("详细地址", "address", "String", "详细地址"),
        ExcelColumnMapping("分子公司", "customerSubName", "String", "客户所属分子公司", "需要补充基础数据"),
    ]
    
    # 订单信息
    ORDER_INFO_COLUMNS = [
        ExcelColumnMapping("订单状态", "orderStatus", "Integer", "订单状态", "值所对应的描述：0-待提交;4-审核中;5-待备货;6-备货中;8-待发货;12-处理中;16-已发货;18-已签收;20-租赁中;22-部分退还;24-全部归还;28-取消;32-结束"),
        ExcelColumnMapping("关联信息", "associationCreateType", "Integer", "关联创建类型", "值所对应的描述：1-ERP创建;2-商城创建;3-再来一单;4-样机转租赁;5-客需换货;6-客户更换抬头;7-赔偿买断销售;8-维修换货;9-租完即送到期销售;10-PMC拆单;11-ERP退货单;12-退租拆单;13-设备转移;14-异常检测;15-PMC拆单(系统);16-资产处置申请单"),
        ExcelColumnMapping("原订单", "originOrderNo", "String", "原订单号", "associationCreateType 属于 (1,4,5,7,9,11,14)其中之一时才需要显示原订单号，否则显示空字符串"),
        ExcelColumnMapping("退租日期", "returnTime", "Date", "退租日期"),
    ]
    
    # 商品信息
    PRODUCT_INFO_COLUMNS = [
        ExcelColumnMapping("成色", "isNew", "Integer", "商品成色", "显示为'全新'或'次新'"),
        ExcelColumnMapping("租赁方式", "rentModeDesc", "String", "租赁方式描述", "优先使用rentModeDesc描述字段，如果为空则根据rentMode获取对应的描述"),
        ExcelColumnMapping("备注", "rentSceneMark", "String", "租赁场景备注", "需要补充基础数据"),
    ]
    
    # 使用信息
    USAGE_INFO_COLUMNS = [
        ExcelColumnMapping("使用人", "customerUser", "String", "客户使用人"),
        ExcelColumnMapping("使用组织", "customerOrganization", "String", "客户使用组织"),
        ExcelColumnMapping("使用备注", "customerRemark", "String", "客户使用备注"),
    ]
    
    @classmethod
    def get_all_columns(cls) -> List[ExcelColumnMapping]:
        """获取所有列映射"""
        all_columns = []
        all_columns.extend(cls.BILL_INFO_COLUMNS)
        all_columns.extend(cls.PAYMENT_INFO_COLUMNS)
        all_columns.extend(cls.CUSTOMER_INFO_COLUMNS)
        all_columns.extend(cls.ORDER_INFO_COLUMNS)
        all_columns.extend(cls.PRODUCT_INFO_COLUMNS)
        all_columns.extend(cls.USAGE_INFO_COLUMNS)
        return all_columns
    
    @classmethod
    def get_column_by_field(cls, field_name: str) -> ExcelColumnMapping:
        """根据字段名获取列映射"""
        for column in cls.get_all_columns():
            if column.field_name == field_name:
                return column
        return None
    
    @classmethod
    def get_field_by_column(cls, column_name: str) -> ExcelColumnMapping:
        """根据列名获取字段映射"""
        for column in cls.get_all_columns():
            if column.column_name == column_name:
                return column
        return None


# 状态映射
ORDER_STATUS_MAPPING = {
    0: "待提交",
    4: "审核中", 
    5: "待备货",
    6: "备货中",
    8: "待发货",
    12: "处理中",
    16: "已发货",
    18: "已签收",
    20: "租赁中",
    22: "部分退还",
    24: "全部归还",
    28: "取消",
    32: "结束"
}

# 支付状态映射（根据convert.mdc文档）
PAY_STATUS_MAPPING = {
    0: "未支付",
    4: "部分支付", 
    8: "已支付",
    16: "无需支付",
    20: "无需支付"
}

ASSOCIATION_CREATE_TYPE_MAPPING = {
    1: "ERP创建",
    4: "样机转租赁",
    5: "客需换货",
    7: "赔偿买断销售",
    9: "租完即送到期销售",
    11: "ERP退货单",
    14: "异常检测",
}

RENT_MODE_MAPPING = {
    1: "固定租期",
    2: "即租即还",
    3: "租完即送",
    4: "分期销售",
    5: "组合租赁"
}

# 租赁场景映射（需要根据实际业务补充）
RENT_SCENE_MAPPING = {
    # 这里需要根据实际业务场景补充
    # 例如：
    # 1: "办公设备租赁",
    # 2: "生产设备租赁", 
    # 3: "测试设备租赁",
    # 4: "临时设备租赁",
    # 5: "长期设备租赁"
} 

# 账单标志映射（根据枚举名称）
BILL_PERIOD_FLAG_MAPPING = {
    "BILL_PERIOD_FLAG_NEW": "新增设备",
    "BILL_PERIOD_FLAG_OLD": "往期设备", 
    "BILL_PERIOD_FLAG_RETURN": "退回设备",
    "BILL_PERIOD_FLAG_ADJUST": "调整项",
    "BILL_PERIOD_FLAG_OTHER": "其他费用",
    "BILL_PERIOD_FLAG_IT_SERVICE": "服务金额",
    "BILL_PERIOD_FLAG_DEPOSIT": "押金金额"
} 