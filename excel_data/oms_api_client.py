"""
OMS接口客户端 - 账单数据获取
根据OMS接口数据获取规则文档实现
"""

import json
import logging
from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Dict, List, Optional, Any

import requests

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class BusinessBillType(Enum):
    """账单类型枚举"""
    BUSINESS_BILL_TYPE_LONG = "BUSINESS_BILL_TYPE_LONG"
    BUSINESS_BILL_TYPE_SHORT = "BUSINESS_BILL_TYPE_SHORT"
    BUSINESS_BILL_TYPE_SALE = "BUSINESS_BILL_TYPE_SALE"
    BUSINESS_BILL_TYPE_SERVICE = "BUSINESS_BILL_TYPE_SERVICE"


class PayStatus(Enum):
    """支付状态枚举"""
    UNPAID = 0  # 未支付
    PARTIAL_PAID = 1  # 部分支付
    PAID = 2  # 已支付
    REFUNDED = 3  # 已退款


class BusinessOrderType(Enum):
    """业务单类型枚举"""
    RENTAL_ORDER = 1  # 租赁订单
    SALE_ORDER = 2  # 销售订单
    SERVICE_ORDER = 3  # 服务订单


class RentMode(Enum):
    """租赁方式枚举"""
    FIXED_TERM = 1  # 固定租期
    RENT_ANYTIME = 2  # 即租即还
    RENT_TO_OWN = 3  # 租完即送
    INSTALLMENT_SALE = 4  # 分期销售
    COMBINATION_RENTAL = 5  # 组合租赁


@dataclass
class CollectionAccount:
    """收款账户信息"""
    accountName: str
    accountBank: str
    accountNo: str


@dataclass
class CustomerAccount:
    """客户账户信息"""
    customerBalanceAmount: Decimal
    totalUnPaidAmount: Decimal
    totalNeedPayAmount: Decimal


@dataclass
class BillPeriodStatementDetail:
    """账单期结算明细"""
    # 基础业务信息
    businessOrderType: int
    businessOrderNo: str
    businessOrderId: int
    rentStartTime: datetime
    productName: str
    categoryName: str
    productSkuName: str
    description: str
    unitAmount: Decimal
    couponUnitAmount: Decimal
    count: int
    
    # 时间信息
    billExpectPayTime: datetime
    periodStartTime: datetime
    periodEndTime: datetime
    statementStartTime: datetime
    statementEndTime: datetime
    
    # 期数信息
    phase: int
    totalPhase: int
    payStatus: int
    billAmount: Decimal
    partPayAmount: Decimal
    billPeriodFlag: str  # 账单标识
    statisticsOrderType: int
    
    # 订单信息
    associationCreateType: int
    orderStatus: int
    originOrderNo: str
    returnTime: Optional[datetime]
    orderSubCompanyId: int
    rentLengthType: int
    
    # 商品信息
    isNew: int
    isReturnAnyTime: int
    rentMode: int
    rentModeDesc: str
    rentSceneMark: str  # 租赁场景备注
    
    # 收货信息
    consigneeName: str
    provinceName: str
    cityName: str
    districtName: str
    address: str
    customerSubName: str
    subsidiaryId: int
    
    # 发货信息
    deliveryMode: int
    deliverySubCompanyId: int
    
    # 支付信息
    paidAmount: Decimal
    returnAmount: Decimal
    payInfo: str
    payInfoGroupUuid: str
    
    # 优惠和冲正信息
    couponAmount: Decimal
    couponInfo: str
    discountedAmount: Decimal
    correctAmount: Decimal
    hasCorrectAmount: bool
    
    # 账单结算信息
    billStatementAmount: Decimal
    billReturnStatementAmount: Decimal
    
    # 设备序列号信息
    serialNumberSet: List[str]
    statementOrderDetailNoList: List[str]
    
    # 使用信息
    customerUser: str  # 客户使用人
    customerOrganization: str  # 客户使用组织
    customerRemark: str  # 客户使用备注

    # 续租信息
    isRelet: int  # 是否续租标识

    # 新增缺失的字段
    businessMode: int  # 业务类型
    productSummary: str  # 商品摘要
    orderItemId: int  # 订单项id
    orderItemType: int  # 订单项类型
    isChangeAllocation: int  # 是否改配单
    returnOrderCreateType: int  # 退货单创建类型
    rentType: int  # 租赁方式：2-月租；1-天租
    businessEquipmentType: int  # 大期业务设备类型
    orderItemMerge: bool  # 原订单项维度是否需要合并
    payInfoMerge: bool  # 原支付维度是否需要合并
    firstOrderItemMergeRecord: bool  # 是否原订单项维度单元格合并的第一条
    orderItemMergeRowCount: int  # 原订单项维度单元格合并行数
    firstPayInfoMergeRecord: bool
    payInfoMergeRowCount: int
    referPlatformThirdNo: str  # 关联平台第三方单号
    firstReferPlatformThirdNo: str  # 关联平台首期第三方单号
    thirdSubOrderNo: str  # 第三方子订单订单号
    couponStatementCorrectOrderIds: List[int]  # 优惠券产生优惠的冲正id


@dataclass
class AccountOrderMonthEquipmentDetail:
    """账户订单月设备明细"""
    # 基础信息
    consigneeName: str
    provinceName: str
    cityName: str
    districtName: str
    address: str
    customerSubName: str
    
    # 商品信息
    brandName: str
    productName: str
    categoryName: str
    isNewProduct: int
    isReturnAnyTime: int
    productSkuName: str
    productSkuPrice: Decimal
    
    # 租赁信息
    rentingProductCount: int
    orderNo: str
    rentStartTime: datetime
    returnTime: Optional[datetime]
    expectReturnTime: datetime
    rentLengthType: int
    rentStatus: int
    rentMode: int
    rentModeDesc: str
    
    # 业务信息
    orderSubCompanyId: int
    businessMode: int
    orderItemId: int
    orderItemType: int
    orderId: int
    orderType: int
    subsidiaryId: int
    
    # 序列号信息
    serialNumberSet: List[str]


@dataclass
class BillPeriodStatement:
    """账单期结算"""
    businessMode: int
    billType: str
    billAmount: Decimal
    paidAmount: Decimal
    unPaidAmount: Decimal
    newEquipmentAmount: Decimal
    oldEquipmentAmount: Decimal
    returnEquipmentAmount: Decimal
    otherAmount: Decimal
    adjustmentEquipmentAmount: Decimal
    itServiceAmount: Decimal
    depositEquipmentAmount: Decimal
    couponAmount: Decimal
    periodStartEquipmentCount: int
    returnEquipmentCount: int
    rentingEquipmentCount: int
    billPeriodDetailList: List[BillPeriodStatementDetail]
    excelBillPeriodDetailList: List[BillPeriodStatementDetail]
    penaltyAndOtherBillPeriodDetailList: List[BillPeriodStatementDetail]
    feesBillPeriodDetailList: List[BillPeriodStatementDetail]
    accountOrderMonthEquipmentDetailList: List[AccountOrderMonthEquipmentDetail]


@dataclass
class BillPeriodStatementStatistics:
    """账单期结算统计"""
    currentBillMonth: datetime
    customerId: int
    customerNo: str
    customerName: str
    billDateOfPayment: datetime
    collectionAccount: CollectionAccount
    customerAccount: CustomerAccount
    billPeriodStatementList: List[BillPeriodStatement]
    billingDetailsStatistics: Optional['BillPeriodStatementStatistics']
    shroffAccount: CollectionAccount


class OMSApiClient:
    """OMS接口客户端"""
    
    def __init__(self, base_url: str):
        """
        初始化OMS接口客户端
        
        Args:
            base_url: OMS API基础URL
            api_token: API访问令牌
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'CustomExcel/1.0'
        })

    
    def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送HTTP请求
        
        Args:
            endpoint: API端点
            data: 请求数据
            
        Returns:
            API响应数据
            
        Raises:
            Exception: 请求失败时抛出异常
        """
        url = f"{self.base_url}{endpoint}"
        
        try:
            logger.info(f"发送请求到: {url}")
            logger.debug(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            response = self.session.post(url, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"请求成功，响应码: {result.get('code')}")
            
            # 检查响应是否成功
            if not result.get('success', False):
                error_msg = result.get('description', '未知错误')
                raise Exception(f"API调用失败: {error_msg}")
            
            # 根据实际API响应结构获取数据
            # 数据可能在 resultMap.data 路径下
            if 'resultMap' in result and 'data' in result['resultMap']:
                return result['resultMap']['data']
            # 或者直接在 data 字段下
            elif 'data' in result:
                return result['data']
            # 或者整个响应就是数据
            else:
                return result
            
        except requests.exceptions.RequestException as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except json.JSONDecodeError as e:
            logger.error(f"响应解析失败: {str(e)}")
            raise Exception(f"响应解析失败: {str(e)}")
        except Exception as e:
            logger.error(f"请求处理失败: {str(e)}")
            raise
    
    def get_bill_data(self, 
                     customer_no: str, 
                     current_bill_month: str,
                     export_bill_account_template_id: int = 1,
                     shroff_account_id: Optional[int] = None) -> BillPeriodStatementStatistics:
        """
        获取账单数据
        
        Args:
            customer_no: 客户编号
            current_bill_month: 当前账单月份 (YYYY-MM格式)
            export_bill_account_template_id: 模板ID
            shroff_account_id: 收款账户ID
            
        Returns:
            BillPeriodStatementStatistics: 账单统计数据
            
        Raises:
            Exception: 参数验证失败或API调用失败
        """
        # 参数验证
        self._validate_bill_request_params(
            customer_no, current_bill_month, 
            export_bill_account_template_id, shroff_account_id
        )
        
        # 将current_bill_month转换为Date对象，然后转换为时间戳
        current_bill_month_date = self._parse_current_bill_month(current_bill_month)
        if not current_bill_month_date:
            raise Exception(f"无法解析账单月份: {current_bill_month}")
        
        # 转换为时间戳（毫秒）
        current_bill_month_timestamp = int(current_bill_month_date.timestamp() * 1000)
        
        # 构建请求参数
        request_data = {
            "exportBillAccountTemplateId": export_bill_account_template_id,
            "customerNo": customer_no,
            "currentBillMonth": current_bill_month_timestamp
        }
        
        if shroff_account_id is not None:
            request_data["shroffAccountId"] = shroff_account_id
        
        # 发送请求
        response_data = self._make_request("/interface/getBillAccountOrderData", request_data)
        
        # 解析响应数据
        return self._parse_bill_response(response_data)
    
    def _validate_bill_request_params(self, 
                                    customer_no: str, 
                                    current_bill_month: str,
                                    export_bill_account_template_id: int,
                                    shroff_account_id: Optional[int]) -> None:
        """
        验证账单请求参数
        
        Args:
            customer_no: 客户编号
            current_bill_month: 账单月份
            export_bill_account_template_id: 模板ID
            shroff_account_id: 收款账户ID
            
        Raises:
            Exception: 参数验证失败
        """
        # 验证客户编号
        if not customer_no or not isinstance(customer_no, str):
            raise Exception("客户编号不能为空且必须为字符串")
        
        if len(customer_no) < 3 or len(customer_no) > 50:
            raise Exception("客户编号长度必须在3-50字符之间")
        
        # 验证账单月份
        if not current_bill_month or not isinstance(current_bill_month, str):
            raise Exception("账单月份不能为空且必须为字符串")
        
        try:
            datetime.strptime(current_bill_month, "%Y-%m")
        except ValueError:
            raise Exception("账单月份格式错误，应为YYYY-MM格式")
        
        # 计算最大日期（当前月份+1）
        now = datetime.now()
        if now.month == 12:
            max_date = datetime(now.year + 1, 1, 1)
        else:
            max_date = datetime(now.year, now.month + 1, 1)
        
        bill_date = datetime.strptime(current_bill_month, "%Y-%m")
        if bill_date < datetime(2020, 1, 1) or bill_date > max_date:
            raise Exception("账单月份超出有效范围(2020-01至当前年月+1)")
        
        # 验证模板ID
        if not isinstance(export_bill_account_template_id, int) or export_bill_account_template_id < 1:
            raise Exception("模板ID必须为正整数")
        
        # 验证收款账户ID
        if shroff_account_id is not None:
            if not isinstance(shroff_account_id, int) or shroff_account_id < 1:
                raise Exception("收款账户ID必须为正整数")
    
    def _parse_bill_response(self, response_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析账单响应数据 - 返回与JSON文件相同的数据结构

        Args:
            response_data: API响应数据

        Returns:
            Dict[str, Any]: 与JSON文件格式相同的数据结构
        """
        try:
            # 直接返回包装后的数据，保持与JSON文件相同的结构
            return {
                'resultMap': {
                    'data': response_data
                }
            }

        except Exception as e:
            logger.error(f"解析账单响应数据失败: {str(e)}")
            raise Exception(f"解析账单响应数据失败: {str(e)}")
    
    def _parse_collection_account(self, account_data: Dict[str, Any]) -> CollectionAccount:
        """解析收款账户信息"""
        return CollectionAccount(
            accountName=account_data.get('accountName', ''),
            accountBank=account_data.get('accountBank', ''),
            accountNo=account_data.get('accountNo', '')
        )
    
    def _parse_customer_account(self, account_data: Dict[str, Any]) -> CustomerAccount:
        """解析客户账户信息"""
        return CustomerAccount(
            customerBalanceAmount=self._parse_decimal(account_data.get('customerBalanceAmount')),
            totalUnPaidAmount=self._parse_decimal(account_data.get('totalUnPaidAmount')),
            totalNeedPayAmount=self._parse_decimal(account_data.get('totalNeedPayAmount'))
        )
    
    def _parse_bill_period_statement(self, statement_data: Dict[str, Any]) -> BillPeriodStatement:
        """解析账单期结算信息"""
        # 解析明细列表
        bill_period_detail_list = []
        for detail_data in statement_data.get('billPeriodDetailList', []):
            detail = self._parse_bill_period_statement_detail(detail_data)
            bill_period_detail_list.append(detail)
        
        excel_bill_period_detail_list = []
        for detail_data in statement_data.get('excelBillPeriodDetailList', []):
            detail = self._parse_bill_period_statement_detail(detail_data)
            excel_bill_period_detail_list.append(detail)
        
        penalty_and_other_list = []
        for detail_data in statement_data.get('penaltyAndOtherBillPeriodDetailList', []):
            detail = self._parse_bill_period_statement_detail(detail_data)
            penalty_and_other_list.append(detail)
        
        fees_list = []
        for detail_data in statement_data.get('feesBillPeriodDetailList', []):
            detail = self._parse_bill_period_statement_detail(detail_data)
            fees_list.append(detail)
        
        # 解析设备明细列表
        equipment_detail_list = []
        for equipment_data in statement_data.get('accountOrderMonthEquipmentDetailList', []):
            equipment = self._parse_account_order_month_equipment_detail(equipment_data)
            equipment_detail_list.append(equipment)
        
        return BillPeriodStatement(
            businessMode=statement_data.get('businessMode', 0),
            billType=statement_data.get('billType', ''),
            billAmount=self._parse_decimal(statement_data.get('billAmount')),
            paidAmount=self._parse_decimal(statement_data.get('paidAmount')),
            unPaidAmount=self._parse_decimal(statement_data.get('unPaidAmount')),
            newEquipmentAmount=self._parse_decimal(statement_data.get('newEquipmentAmount')),
            oldEquipmentAmount=self._parse_decimal(statement_data.get('oldEquipmentAmount')),
            returnEquipmentAmount=self._parse_decimal(statement_data.get('returnEquipmentAmount')),
            otherAmount=self._parse_decimal(statement_data.get('otherAmount')),
            adjustmentEquipmentAmount=self._parse_decimal(statement_data.get('adjustmentEquipmentAmount')),
            itServiceAmount=self._parse_decimal(statement_data.get('itServiceAmount')),
            depositEquipmentAmount=self._parse_decimal(statement_data.get('depositEquipmentAmount')),
            couponAmount=self._parse_decimal(statement_data.get('couponAmount')),
            periodStartEquipmentCount=statement_data.get('periodStartEquipmentCount', 0),
            returnEquipmentCount=statement_data.get('returnEquipmentCount', 0),
            rentingEquipmentCount=statement_data.get('rentingEquipmentCount', 0),
            billPeriodDetailList=bill_period_detail_list,
            excelBillPeriodDetailList=excel_bill_period_detail_list,
            penaltyAndOtherBillPeriodDetailList=penalty_and_other_list,
            feesBillPeriodDetailList=fees_list,
            accountOrderMonthEquipmentDetailList=equipment_detail_list
        )
    
    def _parse_bill_period_statement_detail(self, detail_data: Dict[str, Any]) -> BillPeriodStatementDetail:
        """解析账单期结算明细"""
        return BillPeriodStatementDetail(
            # 基础业务信息
            businessOrderType=detail_data.get('businessOrderType', 0),
            businessOrderNo=detail_data.get('businessOrderNo', ''),
            businessOrderId=detail_data.get('businessOrderId', 0),
            rentStartTime=self._parse_datetime(detail_data.get('rentStartTime')),
            productName=detail_data.get('productName', ''),
            categoryName=detail_data.get('categoryName', ''),
            productSkuName=detail_data.get('productSkuName', ''),
            description=detail_data.get('description', ''),
            unitAmount=self._parse_decimal(detail_data.get('unitAmount')),
            couponUnitAmount=self._parse_decimal(detail_data.get('couponUnitAmount')),
            count=detail_data.get('count', 0),
            
            # 时间信息
            billExpectPayTime=self._parse_datetime(detail_data.get('billExpectPayTime')),
            periodStartTime=self._parse_datetime(detail_data.get('periodStartTime')),
            periodEndTime=self._parse_datetime(detail_data.get('periodEndTime')),
            statementStartTime=self._parse_datetime(detail_data.get('statementStartTime')),
            statementEndTime=self._parse_datetime(detail_data.get('statementEndTime')),
            
            # 期数信息
            phase=detail_data.get('phase', 0),
            totalPhase=detail_data.get('totalPhase', 0),
            payStatus=detail_data.get('payStatus', 0),
            billAmount=self._parse_decimal(detail_data.get('billAmount')),
            partPayAmount=self._parse_decimal(detail_data.get('partPayAmount')),
            billPeriodFlag=detail_data.get('billPeriodFlag', 'BILL_PERIOD_FLAG_OLD'), # 解析账单标识
            statisticsOrderType=detail_data.get('statisticsOrderType', 0),
            
            # 订单信息
            associationCreateType=detail_data.get('associationCreateType', 0),
            orderStatus=detail_data.get('orderStatus', 0),
            originOrderNo=detail_data.get('originOrderNo', ''),
            returnTime=self._parse_datetime(detail_data.get('returnTime')),
            orderSubCompanyId=detail_data.get('orderSubCompanyId', 0),
            rentLengthType=detail_data.get('rentLengthType', 0),
            
            # 商品信息
            isNew=detail_data.get('isNew', 0),
            isReturnAnyTime=detail_data.get('isReturnAnyTime', 0),
            rentMode=detail_data.get('rentMode', 0),
            rentModeDesc=detail_data.get('rentModeDesc', ''),
            rentSceneMark=detail_data.get('rentSceneMark', ''),
            
            # 收货信息
            consigneeName=detail_data.get('consigneeName', ''),
            provinceName=detail_data.get('provinceName', ''),
            cityName=detail_data.get('cityName', ''),
            districtName=detail_data.get('districtName', ''),
            address=detail_data.get('address', ''),
            customerSubName=detail_data.get('customerSubName', ''),
            subsidiaryId=detail_data.get('subsidiaryId', 0),
            
            # 发货信息
            deliveryMode=detail_data.get('deliveryMode', 0),
            deliverySubCompanyId=detail_data.get('deliverySubCompanyId', 0),
            
            # 支付信息
            paidAmount=self._parse_decimal(detail_data.get('paidAmount')),
            returnAmount=self._parse_decimal(detail_data.get('returnAmount')),
            payInfo=detail_data.get('payInfo', ''),
            payInfoGroupUuid=detail_data.get('payInfoGroupUuid', ''),
            payInfoMergeRowCount=detail_data.get('payInfoMergeRowCount', 0),
            
            # 优惠和冲正信息
            couponAmount=self._parse_decimal(detail_data.get('couponAmount')),
            couponInfo=detail_data.get('couponInfo', ''),
            discountedAmount=self._parse_decimal(detail_data.get('discountedAmount')),
            correctAmount=self._parse_decimal(detail_data.get('correctAmount')),
            hasCorrectAmount=detail_data.get('hasCorrectAmount', False),
            
            # 账单结算信息
            billStatementAmount=self._parse_decimal(detail_data.get('billStatementAmount')),
            billReturnStatementAmount=self._parse_decimal(detail_data.get('billReturnStatementAmount')),
            
            # 设备序列号信息
            serialNumberSet=detail_data.get('serialNumberSet', []),
            statementOrderDetailNoList=detail_data.get('statementOrderDetailNoList', []),
            
            # 使用信息
            customerUser=detail_data.get('customerUser', ''),
            customerOrganization=detail_data.get('customerOrganization', ''),
            customerRemark=detail_data.get('customerRemark', ''),
            
            # 续租信息
            isRelet=detail_data.get('isRelet', 1), # 设置默认值为1
            
            # 新增缺失的字段
            businessMode=detail_data.get('businessMode', 0),
            productSummary=detail_data.get('productSummary', ''),
            orderItemId=detail_data.get('orderItemId', 0),
            orderItemType=detail_data.get('orderItemType', 0),
            isChangeAllocation=detail_data.get('isChangeAllocation', 0),
            returnOrderCreateType=detail_data.get('returnOrderCreateType', 0),
            rentType=detail_data.get('rentType', 0),
            businessEquipmentType=detail_data.get('businessEquipmentType', 0),
            orderItemMerge=detail_data.get('orderItemMerge', False),  # 可能不存在，默认为False
            firstOrderItemMergeRecord=detail_data.get('firstOrderItemMergeRecord', False),  # 可能不存在，默认为False
            orderItemMergeRowCount=detail_data.get('orderItemMergeRowCount', 0),  # 可能不存在，默认为0
            payInfoMerge=detail_data.get('payInfoMerge', False),  # JSON中是payInfoMerge
            firstPayInfoMergeRecord=detail_data.get('firstPayInfoMergeRecord', False),  # JSON中是firstPayInfoMergeRecord
            referPlatformThirdNo=detail_data.get('referPlatformThirdNo', ''),
            firstReferPlatformThirdNo=detail_data.get('firstReferPlatformThirdNo', ''),
            thirdSubOrderNo=detail_data.get('thirdSubOrderNo', ''),
            couponStatementCorrectOrderIds=detail_data.get('couponStatementCorrectOrderIds', [])
        )
    
    def _parse_account_order_month_equipment_detail(self, equipment_data: Dict[str, Any]) -> AccountOrderMonthEquipmentDetail:
        """解析账户订单月设备明细"""
        return AccountOrderMonthEquipmentDetail(
            # 基础信息
            consigneeName=equipment_data.get('consigneeName', ''),
            provinceName=equipment_data.get('provinceName', ''),
            cityName=equipment_data.get('cityName', ''),
            districtName=equipment_data.get('districtName', ''),
            address=equipment_data.get('address', ''),
            customerSubName=equipment_data.get('customerSubName', ''),
            
            # 商品信息
            brandName=equipment_data.get('brandName', ''),
            productName=equipment_data.get('productName', ''),
            categoryName=equipment_data.get('categoryName', ''),
            isNewProduct=equipment_data.get('isNewProduct', 0),
            isReturnAnyTime=equipment_data.get('isReturnAnyTime', 0),
            productSkuName=equipment_data.get('productSkuName', ''),
            productSkuPrice=self._parse_decimal(equipment_data.get('productSkuPrice')),
            
            # 租赁信息
            rentingProductCount=equipment_data.get('rentingProductCount', 0),
            orderNo=equipment_data.get('orderNo', ''),
            rentStartTime=self._parse_datetime(equipment_data.get('rentStartTime')),
            returnTime=self._parse_datetime(equipment_data.get('returnTime')),
            expectReturnTime=self._parse_datetime(equipment_data.get('expectReturnTime')),
            rentLengthType=equipment_data.get('rentLengthType', 0),
            rentStatus=equipment_data.get('rentStatus', 0),
            rentMode=equipment_data.get('rentMode', 0),
            rentModeDesc=equipment_data.get('rentModeDesc', ''),
            
            # 业务信息
            orderSubCompanyId=equipment_data.get('orderSubCompanyId', 0),
            businessMode=equipment_data.get('businessMode', 0),
            orderItemId=equipment_data.get('orderItemId', 0),
            orderItemType=equipment_data.get('orderItemType', 0),
            orderId=equipment_data.get('orderId', 0),
            orderType=equipment_data.get('orderType', 0),
            subsidiaryId=equipment_data.get('subsidiaryId', 0),
            
            # 序列号信息
            serialNumberSet=equipment_data.get('serialNumberSet', [])
        )
    
    def _parse_datetime(self, datetime_value: Optional[Any]) -> Optional[datetime]:
        """解析日期时间值（支持字符串和时间戳）"""
        if datetime_value is None:
            return None
        
        try:
            # 如果是整数，可能是时间戳（毫秒）
            if isinstance(datetime_value, int):
                # 判断是秒还是毫秒时间戳
                if datetime_value > 1000000000000:  # 毫秒时间戳
                    return datetime.fromtimestamp(datetime_value / 1000)
                else:  # 秒时间戳
                    return datetime.fromtimestamp(datetime_value)
            
            # 如果是字符串，尝试解析
            if isinstance(datetime_value, str):
                # 尝试多种日期格式
                formats = [
                    "%Y-%m-%dT%H:%M:%S.%fZ",
                    "%Y-%m-%dT%H:%M:%SZ",
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d"
                ]
                
                for fmt in formats:
                    try:
                        return datetime.strptime(datetime_value, fmt)
                    except ValueError:
                        continue
                
                raise ValueError(f"无法解析日期格式: {datetime_value}")
            
            # 如果已经是datetime对象
            if isinstance(datetime_value, datetime):
                return datetime_value
            
            raise ValueError(f"不支持的日期类型: {type(datetime_value)}")
            
        except Exception as e:
            logger.warning(f"日期解析失败: {datetime_value}, 错误: {str(e)}")
            return None
    
    def _parse_current_bill_month(self, datetime_value: Optional[Any]) -> Optional[datetime]:
        """解析当前账单月份，转换为当月1号"""
        if datetime_value is None:
            return None
        
        try:
            # 如果是字符串，先尝试解析年月格式 (YYYY-MM)
            if isinstance(datetime_value, str):
                if len(datetime_value) == 7 and datetime_value[4] == '-':
                    year = int(datetime_value[:4])
                    month = int(datetime_value[5:7])
                    if 1 <= month <= 12:
                        return datetime(year, month, 1)
            
            # 再尝试解析为datetime
            parsed_datetime = self._parse_datetime(datetime_value)
            if parsed_datetime:
                # 转换为当月1号
                return datetime(parsed_datetime.year, parsed_datetime.month, 1)
            
            raise ValueError(f"无法解析账单月份格式: {datetime_value}")
            
        except Exception as e:
            logger.warning(f"账单月份解析失败: {datetime_value}, 错误: {str(e)}")
            return None
    
    def _parse_decimal(self, value: Any) -> Decimal:
        """解析Decimal值"""
        if value is None:
            return Decimal('0.00')
        
        try:
            if isinstance(value, (int, float)):
                return Decimal(str(value)).quantize(Decimal('0.01'))
            elif isinstance(value, str):
                return Decimal(value).quantize(Decimal('0.01'))
            elif isinstance(value, Decimal):
                return value.quantize(Decimal('0.01'))
            else:
                return Decimal('0.00')
        except Exception as e:
            logger.warning(f"Decimal解析失败: {value}, 错误: {str(e)}")
            return Decimal('0.00')
    
    def get_enum_description(self, enum_class: Enum, value: int) -> str:
        """获取枚举值描述"""
        try:
            for enum_item in enum_class:
                if enum_item.value == value:
                    return enum_item.name
            return f"未知值({value})"
        except Exception:
            return f"未知值({value})"





