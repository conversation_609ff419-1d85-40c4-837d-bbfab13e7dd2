"""
增强的占位符处理器
使用优化的映射器，支持更丰富的占位符
"""

import json
import openpyxl
from pathlib import Path
from placeholder_replacer import PlaceholderReplacer, WorksheetTypeIdentifier
from optimized_placeholder_mapper import OptimizedPlaceholderMapper


class EnhancedPlaceholderProcessor:
    """增强的占位符处理器"""
    
    def __init__(self, bill_data: dict):
        self.bill_data = bill_data
        self.placeholder_replacer = PlaceholderReplacer()
        self.worksheet_identifier = WorksheetTypeIdentifier()
        self.mapper = OptimizedPlaceholderMapper(bill_data)
    
    def process_excel_file(self, input_path: str, output_path: str) -> bool:
        """
        处理Excel文件，替换其中的占位符
        
        Args:
            input_path: 输入Excel文件路径
            output_path: 输出Excel文件路径
            
        Returns:
            bool: 处理是否成功
        """
        try:
            # 加载Excel文件
            workbook = openpyxl.load_workbook(input_path)
            
            # 处理每个工作表
            for worksheet in workbook.worksheets:
                self._process_worksheet(worksheet)
            
            # 保存文件
            workbook.save(output_path)
            print(f"✅ 成功处理Excel文件: {output_path}")
            return True
            
        except Exception as e:
            print(f"❌ 处理Excel文件时出错: {e}")
            return False
    
    def _process_worksheet(self, worksheet):
        """处理单个工作表"""
        # 识别工作表类型
        worksheet_type = self.worksheet_identifier.identify_worksheet_type(worksheet.title)
        print(f"📋 处理工作表: {worksheet.title} (类型: {worksheet_type})")
        
        # 获取对应的数据映射
        field_mapping = self.mapper.get_placeholder_mappings(worksheet_type)
        
        # 遍历所有单元格
        for row in worksheet.iter_rows():
            for cell in row:
                if cell.value and isinstance(cell.value, str):
                    # 查找占位符
                    placeholders = self.placeholder_replacer.find_placeholders(cell.value)
                    
                    if placeholders:
                        print(f"  🔍 在单元格 {cell.coordinate} 找到占位符: {[p['full_match'] for p in placeholders]}")
                        
                        # 替换占位符
                        new_value = self.placeholder_replacer.replace_placeholders(
                            cell.value, field_mapping
                        )
                        
                        cell.value = new_value
                        print(f"  ✏️  替换后: {new_value}")


def create_optimized_template_guide():
    """创建优化的模板指南"""
    
    guide = """
# Excel模板占位符使用指南

基于您的数据结构，以下是推荐的占位符使用方案：

## 1. 账单总览工作表占位符

### 基础信息
- `${customerName}` - 客户名称
- `${customerNo}` - 客户编号  
- `${customerId}` - 客户ID
- `${currentBillMonth}` - 当前账单月份 (2025-07-01)
- `${accountTime}` - 格式化月份 (2025年07月)
- `${billDateOfPayment}` - 账单支付日期
- `${currentTime}` - 当前时间
- `${time}` - 当前时间 (别名)

### 金额汇总信息
- `${totalBillAmount}` - 总账单金额
- `${totalPaidAmount}` - 总已付金额
- `${totalUnPaidAmount}` - 总未付金额
- `${totalAmount}` - 总金额 (别名)
- `${paidAmount}` - 已付金额 (别名)
- `${unPaidAmount}` - 未付金额 (别名)
- `${needAmount}` - 应付金额 (别名)
- `${payAmount}` - 已付金额 (别名)
- `${unPayAmount}` - 未付金额 (别名)

### 分类金额信息
- `${newEquipmentAmount}` - 新增设备金额
- `${oldEquipmentAmount}` - 往期设备金额
- `${returnEquipmentAmount}` - 退回设备金额
- `${otherAmount}` - 其他费用金额
- `${adjustmentEquipmentAmount}` - 调整设备金额
- `${itServiceAmount}` - IT服务金额
- `${depositEquipmentAmount}` - 押金设备金额
- `${couponAmount}` - 优惠券金额

### 收款账户信息
- `${accountName}` - 收款账户名称
- `${accountBank}` - 收款银行
- `${accountNo}` - 收款账号

### 客户账户信息
- `${customerBalanceAmount}` - 客户余额
- `${totalUnPaidAmount}` - 总未付金额
- `${totalNeedPayAmount}` - 总需付金额

### 账单类型和数量
- `${billTypeDescription}` - 账单类型描述
- `${periodStartEquipmentCount}` - 期初设备数量
- `${returnEquipmentCount}` - 退回设备数量
- `${rentingEquipmentCount}` - 租赁设备数量

## 2. 设备明细工作表占位符

### 商品信息
- `${productName}` - 商品名称
- `${categoryName}` - 类别名称
- `${productSkuName}` - 商品SKU名称
- `${description}` - 商品描述

### 订单信息
- `${businessOrderNo}` - 业务订单号
- `${businessOrderId}` - 业务订单ID
- `${businessOrderType}` - 业务订单类型

### 价格和数量
- `${unitAmount}` - 单价
- `${couponUnitAmount}` - 优惠单价
- `${count}` - 数量
- `${billAmount}` - 账单金额

### 时间信息
- `${rentStartTime}` - 租赁开始时间
- `${periodStartTime}` - 期初时间
- `${periodEndTime}` - 期末时间
- `${billExpectPayTime}` - 账单预期支付时间
- `${returnTime}` - 退回时间

### 期数和状态
- `${phase}` - 当前期数
- `${totalPhase}` - 总期数
- `${phaseInfo}` - 期数信息 (格式: 1/12)
- `${payStatus}` - 支付状态 (已支付/未支付等)
- `${orderStatus}` - 订单状态

### 租赁信息
- `${rentMode}` - 租赁模式代码
- `${rentModeDesc}` - 租赁模式描述
- `${rentModeDisplay}` - 租赁模式显示名称
- `${isNew}` - 成色 (全新/次新)
- `${isRelet}` - 是否续租 (是/否)
- `${isReturnAnyTime}` - 是否随时可退

### 收货信息
- `${consigneeName}` - 收件人姓名
- `${provinceName}` - 省份
- `${cityName}` - 城市
- `${districtName}` - 区县
- `${address}` - 详细地址
- `${customerSubName}` - 客户子公司

### 支付信息
- `${paidAmount}` - 已付金额
- `${returnAmount}` - 退回金额
- `${payInfo}` - 支付信息
- `${couponAmount}` - 优惠金额
- `${correctAmount}` - 冲正金额

### 其他信息
- `${serialNumberSet}` - 设备序列号
- `${customerUser}` - 客户使用人
- `${customerOrganization}` - 客户使用组织
- `${customerRemark}` - 客户使用备注
- `${associationCreateType}` - 关联创建类型
- `${originOrderNo}` - 原订单号

## 3. 使用建议

### 在Excel单元格中的使用方式：
1. 直接使用：`${customerName}`
2. 带文字说明：`客户名称：${customerName}`
3. 组合使用：`${customerName} (${customerNo})`
4. 格式化显示：`账单月份：${accountTime}`

### 特殊格式占位符：
- 使用 `#{}` 格式也支持，如：`#{accountName}`
- 金额会自动格式化为数字
- 日期会自动格式化为 YYYY-MM-DD 格式
- 状态会自动转换为中文描述

### 注意事项：
1. 占位符名称区分大小写
2. 如果数据中没有对应字段，占位符会保持原样
3. 空值会显示为空白
4. 数字类型会自动格式化
"""
    
    return guide


def main():
    """主函数 - 演示增强的占位符处理功能"""
    
    print("🚀 增强的Excel占位符处理器演示")
    print("=" * 60)
    
    # 显示使用指南
    print("\n📖 显示占位符使用指南...")
    guide = create_optimized_template_guide()
    
    # 保存指南到文件
    with open("Excel模板占位符使用指南.md", "w", encoding="utf-8") as f:
        f.write(guide)
    print("✅ 使用指南已保存到: Excel模板占位符使用指南.md")
    
    # 加载测试数据
    print("\n📊 加载测试数据...")
    data_file = "excel_data/bill_data_LXCC-1000-********-02686_2025-07.json"
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            bill_data = json.load(f)
        print(f"✅ 成功加载数据")
    except Exception as e:
        print(f"❌ 加载数据失败: {e}")
        return
    
    # 处理Excel文件
    print("\n🔄 处理Excel文件...")
    processor = EnhancedPlaceholderProcessor(bill_data)
    
    input_file = r"C:\Users\<USER>\PycharmProjects\CustomeExcel\template\LXCC-1000-20220721-22557.xls"
    output_file = "enhanced_output.xlsx"
    
    if not Path(input_file).exists():
        print(f"❌ 找不到输入文件: {input_file}")
        return
    
    success = processor.process_excel_file(input_file, output_file)
    
    if success:
        print(f"\n🎉 处理完成！")
        print(f"📁 输入文件: {input_file}")
        print(f"📁 输出文件: {output_file}")
        print(f"📖 使用指南: Excel模板占位符使用指南.md")
    else:
        print(f"\n❌ 处理失败！")
    
    print("\n" + "=" * 60)


if __name__ == "__main__":
    main()
