#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试占位符替换过程
专门用于调试 ${long.billAmount} 占位符的替换
"""

import json
import os
import sys
import re
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.multi_data_source_processor import MultiDataSourceProcessor


class PlaceholderDebugger:
    """占位符调试器"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.processor = None
        
    def debug_placeholder_replacement(self):
        """调试占位符替换过程"""
        print("🔍 开始调试占位符替换过程...")
        
        try:
            # 1. 初始化处理器
            print("\n📋 1. 初始化多数据源处理器...")
            self.processor = MultiDataSourceProcessor(self.config_path)
            
            # 2. 加载数据源
            print("\n📥 2. 加载所有数据源...")
            self.processor.load_all_data_sources()
            
            # 3. 合并数据源
            print("\n🔗 3. 合并数据源...")
            self.processor.merge_data_sources()
            
            # 4. 处理数据并分组（跳过，直接检查数据）
            print("\n📊 4. 检查处理器状态...")
            if hasattr(self.processor, 'bill_groups'):
                print("✅ bill_groups 已存在")
            else:
                print("⚠️ bill_groups 不存在，尝试手动创建...")
                # 手动调用数据分组方法
                if hasattr(self.processor, '_group_bills_by_type'):
                    self.processor._group_bills_by_type()
                    print("✅ 手动创建 bill_groups 成功")
            
            # 5. 调试 bill_groups 中的数据
            print("\n🔍 5. 调试 bill_groups 数据...")
            self._debug_bill_groups()
            
            # 6. 测试占位符替换
            print("\n🔄 6. 测试占位符替换...")
            self._test_placeholder_replacement()

            # 7. 检查Excel模板
            print("\n📄 7. 检查Excel模板中的占位符...")
            self._check_excel_template()

        except Exception as e:
            print(f"❌ 调试过程中出错: {e}")
            import traceback
            traceback.print_exc()
    
    def _debug_bill_groups(self):
        """调试 bill_groups 数据"""
        if not hasattr(self.processor, 'bill_groups'):
            print("❌ processor 没有 bill_groups 属性")
            return
            
        bill_groups = self.processor.bill_groups
        print(f"📊 bill_groups 包含 {len(bill_groups)} 个账单类型:")
        
        for bill_type, bill_data in bill_groups.items():
            print(f"\n  📋 账单类型: {bill_type}")
            if isinstance(bill_data, dict):
                # 打印关键字段
                key_fields = ['billAmount', 'paidAmount', 'unPaidAmount', 'billType']
                for field in key_fields:
                    if field in bill_data:
                        value = bill_data[field]
                        print(f"    {field}: {value} (类型: {type(value)})")
                
                # 检查是否有明细列表
                detail_lists = ['billPeriodDetailList', 'excelBillPeriodDetailList']
                for list_name in detail_lists:
                    if list_name in bill_data:
                        detail_list = bill_data[list_name]
                        print(f"    {list_name}: {len(detail_list)} 条记录")
            else:
                print(f"    数据类型: {type(bill_data)}")
    
    def _test_placeholder_replacement(self):
        """测试占位符替换"""
        test_placeholders = [
            "${long.billAmount}",
            "${long.paidAmount}",
            "${long.unPaidAmount}",
            "${long.billType}",
            "${short.billAmount}",
            "${sale.billAmount}"
        ]
        
        print("🧪 测试占位符替换:")
        for placeholder in test_placeholders:
            print(f"\n  🔍 测试占位符: {placeholder}")
            
            # 提取账单类型和字段名
            match = re.match(r'\$\{([^.]+)\.([^}]+)\}', placeholder)
            if match:
                bill_type = match.group(1)
                field_name = match.group(2)
                
                # 调用替换方法
                result = self.processor._replace_bill_type_placeholders(placeholder, bill_type)
                print(f"    替换结果: {result}")
                
                # 详细调试信息
                if hasattr(self.processor, 'bill_groups') and bill_type in self.processor.bill_groups:
                    bill_data = self.processor.bill_groups[bill_type]
                    if field_name in bill_data:
                        original_value = bill_data[field_name]
                        print(f"    原始值: {original_value} (类型: {type(original_value)})")
                    else:
                        print(f"    ❌ 字段 {field_name} 不存在于 {bill_type} 数据中")
                        print(f"    可用字段: {list(bill_data.keys()) if isinstance(bill_data, dict) else 'N/A'}")
                else:
                    print(f"    ❌ 账单类型 {bill_type} 不存在于 bill_groups 中")
                    if hasattr(self.processor, 'bill_groups'):
                        print(f"    可用账单类型: {list(self.processor.bill_groups.keys())}")
            else:
                print(f"    ❌ 无法解析占位符格式")
    
    def debug_specific_placeholder(self, placeholder: str):
        """调试特定占位符"""
        print(f"\n🎯 专门调试占位符: {placeholder}")
        
        # 提取账单类型和字段名
        match = re.match(r'\$\{([^.]+)\.([^}]+)\}', placeholder)
        if not match:
            print(f"❌ 无法解析占位符格式: {placeholder}")
            return
            
        bill_type = match.group(1)
        field_name = match.group(2)
        
        print(f"  账单类型: {bill_type}")
        print(f"  字段名: {field_name}")
        
        # 检查 bill_groups
        if not hasattr(self.processor, 'bill_groups'):
            print("❌ processor 没有 bill_groups 属性")
            return
            
        bill_groups = self.processor.bill_groups
        
        if bill_type not in bill_groups:
            print(f"❌ 账单类型 {bill_type} 不存在于 bill_groups 中")
            print(f"可用账单类型: {list(bill_groups.keys())}")
            return
            
        bill_data = bill_groups[bill_type]
        print(f"  ✅ 找到账单类型 {bill_type}")
        
        if field_name not in bill_data:
            print(f"❌ 字段 {field_name} 不存在于 {bill_type} 数据中")
            print(f"可用字段: {list(bill_data.keys()) if isinstance(bill_data, dict) else 'N/A'}")
            return
            
        value = bill_data[field_name]
        print(f"  ✅ 找到字段 {field_name}")
        print(f"  原始值: {value}")
        print(f"  值类型: {type(value)}")
        
        # 测试替换
        result = self.processor._replace_bill_type_placeholders(placeholder, bill_type)
        print(f"  替换结果: {result}")
        
        # 如果是金额字段，检查格式化
        if field_name in ['billAmount', 'paidAmount', 'unPaidAmount'] and isinstance(value, (int, float)):
            formatted_value = self.processor._format_smart_number(value)
            print(f"  格式化值: {formatted_value}")

    def _check_excel_template(self):
        """检查Excel模板中的占位符"""
        try:
            import openpyxl

            # 获取模板路径
            template_path = self.processor.moban_path
            print(f"📄 检查Excel模板: {template_path}")

            if not os.path.exists(template_path):
                print(f"❌ 模板文件不存在: {template_path}")
                return

            # 加载Excel文件
            workbook = openpyxl.load_workbook(template_path)

            # 查找包含 ${long.billAmount} 的单元格
            target_placeholder = "${long.billAmount}"
            found_cells = []

            for sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                print(f"\n  📋 检查工作表: {sheet_name}")

                for row in worksheet.iter_rows():
                    for cell in row:
                        if cell.value and isinstance(cell.value, str):
                            if target_placeholder in cell.value:
                                found_cells.append({
                                    'sheet': sheet_name,
                                    'cell': cell.coordinate,
                                    'value': cell.value
                                })
                                print(f"    ✅ 找到占位符在 {cell.coordinate}: {cell.value}")

            if not found_cells:
                print(f"❌ 在模板中未找到 {target_placeholder}")

                # 搜索类似的占位符
                similar_placeholders = []
                for sheet_name in workbook.sheetnames:
                    worksheet = workbook[sheet_name]
                    for row in worksheet.iter_rows():
                        for cell in row:
                            if cell.value and isinstance(cell.value, str):
                                if 'billAmount' in cell.value and '${' in cell.value:
                                    similar_placeholders.append({
                                        'sheet': sheet_name,
                                        'cell': cell.coordinate,
                                        'value': cell.value
                                    })

                if similar_placeholders:
                    print(f"🔍 找到类似的占位符:")
                    for item in similar_placeholders:
                        print(f"    {item['sheet']} {item['cell']}: {item['value']}")
            else:
                print(f"✅ 总共找到 {len(found_cells)} 个 {target_placeholder} 占位符")

            workbook.close()

        except Exception as e:
            print(f"❌ 检查Excel模板时出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    # 使用默认配置文件
    config_path = "config/multi_data_source_config.json"
    
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return
    
    debugger = PlaceholderDebugger(config_path)
    
    # 执行完整调试
    debugger.debug_placeholder_replacement()
    
    # 专门调试 ${long.billAmount}
    print("\n" + "="*60)
    debugger.debug_specific_placeholder("${long.billAmount}")


if __name__ == "__main__":
    main()
