#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置文件中的字段映射
"""

import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor

def test_config_field_mapping():
    """测试配置文件中的字段映射"""
    print("🧪 测试配置文件中的字段映射")
    print("=" * 50)
    
    # 创建处理器
    processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
    
    # 检查配置加载
    print(f"📋 配置信息:")
    print(f"  字段映射: {processor.placeholder_mapping.get('field_mappings', {})}")
    print(f"  数据转换: {list(processor.data_transformations.keys())}")
    
    # 测试各种占位符
    test_placeholders = [
        '${customerName}',
        '${shroffAccount.accountName}',
        '${shroffAccount.accountBank}',
        '${shroffAccount.accountNo}',
        '${collectionAccount.accountName}',
        '${collectionAccount.accountBank}',
        '${collectionAccount.accountNo}'
    ]
    
    print(f"\n📋 占位符测试结果:")
    for placeholder in test_placeholders:
        try:
            # 提取字段名
            field_name = placeholder[2:-1]  # 移除 ${}
            
            # 使用配置的字段映射
            field_mappings = processor.placeholder_mapping.get('field_mappings', {})
            mapped_field = field_mappings.get(field_name, field_name)
            
            # 获取值
            value = processor._get_field_value(mapped_field)
            
            print(f"  {placeholder}")
            print(f"    字段名: {field_name}")
            print(f"    映射后: {mapped_field}")
            print(f"    值: {value}")
            print()
            
        except Exception as e:
            print(f"  {placeholder} -> 错误: {e}")
            print()
    
    # 测试标准占位符替换
    print(f"📋 标准占位符替换测试:")
    for placeholder in test_placeholders:
        try:
            result = processor._replace_standard_placeholders(placeholder)
            print(f"  {placeholder} -> {result}")
        except Exception as e:
            print(f"  {placeholder} -> 错误: {e}")

if __name__ == "__main__":
    test_config_field_mapping() 