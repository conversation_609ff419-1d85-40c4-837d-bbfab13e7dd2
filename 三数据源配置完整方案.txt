# 三数据源配置完整方案

## 📊 概述

本方案支持三种数据源：
1. JSON数据源 - 本地JSON文件
2. API数据源 - HTTP API接口  
3. Excel数据源 - Excel文件

## 🛠️ 文件结构

```
configs/
├── data_sources_config.json    # 主配置文件
├── lxcc_template_config.json   # 模板配置
└── data_sources.json          # 数据源配置

data_loaders.py                 # 数据加载器
data_source_manager.py          # 数据源管理器
data_processor.py               # 数据处理器
enhanced_processor_v2.py        # 增强处理器
```

## 📋 1. 配置文件结构

### 1.1 主配置文件 (configs/data_sources_config.json)

```json
{
  "data_sources": {
    "bill_data_json": {
      "type": "json",
      "path": "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json",
      "encoding": "utf-8",
      "data_extraction": {
        "primary_path": "billPeriodStatementList",
        "fallback_paths": [
          "resultMap.data.billPeriodStatementList",
          "data.billPeriodStatementList"
        ]
      }
    },
    "customer_data_api": {
      "type": "api",
      "url": "https://api.example.com/customers",
      "method": "GET",
      "headers": {
        "Authorization": "Bearer ${API_TOKEN}",
        "Content-Type": "application/json"
      },
      "params": {
        "customer_id": "${customer_id}",
        "include_details": "true"
      },
      "timeout": 30,
      "retry": {
        "max_attempts": 3,
        "delay": 1
      },
      "data_extraction": {
        "primary_path": "data.customers",
        "error_path": "error.message"
      }
    },
    "equipment_data_excel": {
      "type": "excel",
      "path": "excel_data/equipment_list.xlsx",
      "sheets": {
        "equipment_list": {
          "start_row": 2,
          "end_row": null,
          "columns": {
            "A": "equipment_id",
            "B": "equipment_name", 
            "C": "serial_number",
            "D": "equipment_type"
          }
        },
        "equipment_types": {
          "start_row": 1,
          "end_row": null,
          "columns": {
            "A": "type_code",
            "B": "type_name",
            "C": "description"
          }
        }
      },
      "data_validation": {
        "required_columns": ["equipment_id", "equipment_name"],
        "unique_keys": ["equipment_id"]
      }
    }
  },
  "data_processing": {
    "preprocessing": {
      "bill_data_json": [
        {
          "step": "extract_nested",
          "path": "billPeriodStatementList",
          "output": "bill_statements"
        },
        {
          "step": "flatten_structure", 
          "input": "bill_statements",
          "output": "flattened_bills"
        }
      ],
      "customer_data_api": [
        {
          "step": "validate_response",
          "success_codes": [200, 201]
        },
        {
          "step": "extract_data",
          "path": "data.customers"
        }
      ],
      "equipment_data_excel": [
        {
          "step": "validate_required_columns",
          "columns": ["equipment_id", "equipment_name"]
        },
        {
          "step": "remove_empty_rows"
        }
      ]
    },
    "transformation": {
      "field_mappings": {
        "bill_data_json": {
          "billType": "bill_type",
          "billPeriodFlag": "period_flag",
          "billAmount": "amount"
        },
        "customer_data_api": {
          "customer_id": "customer_id",
          "customer_name": "name",
          "contact_phone": "phone"
        },
        "equipment_data_excel": {
          "equipment_id": "device_id",
          "equipment_name": "name",
          "serial_number": "serial_no"
        }
      },
      "data_conversions": {
        "bill_data_json": {
          "billAmount": {
            "type": "number",
            "default": 0.0
          },
          "billPeriodFlag": {
            "type": "enum",
            "mapping": {
              "BILL_PERIOD_FLAG_NEW": "new",
              "BILL_PERIOD_FLAG_OLD": "old"
            }
          }
        }
      }
    },
    "merging": {
      "strategy": "multi_source_join",
      "join_rules": [
        {
          "name": "bills_with_customers",
          "left": "bill_data_json",
          "right": "customer_data_api",
          "on": "customer_id",
          "how": "left"
        },
        {
          "name": "bills_with_equipment",
          "left": "bills_with_customers", 
          "right": "equipment_data_excel",
          "on": "equipment_id",
          "how": "left"
        }
      ]
    }
  }
}
```

### 1.2 模板配置文件 (configs/lxcc_template_config.json)

```json
{
  "template_info": {
    "name": "LXCC-1000-20220721-22557",
    "path": "template/LXCC-1000-20220721-22557.xlsx",
    "version": "1.0"
  },
  "worksheets": {
    "账单总览": {
      "header_row": 1,
      "data_start_row": 2,
      "special_processing": false
    },
    "长租账单-新增": {
      "header_row": 1,
      "data_start_row": 2,
      "special_processing": true,
      "filter_config": {
        "bill_type": "BUSINESS_BILL_TYPE_LONG",
        "bill_flag": "BILL_PERIOD_FLAG_NEW"
      }
    },
    "长租账单-往期": {
      "header_row": 1,
      "data_start_row": 2,
      "special_processing": true,
      "filter_config": {
        "bill_type": "BUSINESS_BILL_TYPE_LONG",
        "bill_flag": "BILL_PERIOD_FLAG_OLD"
      }
    },
    "租赁设备明细": {
      "header_row": 1,
      "data_start_row": 2,
      "special_processing": false
    }
  },
  "placeholder_mapping": {
    "default_bill_type": "long",
    "field_mappings": {
      "账单总览": {
        "businessOrderNo": "businessOrderNo",
        "billAmount": "billAmount",
        "billType": "billType"
      },
      "长租账单-新增": {
        "businessOrderNo": "businessOrderNo",
        "billAmount": "billAmount",
        "billPeriodFlag": "billPeriodFlag"
      },
      "长租账单-往期": {
        "businessOrderNo": "businessOrderNo",
        "billAmount": "billAmount",
        "billPeriodFlag": "billPeriodFlag"
      }
    }
  }
}
```

## 🛠️ 2. 核心代码实现

### 2.1 数据加载器 (data_loaders.py)

```python
import json
import pandas as pd
import requests
import os
import time
from typing import Dict, Any, Optional, List
from pathlib import Path

class BaseDataLoader:
    """数据加载器基类"""
    
    def load(self, config: Dict[str, Any]) -> Any:
        """加载数据"""
        raise NotImplementedError

class JSONDataLoader(BaseDataLoader):
    """JSON数据加载器"""
    
    def load(self, config: Dict[str, Any]) -> Dict[str, Any]:
        file_path = config['path']
        encoding = config.get('encoding', 'utf-8')
        
        print(f"📁 加载JSON文件: {file_path}")
        
        with open(file_path, 'r', encoding=encoding) as f:
            data = json.load(f)
        
        # 数据提取
        if 'data_extraction' in config:
            data = self._extract_data(data, config['data_extraction'])
        
        return data
    
    def _extract_data(self, data: Dict, extraction_config: Dict) -> Any:
        """根据配置提取数据"""
        primary_path = extraction_config.get('primary_path')
        if primary_path:
            result = self._get_by_path(data, primary_path)
            if result is not None:
                return result
        
        # 尝试fallback路径
        for path in extraction_config.get('fallback_paths', []):
            result = self._get_by_path(data, path)
            if result is not None:
                return result
        
        return data
    
    def _get_by_path(self, data: Dict, path: str) -> Any:
        """通过点分隔的路径获取数据"""
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current

class APIDataLoader(BaseDataLoader):
    """API数据加载器"""
    
    def load(self, config: Dict[str, Any]) -> Dict[str, Any]:
        url = config['url']
        method = config.get('method', 'GET')
        headers = config.get('headers', {})
        params = config.get('params', {})
        timeout = config.get('timeout', 30)
        
        print(f"🌐 请求API: {url}")
        
        # 处理动态参数
        headers = self._resolve_dynamic_values(headers)
        params = self._resolve_dynamic_values(params)
        
        # 发送请求
        response = self._make_request(url, method, headers, params, timeout, config)
        
        # 数据提取
        if 'data_extraction' in config:
            data = self._extract_data(response.json(), config['data_extraction'])
        else:
            data = response.json()
        
        return data
    
    def _make_request(self, url: str, method: str, headers: Dict, params: Dict, 
                     timeout: int, config: Dict) -> requests.Response:
        """发送HTTP请求"""
        max_attempts = config.get('retry', {}).get('max_attempts', 1)
        delay = config.get('retry', {}).get('delay', 1)
        
        for attempt in range(max_attempts):
            try:
                if method.upper() == 'GET':
                    response = requests.get(url, headers=headers, params=params, timeout=timeout)
                elif method.upper() == 'POST':
                    response = requests.post(url, headers=headers, json=params, timeout=timeout)
                else:
                    raise ValueError(f"Unsupported HTTP method: {method}")
                
                response.raise_for_status()
                return response
                
            except requests.RequestException as e:
                print(f"⚠️ API请求失败 (尝试 {attempt + 1}/{max_attempts}): {e}")
                if attempt == max_attempts - 1:
                    raise e
                time.sleep(delay)
        
        raise Exception("Max retry attempts exceeded")
    
    def _resolve_dynamic_values(self, data: Dict) -> Dict:
        """解析动态值（如环境变量）"""
        resolved = {}
        for key, value in data.items():
            if isinstance(value, str) and value.startswith('${') and value.endswith('}'):
                # 从环境变量获取值
                env_var = value[2:-1]
                resolved[key] = os.getenv(env_var, '')
            else:
                resolved[key] = value
        return resolved

class ExcelDataLoader(BaseDataLoader):
    """Excel数据加载器"""
    
    def load(self, config: Dict[str, Any]) -> Dict[str, Any]:
        file_path = config['path']
        sheets_config = config.get('sheets', {})
        
        print(f"📊 加载Excel文件: {file_path}")
        
        data = {}
        
        if sheets_config:
            # 加载指定的工作表
            for sheet_name, sheet_config in sheets_config.items():
                df = self._load_sheet(file_path, sheet_name, sheet_config)
                data[sheet_name] = df.to_dict('records')
        else:
            # 加载所有工作表
            excel_file = pd.ExcelFile(file_path)
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                data[sheet_name] = df.to_dict('records')
        
        return data
    
    def _load_sheet(self, file_path: str, sheet_name: str, sheet_config: Dict) -> pd.DataFrame:
        """加载指定工作表"""
        # 读取Excel
        df = pd.read_excel(file_path, sheet_name=sheet_name)
        
        # 应用行范围
        start_row = sheet_config.get('start_row', 0)
        end_row = sheet_config.get('end_row')
        
        if start_row > 0:
            df = df.iloc[start_row-1:]
        if end_row:
            df = df.iloc[:end_row-start_row+1]
        
        # 应用列映射
        columns_config = sheet_config.get('columns', {})
        if columns_config:
            df = self._apply_column_mapping(df, columns_config)
        
        return df
    
    def _apply_column_mapping(self, df: pd.DataFrame, columns_config: Dict) -> pd.DataFrame:
        """应用列映射"""
        # 将Excel列名映射为新的列名
        column_mapping = {}
        for excel_col, new_col in columns_config.items():
            if excel_col in df.columns:
                column_mapping[excel_col] = new_col
        
        df = df.rename(columns=column_mapping)
        return df
```

### 2.2 数据源管理器 (data_source_manager.py)

```python
import json
import pandas as pd
from typing import Dict, Any, List
from data_loaders import JSONDataLoader, APIDataLoader, ExcelDataLoader

class DataSourceManager:
    """数据源管理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.loaders = {
            'json': JSONDataLoader(),
            'api': APIDataLoader(), 
            'excel': ExcelDataLoader()
        }
        self.data_sources = {}
        self.processed_data = {}
    
    def load_all_sources(self) -> None:
        """加载所有数据源"""
        print("🔄 开始加载所有数据源...")
        
        for source_name, source_config in self.config['data_sources'].items():
            try:
                print(f"📥 加载数据源: {source_name}")
                source_type = source_config['type']
                
                if source_type not in self.loaders:
                    raise ValueError(f"不支持的数据源类型: {source_type}")
                
                loader = self.loaders[source_type]
                data = loader.load(source_config)
                
                self.data_sources[source_name] = data
                print(f"✅ 成功加载 {source_name}: {len(data) if isinstance(data, list) else 'dict'}")
                
            except Exception as e:
                print(f"❌ 加载数据源 {source_name} 失败: {e}")
                raise
    
    def preprocess_data(self) -> None:
        """预处理数据"""
        print("🔄 开始预处理数据...")
        
        preprocessing_config = self.config['data_processing']['preprocessing']
        
        for source_name, steps in preprocessing_config.items():
            if source_name not in self.data_sources:
                continue
                
            print(f"🔧 预处理数据源: {source_name}")
            data = self.data_sources[source_name]
            
            for step in steps:
                data = self._apply_preprocessing_step(data, step)
            
            self.processed_data[source_name] = data
    
    def transform_data(self) -> None:
        """转换数据格式"""
        print("🔄 开始转换数据格式...")
        
        transformation_config = self.config['data_processing']['transformation']
        
        for source_name, transformations in transformation_config.items():
            if source_name not in self.processed_data:
                continue
                
            print(f"🔄 转换数据源: {source_name}")
            data = self.processed_data[source_name]
            data = self._apply_transformations(data, transformations)
            self.processed_data[source_name] = data
    
    def merge_data(self) -> None:
        """合并数据"""
        print("🔄 开始合并数据...")
        
        merge_config = self.config['data_processing']['merging']
        
        for rule in merge_config['join_rules']:
            print(f"🔗 合并规则: {rule['name']}")
            
            left_data = self.processed_data[rule['left']]
            right_data = self.processed_data[rule['right']]
            
            merged_data = self._merge_datasets(left_data, right_data, rule)
            self.processed_data[rule['name']] = merged_data
    
    def _apply_preprocessing_step(self, data: Any, step: Dict) -> Any:
        """应用预处理步骤"""
        step_type = step['step']
        
        if step_type == 'extract_nested':
            return self._extract_nested_data(data, step['path'])
        elif step_type == 'flatten_structure':
            return self._flatten_structure(data)
        elif step_type == 'validate_response':
            return self._validate_response(data, step)
        elif step_type == 'extract_data':
            return self._extract_data_by_path(data, step['path'])
        elif step_type == 'validate_required_columns':
            return self._validate_required_columns(data, step['columns'])
        elif step_type == 'remove_empty_rows':
            return self._remove_empty_rows(data)
        else:
            raise ValueError(f"未知的预处理步骤: {step_type}")
    
    def _extract_nested_data(self, data: Any, path: str) -> Any:
        """提取嵌套数据"""
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _flatten_structure(self, data: List[Dict]) -> List[Dict]:
        """扁平化数据结构"""
        flattened = []
        
        for item in data:
            if isinstance(item, dict):
                flattened_item = {}
                for key, value in item.items():
                    if isinstance(value, dict):
                        for sub_key, sub_value in value.items():
                            flattened_item[f"{key}_{sub_key}"] = sub_value
                    else:
                        flattened_item[key] = value
                flattened.append(flattened_item)
            else:
                flattened.append(item)
        
        return flattened
    
    def _validate_response(self, data: Dict, step: Dict) -> Dict:
        """验证API响应"""
        success_codes = step.get('success_codes', [200])
        
        if 'status_code' in data and data['status_code'] not in success_codes:
            raise ValueError(f"API响应状态码错误: {data['status_code']}")
        
        return data
    
    def _extract_data_by_path(self, data: Dict, path: str) -> Any:
        """根据路径提取数据"""
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _validate_required_columns(self, data: List[Dict], columns: List[str]) -> List[Dict]:
        """验证必需列"""
        if not data:
            return data
        
        for item in data:
            missing_columns = [col for col in columns if col not in item]
            if missing_columns:
                raise ValueError(f"缺少必需列: {missing_columns}")
        
        return data
    
    def _remove_empty_rows(self, data: List[Dict]) -> List[Dict]:
        """移除空行"""
        return [item for item in data if any(item.values())]
    
    def _apply_transformations(self, data: Any, transformations: Dict) -> Any:
        """应用数据转换"""
        # 字段映射
        if 'field_mappings' in transformations:
            data = self._apply_field_mappings(data, transformations['field_mappings'])
        
        # 数据转换
        if 'data_conversions' in transformations:
            data = self._apply_data_conversions(data, transformations['data_conversions'])
        
        return data
    
    def _apply_field_mappings(self, data: List[Dict], mappings: Dict) -> List[Dict]:
        """应用字段映射"""
        if not isinstance(data, list):
            return data
        
        mapped_data = []
        for item in data:
            mapped_item = {}
            for old_key, new_key in mappings.items():
                if old_key in item:
                    mapped_item[new_key] = item[old_key]
                else:
                    mapped_item[new_key] = None
            mapped_data.append(mapped_item)
        
        return mapped_data
    
    def _apply_data_conversions(self, data: List[Dict], conversions: Dict) -> List[Dict]:
        """应用数据转换"""
        if not isinstance(data, list):
            return data
        
        converted_data = []
        for item in data:
            converted_item = item.copy()
            for field, conversion in conversions.items():
                if field in item:
                    converted_item[field] = self._convert_value(item[field], conversion)
            converted_data.append(converted_item)
        
        return converted_data
    
    def _convert_value(self, value: Any, conversion: Dict) -> Any:
        """转换单个值"""
        conversion_type = conversion.get('type')
        
        if conversion_type == 'number':
            try:
                return float(value) if value is not None else conversion.get('default', 0.0)
            except (ValueError, TypeError):
                return conversion.get('default', 0.0)
        
        elif conversion_type == 'enum':
            mapping = conversion.get('mapping', {})
            return mapping.get(value, value)
        
        return value
    
    def _merge_datasets(self, left_data: List[Dict], right_data: List[Dict], rule: Dict) -> List[Dict]:
        """合并数据集"""
        left_key = rule['on']
        right_key = rule['on']
        how = rule.get('how', 'left')
        
        # 创建查找字典
        right_lookup = {item[right_key]: item for item in right_data}
        
        merged = []
        for left_item in left_data:
            left_value = left_item.get(left_key)
            right_item = right_lookup.get(left_value, {})
            
            if how == 'left' or (how == 'inner' and right_item):
                merged_item = {**left_item, **right_item}
                merged.append(merged_item)
        
        return merged
    
    def get_final_data(self) -> Dict[str, Any]:
        """获取最终处理后的数据"""
        return self.processed_data
```

### 2.3 增强处理器 (enhanced_processor_v2.py)

```python
import json
import os
from typing import Dict, Any, List
from data_source_manager import DataSourceManager

class EnhancedProcessorV2:
    """增强处理器V2 - 支持多数据源"""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        self.data_manager = DataSourceManager(self.config)
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def process_all_data_sources(self) -> Dict[str, Any]:
        """处理所有数据源"""
        print("🚀 开始处理所有数据源...")
        
        # 1. 加载所有数据源
        self.data_manager.load_all_sources()
        
        # 2. 预处理数据
        self.data_manager.preprocess_data()
        
        # 3. 转换数据
        self.data_manager.transform_data()
        
        # 4. 合并数据
        self.data_manager.merge_data()
        
        # 5. 获取最终数据
        final_data = self.data_manager.get_final_data()
        
        print("✅ 数据处理完成!")
        return final_data
    
    def get_data_for_template(self, template_name: str) -> Dict[str, Any]:
        """获取指定模板的数据"""
        final_data = self.process_all_data_sources()
        
        # 根据模板名称返回相应的数据
        if template_name in final_data:
            return final_data[template_name]
        else:
            # 返回合并后的数据
            return final_data.get('bills_with_equipment', final_data)
    
    def export_processed_data(self, output_path: str) -> None:
        """导出处理后的数据"""
        final_data = self.process_all_data_sources()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(final_data, f, ensure_ascii=False, indent=2)
        
        print(f"📤 数据已导出到: {output_path}")
```

## 📊 3. 使用示例

### 3.1 基本使用

```python
# 创建增强处理器
processor = EnhancedProcessorV2('configs/data_sources_config.json')

# 处理所有数据源
final_data = processor.process_all_data_sources()

# 获取特定模板的数据
template_data = processor.get_data_for_template('bills_with_equipment')

# 导出数据
processor.export_processed_data('output/processed_data.json')
```

### 3.2 环境变量设置

```bash
# Windows PowerShell
$env:API_TOKEN="your_api_token_here"
$env:CUSTOMER_ID="12345"

# 或者创建 .env 文件
echo "API_TOKEN=your_api_token_here" > .env
echo "CUSTOMER_ID=12345" >> .env
```

### 3.3 Excel文件示例

```
equipment_list.xlsx:
Sheet: equipment_list
| A(equipment_id) | B(equipment_name) | C(serial_number) | D(equipment_type) |
|-----------------|-------------------|------------------|-------------------|
| EQ001           | 服务器A           | SN123456         | 服务器            |
| EQ002           | 交换机B           | SN789012         | 网络设备          |
| EQ003           | 打印机C           | SN345678         | 办公设备          |

Sheet: equipment_types
| A(type_code) | B(type_name) | C(description) |
|--------------|--------------|----------------|
| SERVER       | 服务器       | 计算服务器     |
| NETWORK      | 网络设备     | 网络连接设备   |
| OFFICE       | 办公设备     | 办公用品       |
```

## 🎯 4. 方案优势

### 4.1 统一接口
- 三种数据源使用相同的配置格式
- 统一的数据处理流程
- 一致的错误处理机制

### 4.2 灵活配置
- 支持动态参数和环境变量
- 条件路径和数据提取
- 可配置的数据转换规则

### 4.3 错误处理
- API重试机制
- 数据验证和清理
- 详细的错误日志

### 4.4 数据转换
- 字段映射和重命名
- 数据类型转换
- 枚举值映射

### 4.5 易于扩展
- 模块化设计
- 可插拔的数据加载器
- 支持自定义处理步骤

## 🔧 5. 部署说明

### 5.1 依赖安装

```bash
pip install pandas requests openpyxl xlrd
```

### 5.2 目录结构

```
project/
├── configs/
│   ├── data_sources_config.json
│   └── lxcc_template_config.json
├── data_loaders.py
├── data_source_manager.py
├── enhanced_processor_v2.py
├── excel_data/
│   └── *.json
├── template/
│   └── *.xlsx
└── output/
    └── processed_data.json
```

### 5.3 运行示例

```python
# 创建处理器
processor = EnhancedProcessorV2('configs/data_sources_config.json')

# 处理数据
final_data = processor.process_all_data_sources()

# 查看结果
print("处理完成的数据:")
for key, value in final_data.items():
    print(f"{key}: {len(value) if isinstance(value, list) else 'dict'}")
```

这个完整方案提供了三种数据源的统一处理能力，支持复杂的数据转换和合并操作，可以满足您的多数据源集成需求。 