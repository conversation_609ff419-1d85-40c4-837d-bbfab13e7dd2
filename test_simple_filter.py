"""
测试简化的过滤配置
"""

import json
from config.worksheet_filter_processor import WorksheetFilterProcessor


def test_simple_filter_configs():
    """测试简化的过滤配置"""
    
    # 创建测试数据
    test_bill_data = {
        "resultMap": {
            "data": {
                "billPeriodStatementList": [
                    {
                        "billType": "BUSINESS_BILL_TYPE_LONG",
                        "billAmount": 50000,
                        "excelBillPeriodDetailList": [
                            {
                                "businessOrderNo": "LXO-001",
                                "billPeriodFlag": "BILL_PERIOD_FLAG_NEW",
                                "billAmount": 15000,
                                "deliverySubCompanyId": 3,  # > 2，应该被包含
                                "orderStatus": 20,
                                "customerName": "测试客户A"
                            },
                            {
                                "businessOrderNo": "LXO-002", 
                                "billPeriodFlag": "BILL_PERIOD_FLAG_NEW",
                                "billAmount": 8000,
                                "deliverySubCompanyId": 1,  # <= 2，应该被排除
                                "orderStatus": 16,
                                "customerName": "测试客户B"
                            },
                            {
                                "businessOrderNo": "LXO-003",
                                "billPeriodFlag": "BILL_PERIOD_FLAG_OLD", 
                                "billAmount": 25000,
                                "deliverySubCompanyId": 5,
                                "orderStatus": 20,  # = 20，应该被包含
                                "customerName": "测试客户C"
                            },
                            {
                                "businessOrderNo": "LXO-004",
                                "billPeriodFlag": "BILL_PERIOD_FLAG_OLD", 
                                "billAmount": 12000,
                                "deliverySubCompanyId": 4,
                                "orderStatus": 16,  # != 20，应该被排除
                                "customerName": "测试客户D"
                            }
                        ]
                    }
                ]
            }
        }
    }
    
    # 创建过滤处理器
    processor = WorksheetFilterProcessor()
    
    print("🧪 测试简化的过滤配置...")
    
    # 测试1：长租账单-新增（需要 deliverySubCompanyId > 2）
    print("\n📋 测试1：长租账单-新增（deliverySubCompanyId > 2）")
    config_new = {
        "processing_type": "filtered",
        "filter_config": {
            "data_path": "excelBillPeriodDetailList",
            "filter_type": "advanced",
            "conditions": [
                {
                    "field_path": "billType",
                    "operator": "equals",
                    "value": "BUSINESS_BILL_TYPE_LONG",
                    "data_source": "parent"
                },
                {
                    "field_path": "billPeriodFlag",
                    "operator": "equals",
                    "value": "BILL_PERIOD_FLAG_NEW",
                    "data_source": "current"
                },
                {
                    "field_path": "deliverySubCompanyId",
                    "operator": "greater_than",
                    "value": 2,
                    "data_source": "current"
                }
            ],
            "logic": "AND"
        }
    }
    
    result1 = processor.process_filtered_worksheet("长租账单-新增", config_new, test_bill_data)
    print(f"结果: {len(result1)} 条记录")
    for item in result1:
        print(f"  - {item['businessOrderNo']}: deliverySubCompanyId={item['deliverySubCompanyId']}, flag={item['billPeriodFlag']}")
    
    # 测试2：长租账单-往期（需要 orderStatus = 20）
    print("\n📋 测试2：长租账单-往期（orderStatus = 20）")
    config_old = {
        "processing_type": "filtered",
        "filter_config": {
            "data_path": "excelBillPeriodDetailList",
            "filter_type": "advanced",
            "conditions": [
                {
                    "field_path": "billType",
                    "operator": "equals",
                    "value": "BUSINESS_BILL_TYPE_LONG",
                    "data_source": "parent"
                },
                {
                    "field_path": "billPeriodFlag",
                    "operator": "equals",
                    "value": "BILL_PERIOD_FLAG_OLD",
                    "data_source": "current"
                },
                {
                    "field_path": "orderStatus",
                    "operator": "equals",
                    "value": 20,
                    "data_source": "current"
                }
            ],
            "logic": "AND"
        }
    }
    
    result2 = processor.process_filtered_worksheet("长租账单-往期", config_old, test_bill_data)
    print(f"结果: {len(result2)} 条记录")
    for item in result2:
        print(f"  - {item['businessOrderNo']}: orderStatus={item['orderStatus']}, flag={item['billPeriodFlag']}")
    
    print("\n✅ 简化过滤配置测试完成！")


def test_with_real_data_simple():
    """使用真实数据测试简化配置"""
    print("\n🔍 使用真实数据测试简化配置...")
    
    try:
        # 加载真实数据
        with open('excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json', 'r', encoding='utf-8') as f:
            real_bill_data = json.load(f)
        
        processor = WorksheetFilterProcessor()
        
        # 测试长租账单-新增（带 deliverySubCompanyId > 2 条件）
        config_new = {
            "processing_type": "filtered",
            "filter_config": {
                "data_path": "excelBillPeriodDetailList",
                "filter_type": "advanced",
                "conditions": [
                    {
                        "field_path": "billType",
                        "operator": "equals",
                        "value": "BUSINESS_BILL_TYPE_LONG",
                        "data_source": "parent"
                    },
                    {
                        "field_path": "billPeriodFlag",
                        "operator": "equals",
                        "value": "BILL_PERIOD_FLAG_NEW",
                        "data_source": "current"
                    },
                    {
                        "field_path": "deliverySubCompanyId",
                        "operator": "greater_than",
                        "value": 2,
                        "data_source": "current"
                    }
                ],
                "logic": "AND"
            }
        }
        
        result_new = processor.process_filtered_worksheet("长租账单-新增", config_new, real_bill_data)
        print(f"📊 长租账单-新增（deliverySubCompanyId > 2）: {len(result_new)} 条记录")
        
        # 测试长租账单-往期（带 orderStatus = 20 条件）
        config_old = {
            "processing_type": "filtered",
            "filter_config": {
                "data_path": "excelBillPeriodDetailList",
                "filter_type": "advanced",
                "conditions": [
                    {
                        "field_path": "billType",
                        "operator": "equals",
                        "value": "BUSINESS_BILL_TYPE_LONG",
                        "data_source": "parent"
                    },
                    {
                        "field_path": "billPeriodFlag",
                        "operator": "equals",
                        "value": "BILL_PERIOD_FLAG_OLD",
                        "data_source": "current"
                    },
                    {
                        "field_path": "orderStatus",
                        "operator": "equals",
                        "value": 20,
                        "data_source": "current"
                    }
                ],
                "logic": "AND"
            }
        }
        
        result_old = processor.process_filtered_worksheet("长租账单-往期", config_old, real_bill_data)
        print(f"📊 长租账单-往期（orderStatus = 20）: {len(result_old)} 条记录")
        
        # 显示一些样本数据
        if result_new:
            print("\n长租账单-新增样本:")
            for i, item in enumerate(result_new[:3]):
                delivery_id = item.get('deliverySubCompanyId', 'N/A')
                print(f"  {i+1}. {item.get('businessOrderNo', 'N/A')}: deliverySubCompanyId={delivery_id}")
        
        if result_old:
            print("\n长租账单-往期样本:")
            for i, item in enumerate(result_old[:3]):
                order_status = item.get('orderStatus', 'N/A')
                print(f"  {i+1}. {item.get('businessOrderNo', 'N/A')}: orderStatus={order_status}")
    
    except FileNotFoundError:
        print("⚠️ 真实数据文件未找到，跳过真实数据测试")
    except Exception as e:
        print(f"❌ 真实数据测试失败: {e}")


if __name__ == "__main__":
    test_simple_filter_configs()
    test_with_real_data_simple()
