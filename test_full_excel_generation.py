#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整测试Excel生成过程
检查 ${long.billAmount} 占位符在最终Excel文件中的值
"""

import json
import os
import sys
import openpyxl
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.multi_data_source_processor import MultiDataSourceProcessor


def test_full_excel_generation():
    """测试完整的Excel生成过程"""
    print("🧪 开始测试完整的Excel生成过程...")
    
    try:
        # 1. 初始化处理器
        config_path = "config/multi_data_source_config.json"
        print(f"\n📋 1. 初始化处理器...")
        processor = MultiDataSourceProcessor(config_path)
        
        # 2. 加载和合并数据
        print(f"\n📥 2. 加载和合并数据...")
        processor.load_all_data_sources()
        processor.merge_data_sources()
        
        # 3. 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"test_excel_generation_{timestamp}.xlsx"
        output_path = os.path.join("output", output_filename)
        
        # 确保输出目录存在
        os.makedirs("output", exist_ok=True)
        
        print(f"\n📄 3. 生成Excel文件: {output_path}")
        
        # 4. 处理Excel模板
        processor.process_moban(output_path)

        # 5. 打印最后填写的占位符数据
        print(f"\n🎯 4. 打印最后填写的占位符数据...")
        processor.print_final_placeholder_values()

        # 6. 检查生成的Excel文件
        print(f"\n🔍 5. 检查生成的Excel文件...")
        check_generated_excel(output_path)
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()


def check_generated_excel(excel_path):
    """检查生成的Excel文件中的占位符替换结果"""
    if not os.path.exists(excel_path):
        print(f"❌ Excel文件不存在: {excel_path}")
        return
    
    try:
        print(f"📄 检查Excel文件: {excel_path}")
        workbook = openpyxl.load_workbook(excel_path)
        
        # 检查特定单元格的值
        target_cells = [
            ("账单总览", "D5"),  # ${long.billAmount}
            ("长租账单明细", "A2")  # 包含 ${long.billAmount} 的文本
        ]
        
        for sheet_name, cell_address in target_cells:
            if sheet_name in workbook.sheetnames:
                worksheet = workbook[sheet_name]
                cell = worksheet[cell_address]
                print(f"\n  📋 工作表: {sheet_name}")
                print(f"    📍 单元格 {cell_address}: {cell.value}")
                print(f"    📊 数据类型: {type(cell.value)}")
                
                # 检查是否还包含未替换的占位符
                if cell.value and isinstance(cell.value, str):
                    if "${long.billAmount}" in cell.value:
                        print(f"    ❌ 发现未替换的占位符: ${long.billAmount}")
                    else:
                        print(f"    ✅ 占位符已被替换")
            else:
                print(f"  ❌ 工作表 {sheet_name} 不存在")
        
        # 搜索所有包含 billAmount 的单元格
        print(f"\n🔍 搜索所有包含 'billAmount' 的单元格:")
        found_cells = []
        
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        if "billAmount" in cell.value:
                            found_cells.append({
                                'sheet': sheet_name,
                                'cell': cell.coordinate,
                                'value': cell.value
                            })
        
        if found_cells:
            for item in found_cells:
                print(f"  📍 {item['sheet']} {item['cell']}: {item['value']}")
        else:
            print(f"  ✅ 未找到任何包含 'billAmount' 的单元格（说明都已被替换）")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ 检查Excel文件时出错: {e}")
        import traceback
        traceback.print_exc()


def check_specific_placeholder_in_template():
    """检查模板中的特定占位符"""
    print("\n🔍 检查模板中的 ${long.billAmount} 占位符...")
    
    template_path = "moban_enhanced.xlsx"
    if not os.path.exists(template_path):
        print(f"❌ 模板文件不存在: {template_path}")
        return
    
    try:
        workbook = openpyxl.load_workbook(template_path)
        
        # 查找所有包含 ${long.billAmount} 的单元格
        found_cells = []
        for sheet_name in workbook.sheetnames:
            worksheet = workbook[sheet_name]
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        if "${long.billAmount}" in cell.value:
                            found_cells.append({
                                'sheet': sheet_name,
                                'cell': cell.coordinate,
                                'value': cell.value
                            })
        
        print(f"📊 在模板中找到 {len(found_cells)} 个 ${{long.billAmount}} 占位符:")
        for item in found_cells:
            print(f"  📍 {item['sheet']} {item['cell']}: {item['value']}")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ 检查模板时出错: {e}")


if __name__ == "__main__":
    # 首先检查模板
    check_specific_placeholder_in_template()
    
    # 然后测试完整生成过程
    test_full_excel_generation()
