#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试多数据源处理器
验证数据合并和占位符替换是否正确
"""

import json
import os
import sys
from openpyxl import load_workbook

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.multi_data_source_processor import MultiDataSourceProcessor


def test_data_merge():
    """测试数据合并功能"""
    print("🧪 测试数据合并功能...")

    # 创建处理器
    processor = MultiDataSourceProcessor(r'config\multi_data_source_config.json')

    # 加载所有数据源
    processor.load_all_data_sources()

    # 检查额外数据源
    extra_data = processor.all_data.get('extra_rent_data', [])
    print(f"📊 额外数据源记录数: {len(extra_data)}")

    if extra_data:
        print(f"📋 额外数据源第一条记录: {extra_data[0]}")

    # 合并数据源
    processor.merge_data_sources()

    # 检查合并后的数据
    merged_data = processor.get_merged_data()
    print(f"📊 合并后数据记录数: {len(merged_data) if isinstance(merged_data, list) else 'dict'}")

    # 查找匹配的记录，验证是否被正确覆盖
    target_business_order_no = "LXO-20250721-4001-00314"

    if isinstance(merged_data, list):
        for record in merged_data:
            if record.get('businessOrderNo') == target_business_order_no:
                print(f"\n🔍 找到匹配记录 {target_business_order_no}:")
                print(f"  rentStartTime: {record.get('rentStartTime', 'N/A')}")
                print(f"  rentEndTime: {record.get('rentEndTime', 'N/A')}")
                print(f"  rentMode: {record.get('rentMode', 'N/A')}")
                print(f"  rentAmount: {record.get('rentAmount', 'N/A')}")
                print(f"  rentPeriod: {record.get('rentPeriod', 'N/A')}")
                print(f"  rentUnitPrice: {record.get('rentUnitPrice', 'N/A')}")
                break
        else:
            print(f"❌ 未找到匹配记录 {target_business_order_no}")

    # 检查重要字段是否存在
    print(f"\n🔍 检查重要字段:")
    important_fields = ['customerName', 'shroffAccount', 'collectionAccount', 'customerAccount']

    for field in important_fields:
        value = processor._get_field_from_bill_data(field)
        if value:
            print(f"  ✅ {field}: {type(value).__name__}")
            if isinstance(value, dict):
                print(f"    内容: {list(value.keys())}")
        else:
            print(f"  ❌ {field}: 未找到")


def test_placeholder_replacement():
    """测试占位符替换"""
    print("\n🧪 测试占位符替换...")

    # 查找最新生成的Excel文件
    output_files = [f for f in os.listdir('.') if f.startswith('multi_source_output_') and f.endswith('.xlsx')]
    if not output_files:
        print("❌ 未找到输出文件")
        return

    latest_file = max(output_files, key=lambda x: os.path.getctime(x))
    print(f"📁 检查文件: {latest_file}")

    try:
        # 加载Excel文件
        workbook = load_workbook(latest_file)

        # 检查账单总览工作表
        if '账单总览' in workbook.sheetnames:
            worksheet = workbook['账单总览']
            print(f"\n📋 检查工作表: 账单总览")

            # 检查特定单元格的占位符替换
            test_cells = [
                ('B1', 'customerName'),
                ('B2', 'currentBillMonth'),
                ('B3', 'shroffAccount.accountName'),
                ('C3', 'shroffAccount.accountBank'),
                ('D3', 'shroffAccount.accountNo'),
                ('B4', 'customerAccount.totalUnPaidAmount'),
                ('C4', 'customerAccount.customerBalanceAmount'),
            ]

            for cell_ref, field_name in test_cells:
                try:
                    cell_value = worksheet[cell_ref].value
                    if cell_value and isinstance(cell_value, str):
                        if '${' in cell_value or '#{' in cell_value:
                            print(f"  ❌ {cell_ref} ({field_name}): 占位符未替换 - '{cell_value}'")
                        else:
                            print(f"  ✅ {cell_ref} ({field_name}): 已替换 - '{cell_value}'")
                    else:
                        print(f"  ℹ️  {cell_ref} ({field_name}): 值为 {cell_value}")
                except Exception as e:
                    print(f"  ⚠️ {cell_ref} ({field_name}): 读取失败 - {e}")

        workbook.close()

    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")

def test_data_merge_logic():
    """测试数据合并逻辑"""
    print("\n🧪 测试数据合并逻辑")
    print("=" * 40)
    
    # 模拟主数据
    primary_data = [
        {
            "businessOrderNo": "LXO-20250721-4001-00314",
            "billAmount": 5000,
            "customerName": "原始客户名称",
            "billType": "BUSINESS_BILL_TYPE_LONG"
        },
        {
            "businessOrderNo": "LXO-20250721-4001-00315", 
            "billAmount": 3000,
            "customerName": "另一个客户",
            "billType": "BUSINESS_BILL_TYPE_SHORT"
        }
    ]
    
    # 模拟额外数据
    extra_data = [
        {
            "businessOrderNo": "LXO-20250721-4001-00314",
            "rentStartTime": "2024-01-01",
            "rentEndTime": "2024-12-31",
            "rentMode": 1,
            "rentAmount": 10000,
            "rentPeriod": 12,
            "rentUnitPrice": 1000
        }
    ]
    
    # 模拟合并规则
    merge_rule = {
        "condition": "businessOrderNo matches",
        "override_fields": [
            "rentStartTime",
            "rentEndTime",
            "rentMode",
            "rentAmount", 
            "rentPeriod",
            "rentUnitPrice"
        ]
    }
    
    # 创建处理器实例来测试合并逻辑
    processor = MultiDataSourceProcessor('config/multi_data_source_config.json')
    
    # 执行合并
    merged_result = processor._merge_lists(primary_data, extra_data, merge_rule)
    
    print("📊 合并结果:")
    for item in merged_result:
        print(f"  {item}")
    
    # 验证合并是否正确
    print("\n✅ 验证合并结果:")
    for item in merged_result:
        if item["businessOrderNo"] == "LXO-20250721-4001-00314":
            print(f"  ✅ 订单 {item['businessOrderNo']} 已合并额外数据:")
            print(f"    rentStartTime: {item.get('rentStartTime')}")
            print(f"    rentEndTime: {item.get('rentEndTime')}")
            print(f"    rentAmount: {item.get('rentAmount')}")
        else:
            print(f"  ℹ️  订单 {item['businessOrderNo']} 无额外数据")

def main():
    """主函数"""
    print("🚀 开始测试多数据源处理器...")

    # 测试数据合并
    test_data_merge()

    # 测试占位符替换
    test_placeholder_replacement()

    # 测试数据合并逻辑
    test_data_merge_logic()

    print("\n✅ 测试完成")


if __name__ == "__main__":
    main()