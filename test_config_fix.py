#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置修复
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor


def test_config_fix():
    """测试配置修复"""
    print("🧪 测试配置修复")
    print("=" * 50)
    
    # 创建处理器
    config_path = "config/custom_001_advanced_config.json"
    processor = UnifiedConfigurableProcessor(config_path)
    
    # 显示数据统计
    processor.show_data_statistics()
    
    # 测试过滤功能
    new_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", "BILL_PERIOD_FLAG_NEW", True)
    old_bills = processor._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", "BILL_PERIOD_FLAG_OLD", True)
    
    print(f"\n📊 过滤结果:")
    print(f"   长租账单-新增 (BILL_PERIOD_FLAG_NEW): {len(new_bills)} 条")
    print(f"   长租账单-往期 (BILL_PERIOD_FLAG_OLD): {len(old_bills)} 条")
    
    # 显示前几条数据的详细信息
    if new_bills:
        print(f"\n📋 新增账单示例:")
        for i, bill in enumerate(new_bills[:2], 1):
            print(f"   {i}. 账单类型: {bill.get('statement_billType')}, 标志: {bill.get('billPeriodFlag')}")
            print(f"      订单号: {bill.get('businessOrderNo')}, 金额: {bill.get('billAmount')}")
    
    if old_bills:
        print(f"\n📋 往期账单示例:")
        for i, bill in enumerate(old_bills[:2], 1):
            print(f"   {i}. 账单类型: {bill.get('statement_billType')}, 标志: {bill.get('billPeriodFlag')}")
            print(f"      订单号: {bill.get('businessOrderNo')}, 金额: {bill.get('billAmount')}")


def main():
    """主函数"""
    print("🚀 配置修复测试")
    print("=" * 60)
    
    # 测试配置修复
    test_config_fix()
    
    print(f"\n📖 修复说明:")
    print(f"   1. 添加了 statement_level_filter 配置选项")
    print(f"   2. 修改了过滤逻辑，支持statement级别的账单类型过滤")
    print(f"   3. 将statement的billType信息添加到过滤后的数据中")
    print(f"   4. 现在应该能够正确解析到长租账单数据")


if __name__ == "__main__":
    main() 