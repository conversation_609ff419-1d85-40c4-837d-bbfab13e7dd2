#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单使用示例 - 展示修复后的配置使用方法
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor


def main():
    """主函数 - 演示修复后的配置使用"""
    print("🚀 修复后的配置使用示例")
    print("=" * 60)
    
    # 创建处理器
    config_path = "config/custom_001_advanced_config.json"
    processor = UnifiedConfigurableProcessor(config_path)
    
    # 显示数据统计
    print("\n📊 数据统计:")
    processor.show_data_statistics()
    
    # 处理模板
    print("\n🔄 开始处理模板...")
    output_path = "output/test_output_fixed.xlsx"
    
    success = processor.process_template(output_path)
    
    if success:
        print(f"✅ 模板处理成功！输出文件: {output_path}")
        print(f"📁 请检查输出文件中的 '长租账单-新增' 和 '长租账单-往期' 工作表")
    else:
        print("❌ 模板处理失败")


if __name__ == "__main__":
    main() 