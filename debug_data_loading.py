#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试数据加载
"""

import json
import os
import sys

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.multi_data_source_processor import MultiDataSourceProcessor


def debug_data_loading():
    """调试数据加载"""
    print("🔍 调试数据加载...")
    
    # 创建处理器
    processor = MultiDataSourceProcessor(r'config\multi_data_source_config.json')
    
    # 加载所有数据源
    processor.load_all_data_sources()
    
    # 检查主数据源
    primary_data = processor.all_data.get('primary', [])
    print(f"📊 主数据源记录数: {len(primary_data)}")
    
    if primary_data:
        print(f"📋 主数据源第一条记录的businessOrderNo: {primary_data[0].get('businessOrderNo', 'N/A')}")
        
        # 查找目标记录
        target_business_order_no = "LXO-20250721-4001-00314"
        found = False
        
        for i, record in enumerate(primary_data):
            business_order_no = record.get('businessOrderNo', '')
            if business_order_no == target_business_order_no:
                print(f"✅ 在主数据源中找到目标记录 (索引 {i}): {target_business_order_no}")
                print(f"   记录内容: {record}")
                found = True
                break
        
        if not found:
            print(f"❌ 在主数据源中未找到目标记录: {target_business_order_no}")
            print("📋 主数据源中的所有businessOrderNo:")
            for i, record in enumerate(primary_data):
                business_order_no = record.get('businessOrderNo', 'N/A')
                print(f"   [{i}] {business_order_no}")
    
    # 检查额外数据源
    extra_data = processor.all_data.get('extra_rent_data', [])
    print(f"\n📊 额外数据源记录数: {len(extra_data)}")
    
    if extra_data:
        print(f"📋 额外数据源记录: {extra_data[0]}")


def debug_original_json():
    """调试原始JSON文件"""
    print("\n🔍 调试原始JSON文件...")
    
    json_file = r'excel_data\bill_data_LXCC-1000-20241219-03101_2025-06.json'
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 检查数据结构
    print(f"📊 JSON根级别键: {list(data.keys())}")
    
    if 'resultMap' in data:
        result_map = data['resultMap']
        print(f"📊 resultMap键: {list(result_map.keys())}")
        
        if 'data' in result_map:
            data_section = result_map['data']
            print(f"📊 data键: {list(data_section.keys())}")
            
            if 'billPeriodStatementList' in data_section:
                bill_list = data_section['billPeriodStatementList']
                print(f"📊 billPeriodStatementList记录数: {len(bill_list)}")
                
                # 查找目标记录
                target_business_order_no = "LXO-20250721-4001-00314"
                found_count = 0
                
                for i, record in enumerate(bill_list):
                    business_order_no = record.get('businessOrderNo', '')
                    if business_order_no == target_business_order_no:
                        found_count += 1
                        if found_count == 1:  # 只打印第一个匹配的记录
                            print(f"✅ 在原始JSON中找到目标记录 (索引 {i}): {target_business_order_no}")
                            print(f"   记录键: {list(record.keys())}")
                
                print(f"📊 总共找到 {found_count} 条匹配记录")


if __name__ == "__main__":
    debug_data_loading()
    debug_original_json()
