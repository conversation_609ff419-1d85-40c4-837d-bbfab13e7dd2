"""
数据过滤器模块
支持简单条件过滤、复杂条件过滤和自定义方法过滤
"""

import re
from typing import List, Dict, Any, Callable
from datetime import datetime


class DataFilter:
    """数据过滤器基类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
    
    def filter(self, data_list: List[Dict], context_data: Dict = None) -> List[Dict]:
        """过滤数据"""
        raise NotImplementedError
    
    def _get_nested_value(self, data: Dict, path: str) -> Any:
        """获取嵌套字段值，支持点号路径"""
        if not path:
            return None
        
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            elif isinstance(current, list) and key.isdigit():
                index = int(key)
                if 0 <= index < len(current):
                    current = current[index]
                else:
                    return None
            else:
                return None
        
        return current


class AdvancedFilter(DataFilter):
    """高级条件过滤器"""
    
    def filter(self, data_list: List[Dict], context_data: Dict = None) -> List[Dict]:
        filtered_data = []
        conditions = self.config.get('conditions', [])
        logic = self.config.get('logic', 'AND')
        
        for item in data_list:
            if self._evaluate_conditions(item, conditions, logic, context_data):
                filtered_data.append(item)
        
        return filtered_data
    
    def _evaluate_conditions(self, item: Dict, conditions: List[Dict], 
                           logic: str, context_data: Dict = None) -> bool:
        results = []
        
        for condition in conditions:
            field_path = condition['field_path']
            operator = condition['operator']
            value = condition['value']
            data_source = condition.get('data_source', 'current')  # current, context, parent
            
            # 根据数据源获取值
            if data_source == 'current':
                item_value = self._get_nested_value(item, field_path)
            elif data_source == 'context' and context_data:
                item_value = self._get_nested_value(context_data, field_path)
            elif data_source == 'parent' and context_data:
                # 从父级数据中获取值
                parent_data = context_data.get('parent_data', {})
                item_value = self._get_nested_value(parent_data, field_path)
            else:
                item_value = None
            
            result = self._apply_operator(item_value, operator, value)
            results.append(result)
        
        if logic == 'AND':
            return all(results)
        elif logic == 'OR':
            return any(results)
        else:
            return False
    
    def _apply_operator(self, item_value: Any, operator: str, expected_value: Any) -> bool:
        """应用操作符"""
        try:
            if operator == 'equals':
                return item_value == expected_value
            elif operator == 'not_equals':
                return item_value != expected_value
            elif operator == 'greater_than':
                if item_value is None:
                    return False
                return float(item_value) > float(expected_value)
            elif operator == 'greater_than_or_equal':
                if item_value is None:
                    return False
                return float(item_value) >= float(expected_value)
            elif operator == 'less_than':
                if item_value is None:
                    return False
                return float(item_value) < float(expected_value)
            elif operator == 'less_than_or_equal':
                if item_value is None:
                    return False
                return float(item_value) <= float(expected_value)
            elif operator == 'in':
                return item_value in expected_value
            elif operator == 'not_in':
                return item_value not in expected_value
            elif operator == 'contains':
                return expected_value in str(item_value)
            elif operator == 'not_contains':
                return expected_value not in str(item_value)
            elif operator == 'starts_with':
                return str(item_value).startswith(str(expected_value))
            elif operator == 'ends_with':
                return str(item_value).endswith(str(expected_value))
            elif operator == 'between':
                if len(expected_value) == 2:
                    return expected_value[0] <= item_value <= expected_value[1]
                return False
            elif operator == 'not_between':
                if len(expected_value) == 2:
                    return not (expected_value[0] <= item_value <= expected_value[1])
                return True
            elif operator == 'is_null':
                return item_value is None
            elif operator == 'is_not_null':
                return item_value is not None
            elif operator == 'regex':
                return bool(re.match(str(expected_value), str(item_value)))
            elif operator == 'not_greater_than':
                if item_value is None:
                    return True
                return not (float(item_value) > float(expected_value))
            elif operator == 'not_less_than':
                if item_value is None:
                    return True
                return not (float(item_value) < float(expected_value))
            else:
                print(f"⚠️ 不支持的操作符: {operator}")
                return False
        except (ValueError, TypeError, AttributeError) as e:
            print(f"⚠️ 操作符 {operator} 执行失败: {e}")
            return False


class CustomFilter(DataFilter):
    """自定义方法过滤器"""
    
    def __init__(self, config: Dict[str, Any], custom_methods: Dict[str, Callable] = None):
        super().__init__(config)
        self.custom_methods = custom_methods or {}
    
    def filter(self, data_list: List[Dict], context_data: Dict = None) -> List[Dict]:
        method_name = self.config.get('method_name')
        parameters = self.config.get('parameters', {})
        
        if method_name in self.custom_methods:
            filter_method = self.custom_methods[method_name]
            return filter_method(data_list, context_data, **parameters)
        else:
            print(f"⚠️ 自定义过滤方法 {method_name} 未找到")
            return data_list


class CombinedFilter(DataFilter):
    """组合过滤器"""
    
    def __init__(self, config: Dict[str, Any], filter_factory):
        super().__init__(config)
        self.filter_factory = filter_factory
    
    def filter(self, data_list: List[Dict], context_data: Dict = None) -> List[Dict]:
        filters = self.config.get('filters', [])
        combine_logic = self.config.get('combine_logic', 'AND')
        
        if not filters:
            return data_list
        
        # 应用第一个过滤器
        first_filter = self.filter_factory.create_filter(filters[0])
        result = first_filter.filter(data_list, context_data)
        
        # 应用后续过滤器
        for filter_config in filters[1:]:
            filter_obj = self.filter_factory.create_filter(filter_config)
            
            if combine_logic == 'AND':
                result = filter_obj.filter(result, context_data)
            elif combine_logic == 'OR':
                # OR逻辑：合并结果
                other_result = filter_obj.filter(data_list, context_data)
                # 去重合并
                result_ids = {id(item) for item in result}
                for item in other_result:
                    if id(item) not in result_ids:
                        result.append(item)
        
        return result


class FilterFactory:
    """过滤器工厂"""
    
    def __init__(self, custom_methods: Dict[str, Callable] = None):
        self.custom_methods = custom_methods or {}
    
    def create_filter(self, filter_config: Dict[str, Any]) -> DataFilter:
        filter_type = filter_config.get('filter_type', 'advanced')
        
        if filter_type == 'advanced':
            return AdvancedFilter(filter_config)
        elif filter_type == 'custom':
            return CustomFilter(filter_config, self.custom_methods)
        elif filter_type == 'combined':
            return CombinedFilter(filter_config, self)
        else:
            raise ValueError(f"不支持的过滤器类型: {filter_type}")
    
    def register_custom_method(self, name: str, method: Callable):
        """注册自定义过滤方法"""
        self.custom_methods[name] = method
