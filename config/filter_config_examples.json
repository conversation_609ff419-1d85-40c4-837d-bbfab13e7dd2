{"worksheet_config": {"长租账单-新增": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent"}, {"field_path": "billPeriodFlag", "operator": "equals", "value": "BILL_PERIOD_FLAG_NEW", "data_source": "current"}, {"field_path": "deliverySubCompanyId", "operator": "greater_than", "value": 2, "data_source": "current"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "长租账单-往期": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent"}, {"field_path": "billPeriodFlag", "operator": "equals", "value": "BILL_PERIOD_FLAG_OLD", "data_source": "current"}, {"field_path": "orderStatus", "operator": "equals", "value": 20, "data_source": "current"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "高价值长租订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent"}, {"field_path": "billAmount", "operator": "greater_than", "value": 10000, "data_source": "current"}, {"field_path": "payStatus", "operator": "in", "value": [0, 4], "data_source": "current"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "VIP客户订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "custom", "method_name": "filter_by_customer_level", "parameters": {"min_level": 3, "vip_customers": ["深圳未来", "凌雄科技", "腾讯", "华为"]}}, "header_row": 1, "data_start_row": 2}, "逾期未付订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "custom", "method_name": "filter_by_payment_pattern", "parameters": {"pattern_type": "overdue", "days_threshold": 30}}, "header_row": 1, "data_start_row": 2}, "设备租赁中订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "custom", "method_name": "filter_by_equipment_status", "parameters": {"required_status": ["租赁中", "已发货"], "min_equipment_count": 1}}, "header_row": 1, "data_start_row": 2}, "复杂组合过滤": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "combined", "filters": [{"filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "in", "value": ["BUSINESS_BILL_TYPE_LONG", "BUSINESS_BILL_TYPE_SHORT"], "data_source": "parent"}], "logic": "AND"}, {"filter_type": "custom", "method_name": "filter_complex_business_logic", "parameters": {"threshold_amount": 5000, "priority_customers": ["深圳未来", "凌雄科技"], "exclude_statuses": [28, 32]}}], "combine_logic": "AND"}, "header_row": 1, "data_start_row": 2}, "时间范围过滤": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "combined", "filters": [{"filter_type": "custom", "method_name": "filter_by_time_range", "parameters": {"start_time": 1704067200000, "end_time": 1735689600000, "time_field": "billDate"}}, {"filter_type": "advanced", "conditions": [{"field_path": "billAmount", "operator": "between", "value": [1000, 50000], "data_source": "current"}], "logic": "AND"}], "combine_logic": "AND"}, "header_row": 1, "data_start_row": 2}}, "filter_operators_reference": {"description": "支持的过滤操作符参考", "operators": {"equals": "等于", "not_equals": "不等于", "greater_than": "大于", "greater_than_or_equal": "大于等于", "less_than": "小于", "less_than_or_equal": "小于等于", "in": "包含在列表中", "not_in": "不包含在列表中", "contains": "字符串包含", "not_contains": "字符串不包含", "starts_with": "字符串开头", "ends_with": "字符串结尾", "between": "在范围内", "not_between": "不在范围内", "is_null": "为空", "is_not_null": "不为空", "regex": "正则表达式匹配"}}, "data_source_reference": {"description": "数据源类型参考", "sources": {"current": "当前数据项（如excelBillPeriodDetailList中的单个项目）", "parent": "父级数据（如billPeriodStatementList中的单个汇总项）", "context": "上下文数据（完整的账单数据）"}}, "custom_methods_reference": {"description": "可用的自定义过滤方法", "methods": {"filter_bill_type_and_flag": "过滤账单类型和标志", "filter_complex_business_logic": "复杂业务逻辑过滤", "filter_by_dynamic_calculation": "动态计算过滤", "filter_by_time_range": "时间范围过滤", "filter_by_equipment_status": "设备状态过滤", "filter_by_customer_level": "客户等级过滤", "filter_by_payment_pattern": "支付模式过滤"}}}