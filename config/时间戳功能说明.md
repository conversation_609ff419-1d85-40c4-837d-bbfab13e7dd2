# 时间戳功能说明

## 功能概述

为生成的Excel文件自动添加时间戳，避免文件覆盖，便于版本管理和追踪。

## 实现方式

### 1. 文件名格式
```
{基础文件名}_{YYYYMMDD_HHMMSS}.xlsx
```

**示例**:
- `simple_output_20250731_114427.xlsx`
- `advanced_output_20250731_114427.xlsx`
- `unified_config_output_20250731_114427.xlsx`

### 2. 时间戳格式
- **日期**: `YYYYMMDD` (年月日)
- **时间**: `HHMMSS` (时分秒)
- **分隔符**: `_` (下划线)

### 3. 实现位置

#### 主测试函数 (`main()`)
```python
# 生成带时间戳的文件名
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
simple_output_file = f'simple_output_{current_time}.xlsx'
advanced_output_file = f'advanced_output_{current_time}.xlsx'
```

#### 处理器类 (`process_template()`)
```python
def process_template(self, output_path: str = None) -> bool:
    try:
        # 生成默认输出路径
        if output_path is None:
            current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"unified_config_output_{current_time}.xlsx"
        else:
            # 检查是否已经包含时间戳
            if not output_path.endswith('.xlsx'):
                output_path += '.xlsx'
            
            # 检查文件名是否已经包含时间戳格式
            import re
            if not re.search(r'_\d{8}_\d{6}\.xlsx$', output_path):
                base_name = output_path[:-5]
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"{base_name}_{current_time}.xlsx"
```

## 功能特性

### ✅ 自动时间戳
- 每次生成文件时自动添加当前时间戳
- 格式统一：`YYYYMMDD_HHMMSS`

### ✅ 防重复
- 检查文件名是否已包含时间戳
- 避免重复添加时间戳

### ✅ 灵活配置
- 支持自定义输出路径
- 自动处理文件扩展名

### ✅ 用户友好
- 控制台输出显示生成的文件名
- 便于用户识别和定位文件

## 使用示例

### 1. 默认输出
```python
processor = UnifiedConfigurableProcessor(config_path)
success = processor.process_template()  # 自动生成: unified_config_output_20250731_114427.xlsx
```

### 2. 自定义输出
```python
processor = UnifiedConfigurableProcessor(config_path)
success = processor.process_template("my_output.xlsx")  # 生成: my_output_20250731_114427.xlsx
```

### 3. 测试脚本
```python
# 生成带时间戳的文件名
current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
simple_output_file = f'simple_output_{current_time}.xlsx'
advanced_output_file = f'advanced_output_{current_time}.xlsx'

# 使用时间戳文件名
success = processor.process_template(os.path.join(output_dir, simple_output_file))
```

## 输出示例

```
🚀 统一配置化处理器
============================================================

📋 测试简单配置...
✅ 简单配置处理成功! 文件: simple_output_20250731_114427.xlsx

📋 测试高级配置...
✅ 高级配置处理成功! 文件: advanced_output_20250731_114427.xlsx
```

## 文件管理

### 目录结构
```
output/
├── simple_output_20250731_114427.xlsx
├── advanced_output_20250731_114427.xlsx
├── unified_config_output_20250731_114427.xlsx
└── ...
```

### 版本追踪
- 每次运行生成新的时间戳文件
- 保留历史版本便于对比
- 避免意外覆盖重要文件

## 优势

1. **版本管理**: 每次生成的文件都有唯一标识
2. **避免覆盖**: 不会意外覆盖之前的文件
3. **便于追踪**: 可以追踪文件的生成时间
4. **用户友好**: 文件名清晰易懂
5. **自动化**: 无需手动添加时间戳

这个时间戳功能确保了每次生成的文件都有唯一的标识，大大提高了文件管理的便利性和安全性！ 