# 统一配置化处理器使用说明

## 📋 概述

统一配置化处理器 (`UnifiedConfigurableProcessor`) 是一个通过配置文件实现不同处理逻辑的Excel模板处理器。它可以用一份代码实现 `UnifiedMobanProcessor` 和 `Custom001Processor` 的功能，通过不同的配置文件来区分处理逻辑。

## 🛠️ 文件结构

```
config/
├── unified_configurable_processor.py    # 统一处理器主文件
├── unified_simple_config.json           # 简单配置（类似UnifiedMobanProcessor）
├── custom_001_advanced_config.json      # 高级配置（类似Custom001Processor）
├── test_unified_processor.py            # 测试脚本
└── README.md                            # 使用说明
```

## 🚀 快速开始

### 1. 基本使用

```python
from config.unified_configurable_processor import UnifiedConfigurableProcessor

# 使用简单配置（类似UnifiedMobanProcessor）
simple_processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
simple_processor.process_template('output/simple_output.xlsx')

# 使用高级配置（类似Custom001Processor）
advanced_processor = UnifiedConfigurableProcessor('config/custom_001_advanced_config.json')
advanced_processor.process_template('output/advanced_output.xlsx')
```

### 2. 运行测试

```bash
python config/test_unified_processor.py
```

## 📊 配置文件说明

### 配置文件结构

```json
{
  "template_info": {
    "name": "模板名称",
    "template_path": "模板文件路径",
    "version": "版本号"
  },
  "data_sources": {
    "bill_data": {
      "type": "json",
      "path": "数据文件路径",
      "encoding": "utf-8",
      "data_extraction": {
        "primary_path": "主要数据路径",
        "fallback_paths": ["备用路径1", "备用路径2"]
      }
    }
  },
  "processing_mode": {
    "enable_special_filtering": true/false,
    "enable_bill_summary_processing": true/false,
    "preserve_original_logic": true/false
  },
  "worksheet_config": {
    "工作表名称": {
      "processing_type": "standard/filtered/special",
      "filter_config": {
        "bill_type": "账单类型",
        "bill_flag": "账单标志"
      },
      "header_row": 1,
      "data_start_row": 2
    }
  },
  "data_transformations": {
    "字段名": {
      "type": "number/enum",
      "format": "currency",
      "decimal_places": 2,
      "mapping": {
        "原值": "新值"
      }
    }
  },
  "placeholder_mapping": {
    "default_bill_type": "long",
    "field_mappings": {
      "占位符字段": "数据字段"
    }
  },
  "processing_options": {
    "remove_empty_worksheets": true,
    "preserve_styles": true,
    "auto_adjust_columns": true
  }
}
```

### 配置参数说明

#### template_info
- `name`: 模板名称
- `template_path`: 模板文件路径
- `version`: 版本号

#### data_sources
- `bill_data`: 账单数据源配置
  - `type`: 数据源类型（json/api/excel）
  - `path`: 数据文件路径
  - `encoding`: 文件编码
  - `data_extraction`: 数据提取配置

#### processing_mode
- `enable_special_filtering`: 是否启用特殊过滤（类似Custom001）
- `enable_bill_summary_processing`: 是否启用账单总览处理
- `preserve_original_logic`: 是否保持原有逻辑

#### worksheet_config
- `processing_type`: 处理类型
  - `standard`: 标准处理
  - `filtered`: 过滤处理（需要filter_config）
  - `special`: 特殊处理（如账单总览）
- `filter_config`: 过滤配置（仅filtered类型需要）
  - `bill_type`: 账单类型
  - `bill_flag`: 账单标志
- `header_row`: 标题行号
- `data_start_row`: 数据起始行号

#### data_transformations
- `type`: 转换类型
  - `number`: 数字转换
  - `enum`: 枚举转换
- `format`: 格式化类型
- `decimal_places`: 小数位数
- `mapping`: 枚举映射

#### placeholder_mapping
- `default_bill_type`: 默认账单类型
- `field_mappings`: 字段映射

#### processing_options
- `remove_empty_worksheets`: 是否移除空工作表
- `preserve_styles`: 是否保持样式
- `auto_adjust_columns`: 是否自动调整列宽

## 🎯 使用场景

### 1. 简单配置（类似UnifiedMobanProcessor）

适用于：
- 简单的占位符替换
- 不需要特殊数据过滤
- 标准的工作表处理

配置特点：
- `enable_special_filtering: false`
- `processing_type: "standard"` 或 `"special"`

### 2. 高级配置（类似Custom001Processor）

适用于：
- 需要根据条件过滤数据
- 特殊工作表处理
- 复杂的数据转换

配置特点：
- `enable_special_filtering: true`
- `processing_type: "filtered"`
- 包含 `filter_config`

## 📝 示例

### 简单配置示例

```python
# 创建简单处理器
processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')

# 显示数据统计
processor.show_data_statistics()

# 处理模板
success = processor.process_template('output/simple_output.xlsx')
```

### 高级配置示例

```python
# 创建高级处理器
processor = UnifiedConfigurableProcessor('config/custom_001_advanced_config.json')

# 显示数据统计（包含过滤统计）
processor.show_data_statistics()

# 处理模板
success = processor.process_template('output/advanced_output.xlsx')
```

## 🔧 自定义配置

### 1. 创建新的配置文件

```json
{
  "template_info": {
    "name": "我的模板",
    "template_path": "template/my_template.xlsx",
    "version": "1.0"
  },
  "data_sources": {
    "bill_data": {
      "type": "json",
      "path": "excel_data/my_data.json",
      "encoding": "utf-8"
    }
  },
  "processing_mode": {
    "enable_special_filtering": true,
    "enable_bill_summary_processing": true,
    "preserve_original_logic": true
  },
  "worksheet_config": {
    "我的工作表": {
      "processing_type": "filtered",
      "filter_config": {
        "bill_type": "MY_BILL_TYPE",
        "bill_flag": "MY_BILL_FLAG"
      },
      "header_row": 1,
      "data_start_row": 2
    }
  },
  "data_transformations": {
    "myField": {
      "type": "enum",
      "mapping": {
        "VALUE1": "显示值1",
        "VALUE2": "显示值2"
      }
    }
  },
  "placeholder_mapping": {
    "default_bill_type": "my",
    "field_mappings": {
      "myField": "myField"
    }
  },
  "processing_options": {
    "remove_empty_worksheets": true,
    "preserve_styles": true,
    "auto_adjust_columns": true
  }
}
```

### 2. 使用自定义配置

```python
# 使用自定义配置
processor = UnifiedConfigurableProcessor('config/my_config.json')
processor.process_template('output/my_output.xlsx')
```

## 🐛 故障排除

### 常见问题

1. **配置文件加载失败**
   - 检查JSON格式是否正确
   - 确认文件路径是否正确

2. **模板文件不存在**
   - 检查 `template_path` 配置
   - 确认模板文件是否存在

3. **数据文件不存在**
   - 检查 `data_sources.bill_data.path` 配置
   - 确认数据文件是否存在

4. **处理失败**
   - 检查数据格式是否正确
   - 确认占位符映射是否正确

### 调试方法

1. **启用详细日志**
   ```python
   processor = UnifiedConfigurableProcessor('config/my_config.json')
   processor.show_data_statistics()  # 显示数据统计
   ```

2. **检查配置文件**
   ```python
   import json
   with open('config/my_config.json', 'r', encoding='utf-8') as f:
       config = json.load(f)
   print(json.dumps(config, indent=2, ensure_ascii=False))
   ```

3. **运行测试**
   ```bash
   python config/test_unified_processor.py
   ```

## 📈 性能优化

1. **减少不必要的数据转换**
   - 只在需要时配置 `data_transformations`

2. **优化过滤逻辑**
   - 使用精确的 `filter_config`
   - 避免不必要的过滤条件

3. **合理配置工作表**
   - 只为需要特殊处理的工作表配置 `processing_type`

## 🔄 版本更新

当前版本：1.0

更新日志：
- v1.0: 初始版本，支持基本配置化处理

## 📞 支持

如有问题，请检查：
1. 配置文件格式是否正确
2. 文件路径是否存在
3. 数据格式是否符合要求
4. 运行测试脚本验证功能 