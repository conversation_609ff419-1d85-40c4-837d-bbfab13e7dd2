{"template_info": {"name": "简化过滤配置示例", "description": "展示如何使用简化的过滤配置", "version": "1.0"}, "worksheet_config": {"长租账单-新增": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent", "description": "账单类型必须是长租"}, {"field_path": "billPeriodFlag", "operator": "equals", "value": "BILL_PERIOD_FLAG_NEW", "data_source": "current", "description": "账单标志必须是新增"}, {"field_path": "deliverySubCompanyId", "operator": "greater_than", "value": 2, "data_source": "current", "description": "配送子公司ID必须大于2"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "长租账单-往期": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent", "description": "账单类型必须是长租"}, {"field_path": "billPeriodFlag", "operator": "equals", "value": "BILL_PERIOD_FLAG_OLD", "data_source": "current", "description": "账单标志必须是往期"}, {"field_path": "orderStatus", "operator": "equals", "value": 20, "data_source": "current", "description": "订单状态必须是20（租赁中）"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "短租账单-全部": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_SHORT", "data_source": "parent", "description": "账单类型必须是短租"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "高价值订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billAmount", "operator": "greater_than", "value": 10000, "data_source": "current", "description": "账单金额必须大于10000"}, {"field_path": "payStatus", "operator": "in", "value": [0, 4], "data_source": "current", "description": "支付状态必须是未支付或部分支付"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "特定客户订单": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "customerName", "operator": "contains", "value": "深圳", "data_source": "current", "description": "客户名称必须包含'深圳'"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}}, "usage_examples": {"description": "使用说明和示例", "examples": [{"name": "基本过滤", "description": "过滤特定账单类型和标志", "config": {"conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent"}]}}, {"name": "数值比较", "description": "过滤金额或数量字段", "config": {"conditions": [{"field_path": "billAmount", "operator": "greater_than", "value": 5000, "data_source": "current"}]}}, {"name": "列表包含", "description": "检查字段值是否在指定列表中", "config": {"conditions": [{"field_path": "payStatus", "operator": "in", "value": [0, 4, 8], "data_source": "current"}]}}, {"name": "字符串匹配", "description": "检查字符串字段是否包含特定内容", "config": {"conditions": [{"field_path": "customerName", "operator": "contains", "value": "科技", "data_source": "current"}]}}]}, "field_reference": {"description": "常用字段参考", "parent_fields": {"description": "父级数据字段（billPeriodStatementList中的字段）", "fields": ["billType", "billAmount", "paidAmount", "unPaidAmount"]}, "current_fields": {"description": "当前数据字段（excelBillPeriodDetailList中的字段）", "fields": ["businessOrderNo", "billPeriodFlag", "billAmount", "payStatus", "orderStatus", "customerName", "deliverySubCompanyId", "rentMode", "isNew"]}}, "operator_reference": {"description": "支持的操作符", "operators": {"equals": "等于", "not_equals": "不等于", "greater_than": "大于", "greater_than_or_equal": "大于等于", "less_than": "小于", "less_than_or_equal": "小于等于", "in": "包含在列表中", "not_in": "不包含在列表中", "contains": "字符串包含", "not_contains": "字符串不包含", "starts_with": "字符串开头", "ends_with": "字符串结尾", "is_null": "为空", "is_not_null": "不为空"}}}