"""
自定义过滤方法集合
包含各种复杂的业务逻辑过滤方法
"""

from typing import List, Dict, Any


class CustomFilterMethods:
    """自定义过滤方法集合"""
    
    @staticmethod
    def filter_bill_type_and_flag(data_list: List[Dict], context_data: Dict = None, 
                                 bill_type: str = None, bill_flag: str = None) -> List[Dict]:
        """
        过滤账单类型和账单标志
        bill_type 在父级数据中，bill_flag 在当前数据中
        """
        if not context_data or not bill_type:
            return data_list
        
        # 检查父级数据的账单类型
        parent_data = context_data.get('parent_data', {})
        parent_bill_type = parent_data.get('billType')
        
        if parent_bill_type != bill_type:
            return []  # 父级账单类型不匹配，返回空列表
        
        # 过滤当前数据的账单标志
        if not bill_flag:
            return data_list
        
        filtered_data = []
        for item in data_list:
            if item.get('billPeriodFlag') == bill_flag:
                filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_complex_business_logic(data_list: List[Dict], context_data: Dict = None,
                                    threshold_amount: float = 5000, 
                                    priority_customers: List[str] = None, 
                                    exclude_statuses: List[int] = None) -> List[Dict]:
        """复杂业务逻辑过滤"""
        priority_customers = priority_customers or []
        exclude_statuses = exclude_statuses or []
        
        filtered_data = []
        
        for item in data_list:
            # 排除特定状态
            if item.get('orderStatus') in exclude_statuses:
                continue
            
            # 高价值订单或优先客户
            is_high_value = item.get('billAmount', 0) >= threshold_amount
            is_priority_customer = any(customer in item.get('customerName', '') 
                                     for customer in priority_customers)
            
            if is_high_value or is_priority_customer:
                filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_by_dynamic_calculation(data_list: List[Dict], context_data: Dict = None,
                                    calculation_type: str = 'profit_margin', 
                                    min_margin: float = 0.2) -> List[Dict]:
        """动态计算过滤"""
        filtered_data = []
        
        for item in data_list:
            if calculation_type == 'profit_margin':
                bill_amount = item.get('billAmount', 0)
                cost_amount = item.get('costAmount', 0)
                
                if bill_amount > 0:
                    margin = (bill_amount - cost_amount) / bill_amount
                    if margin >= min_margin:
                        filtered_data.append(item)
            elif calculation_type == 'payment_ratio':
                bill_amount = item.get('billAmount', 0)
                paid_amount = item.get('paidAmount', 0)
                
                if bill_amount > 0:
                    ratio = paid_amount / bill_amount
                    if ratio >= min_margin:
                        filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_by_time_range(data_list: List[Dict], context_data: Dict = None,
                           start_time: int = None, end_time: int = None,
                           time_field: str = 'billDate') -> List[Dict]:
        """时间范围过滤"""
        if not start_time and not end_time:
            return data_list
        
        filtered_data = []
        
        for item in data_list:
            item_time = item.get(time_field)
            if item_time is None:
                continue
            
            # 转换为时间戳进行比较
            if isinstance(item_time, str):
                try:
                    from datetime import datetime
                    item_time = int(datetime.strptime(item_time, '%Y-%m-%d').timestamp() * 1000)
                except:
                    continue
            
            # 检查时间范围
            if start_time and item_time < start_time:
                continue
            if end_time and item_time > end_time:
                continue
            
            filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_by_equipment_status(data_list: List[Dict], context_data: Dict = None,
                                 required_status: List[str] = None,
                                 min_equipment_count: int = 1) -> List[Dict]:
        """设备状态过滤"""
        required_status = required_status or ['租赁中', '已发货']
        filtered_data = []
        
        for item in data_list:
            # 检查设备状态
            equipment_status = item.get('equipmentStatus', '')
            if equipment_status not in required_status:
                continue
            
            # 检查设备数量
            equipment_count = item.get('equipmentCount', 0)
            if equipment_count < min_equipment_count:
                continue
            
            filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_by_customer_level(data_list: List[Dict], context_data: Dict = None,
                                min_level: int = 1, vip_customers: List[str] = None) -> List[Dict]:
        """客户等级过滤"""
        vip_customers = vip_customers or []
        filtered_data = []
        
        for item in data_list:
            customer_name = item.get('customerName', '')
            customer_level = item.get('customerLevel', 0)
            
            # VIP客户直接通过
            if any(vip in customer_name for vip in vip_customers):
                filtered_data.append(item)
                continue
            
            # 检查客户等级
            if customer_level >= min_level:
                filtered_data.append(item)
        
        return filtered_data
    
    @staticmethod
    def filter_by_payment_pattern(data_list: List[Dict], context_data: Dict = None,
                                pattern_type: str = 'overdue', days_threshold: int = 30) -> List[Dict]:
        """支付模式过滤"""
        filtered_data = []

        for item in data_list:
            if pattern_type == 'overdue':
                # 逾期订单
                pay_status = item.get('payStatus', 0)
                bill_date = item.get('billDate')

                if pay_status in [0, 4] and bill_date:  # 未支付或部分支付
                    try:
                        from datetime import datetime, timedelta
                        bill_datetime = datetime.strptime(bill_date, '%Y-%m-%d')
                        threshold_date = datetime.now() - timedelta(days=days_threshold)

                        if bill_datetime < threshold_date:
                            filtered_data.append(item)
                    except:
                        continue

            elif pattern_type == 'quick_pay':
                # 快速支付订单
                pay_status = item.get('payStatus', 0)
                if pay_status == 8:  # 已支付
                    filtered_data.append(item)

        return filtered_data

    @staticmethod
    def filter_long_new_bills(data_list: List[Dict], context_data: Dict = None,
                            bill_type: str = None, bill_flag: str = None,
                            min_delivery_sub_company_id: int = 0) -> List[Dict]:
        """
        过滤长租新增账单
        """
        if not context_data or not bill_type:
            return data_list

        # 检查父级数据的账单类型
        parent_data = context_data.get('parent_data', {})
        parent_bill_type = parent_data.get('billType')

        if parent_bill_type != bill_type:
            return []  # 父级账单类型不匹配，返回空列表

        # 过滤当前数据
        filtered_data = []
        for item in data_list:
            # 检查账单标志
            if bill_flag and item.get('billPeriodFlag') != bill_flag:
                continue

            # 检查配送子公司ID
            delivery_id = item.get('deliverySubCompanyId')
            if delivery_id is not None and delivery_id <= min_delivery_sub_company_id:
                continue

            filtered_data.append(item)

        print(f"    🔍 长租新增过滤: billPeriodFlag={bill_flag}, deliverySubCompanyId>{min_delivery_sub_company_id}")
        return filtered_data

    @staticmethod
    def filter_long_old_bills(data_list: List[Dict], context_data: Dict = None,
                            bill_type: str = None, bill_flag: str = None,
                            required_order_status: int = None) -> List[Dict]:
        """
        过滤长租往期账单
        """
        if not context_data or not bill_type:
            return data_list

        # 检查父级数据的账单类型
        parent_data = context_data.get('parent_data', {})
        parent_bill_type = parent_data.get('billType')

        if parent_bill_type != bill_type:
            return []  # 父级账单类型不匹配，返回空列表

        # 过滤当前数据
        filtered_data = []
        for item in data_list:
            # 检查账单标志
            if bill_flag and item.get('billPeriodFlag') != bill_flag:
                continue

            # 检查订单状态
            if required_order_status is not None:
                order_status = item.get('orderStatus')
                if order_status != required_order_status:
                    continue

            filtered_data.append(item)

        print(f"    🔍 长租往期过滤: billPeriodFlag={bill_flag}, orderStatus={required_order_status}")
        return filtered_data


def get_custom_filter_methods() -> Dict[str, callable]:
    """获取所有自定义过滤方法"""
    return {
        'filter_bill_type_and_flag': CustomFilterMethods.filter_bill_type_and_flag,
        'filter_complex_business_logic': CustomFilterMethods.filter_complex_business_logic,
        'filter_by_dynamic_calculation': CustomFilterMethods.filter_by_dynamic_calculation,
        'filter_by_time_range': CustomFilterMethods.filter_by_time_range,
        'filter_by_equipment_status': CustomFilterMethods.filter_by_equipment_status,
        'filter_by_customer_level': CustomFilterMethods.filter_by_customer_level,
        'filter_by_payment_pattern': CustomFilterMethods.filter_by_payment_pattern,
        'filter_long_new_bills': CustomFilterMethods.filter_long_new_bills,
        'filter_long_old_bills': CustomFilterMethods.filter_long_old_bills,
    }
