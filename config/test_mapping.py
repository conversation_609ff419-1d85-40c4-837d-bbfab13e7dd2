#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor

def test_mapping():
    """测试配置映射是否生效"""
    print("🔍 测试配置映射功能")
    print("=" * 50)
    
    # 配置文件路径
    config_path = os.path.join(project_root, 'config', 'custom_001_advanced_config.json')
    
    # 创建处理器
    processor = UnifiedConfigurableProcessor(config_path)
    
    # 测试数据
    test_cases = [
        {"field": "orderStatus", "value": 20, "expected": "租赁222中"},
        {"field": "orderStatus", "value": 22, "expected": "部111分退还"},
        {"field": "payStatus", "value": 8, "expected": "已支付"},
        {"field": "payStatus", "value": 0, "expected": "未支付"},
        {"field": "billPeriodFlag", "value": "BILL_PERIOD_FLAG_NEW", "expected": "新增"},
        {"field": "billPeriodFlag", "value": "BILL_PERIOD_FLAG_OLD", "expected": "往期"},
    ]
    
    print("📋 测试配置映射:")
    for i, test_case in enumerate(test_cases, 1):
        field = test_case["field"]
        value = test_case["value"]
        expected = test_case["expected"]
        
        # 测试格式化方法
        if field == "orderStatus":
            result = processor._format_order_status(value)
        elif field == "payStatus":
            result = processor._format_pay_status(value)
        elif field == "billPeriodFlag":
            result = processor._format_bill_period_flag({"billPeriodFlag": value})
        else:
            result = "未知字段"
        
        # 检查结果
        status = "✅" if result == expected else "❌"
        print(f"  {i}. {field}({value}) -> {result} {status}")
        if result != expected:
            print(f"     期望: {expected}")
    
    print("\n📊 配置信息:")
    print(f"  配置文件: {config_path}")
    print(f"  数据转换规则: {list(processor.data_transformations.keys())}")
    
    # 显示具体的映射配置
    for field, rule in processor.data_transformations.items():
        if rule.get('type') == 'enum':
            print(f"  {field}: {rule['mapping']}")

if __name__ == "__main__":
    test_mapping() 