{"template_info": {"name": "多数据源处理器", "template_path": "C:\\Users\\<USER>\\PycharmProjects\\CustomeExcel\\moban_enhanced.xlsx", "version": "2.0"}, "data_sources": {"primary": {"name": "账单数据", "type": "json", "path": "C:\\Users\\<USER>\\PycharmProjects\\CustomeExcel\\excel_data\\bill_data_LXCC-1000-20241219-03101_2025-06.json", "encoding": "utf-8", "data_extraction": {"primary_path": "resultMap.data.billPeriodStatementList", "fallback_paths": ["billPeriodStatementList", "data.billPeriodStatementList"]}, "relation_key": "businessOrderNo", "is_primary": true, "enabled": true}, "extra_rent_data": {"name": "租赁数据和样式配置", "type": "json", "path": "config/extra.json", "encoding": "utf-8", "is_primary": false, "enabled": true}}, "data_processing": {"merge_rules": [{"source": "extra_rent_data", "condition": "businessOrderNo matches", "operation": "override_fields", "override_fields": ["rentStartTime", "rentEndTime", "rentMode", "rentAmount", "rentPeriod", "rentUnitPrice"]}, {"source": "customer_api_data", "condition": "customerId matches", "operation": "override_fields", "override_fields": ["customerName", "customerPhone", "customerEmail", "customerAddress"]}]}, "worksheet_config": {"账单总览": {"processing_type": "special", "enable_bill_summary": true, "header_row": 1, "data_start_row": 2}, "长租账单明细": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_LONG", "data_source": "parent"}, {"field_path": "deliverySubCompanyId", "operator": "greater_than", "value": 2, "data_source": "current"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "短租账单明细": {"processing_type": "filtered", "filter_config": {"data_path": "excelBillPeriodDetailList", "filter_type": "advanced", "conditions": [{"field_path": "billType", "operator": "equals", "value": "BUSINESS_BILL_TYPE_SHORT", "data_source": "parent"}, {"field_path": "orderStatus", "operator": "equals", "value": 20, "data_source": "current"}], "logic": "AND"}, "header_row": 1, "data_start_row": 2}, "租赁设备明细": {"processing_type": "standard", "header_row": 1, "data_start_row": 2}}, "placeholder_mapping": {"default_bill_type": "long", "field_mappings": {"businessOrderNo": "businessOrderNo", "billAmount": "billAmount", "billType": "billType", "customerName": "customerName", "billDate": "billDate", "payStatus": "payStatus", "orderStatus": "orderStatus", "rentMode": "rentMode", "isNew": "isNew", "serialNumberSet": "serialNumberSet", "rentStartTime": "rentStartTime", "rentEndTime": "rentEndTime", "rentAmount": "rentAmount", "rentPeriod": "rentPeriod", "rentUnitPrice": "rentUnitPrice", "customerPhone": "customerPhone", "customerEmail": "customerEmail", "customerAddress": "customerAddress", "shroffAccount.accountName": "shroffAccount.accountName", "shroffAccount.accountBank": "shroffAccount.accountBank", "shroffAccount.accountNo": "shroffAccount.accountNo", "collectionAccount.accountName": "collectionAccount.accountName", "collectionAccount.accountBank": "collectionAccount.accountBank", "collectionAccount.accountNo": "collectionAccount.accountNo"}}, "processing_options": {"remove_empty_worksheets": true, "preserve_styles": true, "auto_adjust_columns": true}}