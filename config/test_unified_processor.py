"""
测试统一配置化处理器
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor


def test_simple_config():
    """测试简单配置（类似UnifiedMobanProcessor）"""
    print("🧪 测试简单配置...")
    
    try:
        # 创建处理器
        processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
        
        # 显示数据统计
        processor.show_data_statistics()
        
        # 处理模板
        output_path = f"output/simple_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        success = processor.process_template(output_path)
        
        if success:
            print(f"✅ 简单配置处理成功! 输出文件: {output_path}")
            return True
        else:
            print("❌ 简单配置处理失败!")
            return False
            
    except Exception as e:
        print(f"❌ 简单配置测试失败: {e}")
        return False


def test_advanced_config():
    """测试高级配置（类似Custom001Processor）"""
    print("🧪 测试高级配置...")
    
    try:
        # 创建处理器
        processor = UnifiedConfigurableProcessor('config/custom_001_advanced_config.json')
        
        # 显示数据统计
        processor.show_data_statistics()
        
        # 处理模板
        output_path = f"output/advanced_output_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        success = processor.process_template(output_path)
        
        if success:
            print(f"✅ 高级配置处理成功! 输出文件: {output_path}")
            return True
        else:
            print("❌ 高级配置处理失败!")
            return False
            
    except Exception as e:
        print(f"❌ 高级配置测试失败: {e}")
        return False


def test_config_loading():
    """测试配置文件加载"""
    print("🧪 测试配置文件加载...")
    
    try:
        # 测试简单配置
        with open('config/unified_simple_config.json', 'r', encoding='utf-8') as f:
            simple_config = json.load(f)
        print("✅ 简单配置文件加载成功")
        
        # 测试高级配置
        with open('config/custom_001_advanced_config.json', 'r', encoding='utf-8') as f:
            advanced_config = json.load(f)
        print("✅ 高级配置文件加载成功")
        
        # 验证配置结构
        required_keys = ['template_info', 'data_sources', 'processing_mode', 'worksheet_config']
        for key in required_keys:
            if key not in simple_config:
                print(f"❌ 简单配置缺少必需键: {key}")
                return False
            if key not in advanced_config:
                print(f"❌ 高级配置缺少必需键: {key}")
                return False
        
        print("✅ 配置文件结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载测试失败: {e}")
        return False


def test_file_paths():
    """测试文件路径"""
    print("🧪 测试文件路径...")
    
    try:
        # 检查模板文件
        template_path = "template/LXCC-1000-20220721-22557.xlsx"
        if not os.path.exists(template_path):
            print(f"❌ 模板文件不存在: {template_path}")
            return False
        print(f"✅ 模板文件存在: {template_path}")
        
        # 检查数据文件
        data_path = "excel_data/bill_data_LXCC-1000-20241219-03101_2025-06.json"
        if not os.path.exists(data_path):
            print(f"❌ 数据文件不存在: {data_path}")
            return False
        print(f"✅ 数据文件存在: {data_path}")
        
        # 创建输出目录
        output_dir = "output"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            print(f"✅ 创建输出目录: {output_dir}")
        else:
            print(f"✅ 输出目录存在: {output_dir}")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件路径测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 统一配置化处理器测试")
    print("=" * 60)
    
    # 创建输出目录
    if not os.path.exists("output"):
        os.makedirs("output")
    
    # 运行测试
    tests = [
        ("配置文件加载", test_config_loading),
        ("文件路径检查", test_file_paths),
        ("简单配置处理", test_simple_config),
        ("高级配置处理", test_advanced_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        result = test_func()
        results.append((test_name, result))
        print(f"{'='*20} {test_name} {'完成' if result else '失败'} {'='*20}")
    
    # 显示测试结果
    print(f"\n{'='*60}")
    print("📊 测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
    else:
        print("⚠️  部分测试失败，请检查错误信息")


if __name__ == "__main__":
    main() 