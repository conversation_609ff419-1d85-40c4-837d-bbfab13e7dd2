{"template_info": {"name": "LXCC-1000-********-02739", "template_path": "moban_enhanced.xlsx", "version": "1.0"}, "data_sources": {"primary": {"name": "账单数据", "type": "api", "api_config": {"client_module": "excel_data.oms_api_client", "client_class": "OMSApiClient", "base_url": "http://127.0.0.1:20001", "method": "get_bill_data", "parameters": {"customer_no": "LXCC-1000-********-02739", "current_bill_month": "2025-08", "export_bill_account_template_id": 2}, "timeout": 300, "retry_count": 3, "fallback_data_path": "excel_data/bill_data_LXCC-1000-********-03101_2025-06.json"}, "is_primary": true, "enabled": true}, "extra_serial_data": {"name": "序列号修改数据", "type": "json", "path": "config/simple_001/extra_data.json", "encoding": "utf-8", "is_primary": false, "enabled": true}}, "data_processing": {"merge_rules": [{"source": "extra_serial_data", "condition": "apply_to_all", "operation": "transform_field", "field_operations": [{"field": "serialNumberSet", "operation": "replace_in_set", "rules": [{"find": "PF1C5R5Y", "replace": "PF1DJJJS"}]}]}]}, "worksheet_config": {"账单总览": {"processing_type": "special", "enable_bill_summary": true, "header_row": 1, "data_start_row": 2}, "长租账单明细": {"processing_type": "standard", "header_row": 5, "data_start_row": 6}, "短租账单明细": {"processing_type": "standard", "header_row": 5, "data_start_row": 6}, "销售账单明细": {"processing_type": "standard", "header_row": 5, "data_start_row": 6}, "IT服务账单明细": {"processing_type": "standard", "header_row": 5, "data_start_row": 6}, "租赁设备明细": {"processing_type": "standard", "header_row": 3, "data_start_row": 4}}, "data_transformations": {}, "placeholder_mapping": {"default_bill_type": "long", "field_mappings": {"businessOrderNo": "businessOrderNo", "billAmount": "billAmount", "billType": "billType", "customerName": "customerName", "billDate": "billDate", "payStatus": "payStatus", "orderStatus": "orderStatus", "rentMode": "rentMode", "isNew": "isNew", "serialNumberSet": "serialNumberSet", "shroffAccount.accountName": "shroffAccount.accountName", "shroffAccount.accountBank": "shroffAccount.accountBank", "shroffAccount.accountNo": "shroffAccount.accountNo", "collectionAccount.accountName": "collectionAccount.accountName", "collectionAccount.accountBank": "collectionAccount.accountBank", "collectionAccount.accountNo": "collectionAccount.accountNo"}}, "processing_options": {"remove_empty_worksheets": true, "preserve_styles": true, "auto_adjust_columns": true}}