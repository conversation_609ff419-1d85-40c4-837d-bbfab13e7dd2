{"template_info": {"name": "过滤测试处理器", "template_path": "template/LXCC-1000-********-22557.xlsx", "version": "1.0"}, "data_sources": {"primary": {"name": "账单数据", "type": "api", "api_config": {"client_module": "excel_data.oms_api_client", "client_class": "OMSApiClient", "base_url": "http://127.0.0.1:20001", "method": "get_bill_data", "parameters": {"customer_no": "LXCC-1000-********-02739", "current_bill_month": "2025-08", "export_bill_account_template_id": 2}, "timeout": 300, "retry_count": 3, "fallback_data_path": "excel_data/bill_data_LXCC-1000-********-03101_2025-06.json"}, "is_primary": true, "enabled": true}, "extra_serial_data": {"name": "序列号修改数据", "type": "json", "path": "config/extra_serial_data.json", "enabled": true}}, "data_processing": {"merge_rules": [{"source": "extra_serial_data", "condition": "apply_to_all", "operation": "transform_field", "field_operations": [{"field": "serialNumberSet", "operation": "replace_in_set", "rules": [{"find": "PF1C5R5Y", "replace": "PF1DJJJS"}]}]}], "filter_config": {"long.excelBillPeriodDetailList": {"description": "过滤长租Excel明细 - 只保留金额大于50的记录", "conditions": [{"field": "billAmount", "operator": "greater_than", "value": 50, "description": "账单金额大于50"}], "logic": "AND"}, "long.billPeriodDetailList": {"description": "过滤长租账单明细 - 只保留金额大于50的记录", "conditions": [{"field": "billAmount", "operator": "greater_than", "value": 50, "description": "账单金额大于50"}], "logic": "AND"}}}, "excel_operations": {"style_config": {"enable_auto_style": true, "border_style": "thin", "header_style": {"font_bold": true, "background_color": "E6F3FF"}}}, "processing_options": {"debug_mode": true, "show_statistics": true, "auto_remove_empty_worksheets": true}}