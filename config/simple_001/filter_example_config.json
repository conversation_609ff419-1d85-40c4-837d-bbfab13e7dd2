{"template_info": {"name": "过滤示例处理器", "template_path": "template/LXCC-1000-********-22557.xlsx", "version": "1.0"}, "data_sources": {"primary": {"name": "账单数据", "type": "api", "api_config": {"client_module": "excel_data.oms_api_client", "client_class": "OMSApiClient", "base_url": "http://127.0.0.1:20001", "method": "get_bill_data", "parameters": {"customer_no": "LXCC-1000-********-02739", "current_bill_month": "2025-08", "export_bill_account_template_id": 2}, "timeout": 300, "retry_count": 3, "fallback_data_path": "excel_data/bill_data_LXCC-1000-********-03101_2025-06.json"}, "is_primary": true, "enabled": true}}, "data_processing": {"merge_rules": [], "filter_config": {"long.billPeriodDetailList": {"description": "过滤长租账单明细 - 只保留金额大于100的记录", "conditions": [{"field": "billAmount", "operator": "greater_than", "value": 100, "description": "账单金额大于100"}], "logic": "AND"}, "long.excelBillPeriodDetailList": {"description": "过滤长租Excel明细 - 只保留金额大于100的记录", "conditions": [{"field": "billAmount", "operator": "greater_than", "value": 100, "description": "账单金额大于100"}], "logic": "AND"}, "equipment.accountOrderMonthEquipmentDetailList": {"description": "过滤设备明细 - 只保留特定租赁模式", "conditions": [{"field": "rentMode", "operator": "in", "value": ["RENT_MODE_LONG", "RENT_MODE_MONTH"], "description": "只保留长租和月租模式"}], "logic": "AND"}}}, "excel_operations": {"style_config": {"enable_auto_style": true, "border_style": "thin", "header_style": {"font_bold": true, "background_color": "E6F3FF"}}}, "processing_options": {"debug_mode": true, "show_statistics": true, "auto_remove_empty_worksheets": true}}