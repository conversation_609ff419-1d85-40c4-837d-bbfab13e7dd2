{"_config": {"description": "额外数据源配置和过滤规则", "filter_rules": {"exclude_conditions": [{"field": "businessOrderNo", "operator": "equals", "value": "LXTO-************-00154", "description": "排除特定订单号"}], "include_conditions": [{"field": "rentAmount", "operator": "greater_than", "value": 100, "description": "只包含租金大于100的订单"}], "logic": "AND"}, "excel_operations": {"description": "Excel样式和格式操作配置", "style_rules": [{"name": "highlight_target_order", "description": "高亮特定订单号", "condition": {"field": "businessOrderNo", "operator": "equals", "value": "LXO-20250721-4001-00314"}, "style": {"font_color": "FF0000", "font_bold": true, "background_color": "FFFF99"}, "apply_to": "entire_row"}, {"name": "highlight_high_amount", "description": "高亮高金额订单", "condition": {"field": "billAmount", "operator": "greater_than", "value": 5000}, "style": {"font_color": "0000FF", "font_bold": true}, "apply_to": "specific_cells", "target_columns": ["billAmount", "businessOrderNo"]}], "conditional_formatting": [{"name": "amount_color_scale", "description": "金额颜色渐变", "field": "billAmount", "type": "color_scale", "min_color": "FFFFFF", "max_color": "FF6B6B", "apply_to_worksheets": ["长租账单-新增", "长租账单-往期"]}], "cell_operations": [{"name": "format_dates", "description": "格式化日期字段", "fields": ["rentStartTime", "rentEndTime"], "format": "YYYY-MM-DD", "apply_to_worksheets": ["长租账单-新增", "长租账单-往期"]}]}}, "data": [{"relateion_key": "businessOrderNo", "businessOrderNo": "LXO-20250721-4001-00314", "rentStartTime": 1754015530000, "rentEndTime": 1754015530000, "rentMode": 1, "rentAmount": 10000, "rentPeriod": 12, "rentUnitPrice": 1000}, {"relateion_key": "businessOrderNo", "businessOrderNo": "LXO-20250326-4001-10460", "rentStartTime": 1754015530000, "rentEndTime": 1754015530000, "rentMode": 1, "rentAmount": 500, "rentPeriod": 12, "rentUnitPrice": 1000}, {"relateion_key": "businessOrderNo", "businessOrderNo": "LXTO-************-00154", "rentStartTime": 1754015530000, "rentEndTime": 1754015530000, "rentMode": 1, "rentAmount": 20, "rentPeriod": 12, "rentUnitPrice": 1000}]}