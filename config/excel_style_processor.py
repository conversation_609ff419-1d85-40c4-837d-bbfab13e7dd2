"""
Excel样式处理器
支持基于配置的样式应用和条件格式化
"""

from typing import Dict, List, Any, Optional
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.formatting.rule import ColorScaleRule
from openpyxl.utils import get_column_letter


class ExcelStyleProcessor:
    """Excel样式处理器"""
    
    def __init__(self, excel_operations_config: Dict[str, Any] = None):
        self.config = excel_operations_config or {}
        self.style_rules = self.config.get('style_rules', [])
        self.conditional_formatting = self.config.get('conditional_formatting', [])
        self.cell_operations = self.config.get('cell_operations', [])
    
    def apply_styles_to_worksheet(self, worksheet, worksheet_name: str, data_list: List[Dict[str, Any]], 
                                data_start_row: int = 2):
        """应用样式到工作表"""
        if not self.config:
            return
        
        print(f"🎨 应用Excel样式到工作表: {worksheet_name}")
        
        # 应用样式规则
        self._apply_style_rules(worksheet, worksheet_name, data_list, data_start_row)
        
        # 应用条件格式化
        self._apply_conditional_formatting(worksheet, worksheet_name, data_list, data_start_row)
        
        # 应用单元格操作
        self._apply_cell_operations(worksheet, worksheet_name, data_list, data_start_row)
    
    def _apply_style_rules(self, worksheet, worksheet_name: str, data_list: List[Dict[str, Any]], 
                          data_start_row: int):
        """应用样式规则"""
        for rule in self.style_rules:
            try:
                self._apply_single_style_rule(worksheet, rule, data_list, data_start_row)
            except Exception as e:
                print(f"⚠️ 样式规则 {rule.get('name', 'Unknown')} 应用失败: {e}")
    
    def _apply_single_style_rule(self, worksheet, rule: Dict[str, Any], 
                                data_list: List[Dict[str, Any]], data_start_row: int):
        """应用单个样式规则"""
        rule_name = rule.get('name', 'Unknown')
        condition = rule.get('condition', {})
        style_config = rule.get('style', {})
        apply_to = rule.get('apply_to', 'entire_row')
        target_columns = rule.get('target_columns', [])
        
        print(f"  🎯 应用样式规则: {rule_name}")
        
        # 创建样式对象
        style = self._create_style_from_config(style_config)
        
        # 查找匹配的行
        matching_rows = []
        for i, data_item in enumerate(data_list):
            if self._evaluate_condition(data_item, condition):
                row_num = data_start_row + i
                matching_rows.append(row_num)
                # 调试：显示匹配的数据
                if len(matching_rows) <= 3:  # 只显示前3个匹配项
                    field = condition.get('field')
                    value = data_item.get(field)
                    print(f"      匹配项{len(matching_rows)}: {field}={value}")

        print(f"    📊 找到 {len(matching_rows)} 行匹配条件")
        
        # 应用样式
        for row_num in matching_rows:
            if apply_to == 'entire_row':
                self._apply_style_to_entire_row(worksheet, row_num, style)
            elif apply_to == 'specific_cells':
                self._apply_style_to_specific_cells(worksheet, row_num, style, target_columns, data_list[0])
    
    def _create_style_from_config(self, style_config: Dict[str, Any]) -> Dict[str, Any]:
        """从配置创建样式对象"""
        style = {}
        
        # 字体样式
        font_kwargs = {}
        if 'font_color' in style_config:
            font_kwargs['color'] = style_config['font_color']
        if 'font_bold' in style_config:
            font_kwargs['bold'] = style_config['font_bold']
        if 'font_size' in style_config:
            font_kwargs['size'] = style_config['font_size']
        if 'font_name' in style_config:
            font_kwargs['name'] = style_config['font_name']
        
        if font_kwargs:
            style['font'] = Font(**font_kwargs)
        
        # 背景填充
        if 'background_color' in style_config:
            style['fill'] = PatternFill(
                start_color=style_config['background_color'],
                end_color=style_config['background_color'],
                fill_type='solid'
            )
        
        # 对齐方式
        alignment_kwargs = {}
        if 'horizontal_alignment' in style_config:
            alignment_kwargs['horizontal'] = style_config['horizontal_alignment']
        if 'vertical_alignment' in style_config:
            alignment_kwargs['vertical'] = style_config['vertical_alignment']
        
        if alignment_kwargs:
            style['alignment'] = Alignment(**alignment_kwargs)
        
        return style
    
    def _evaluate_condition(self, data_item: Dict[str, Any], condition: Dict[str, Any]) -> bool:
        """评估条件"""
        field = condition.get('field')
        operator = condition.get('operator')
        value = condition.get('value')

        if not field or not operator:
            return False

        item_value = data_item.get(field)


        
        if operator == 'equals':
            return item_value == value
        elif operator == 'not_equals':
            return item_value != value
        elif operator == 'greater_than':
            try:
                return float(item_value) > float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'less_than':
            try:
                return float(item_value) < float(value)
            except (ValueError, TypeError):
                return False
        elif operator == 'contains':
            return value in str(item_value)
        elif operator == 'in':
            return item_value in value
        
        return False
    
    def _apply_style_to_entire_row(self, worksheet, row_num: int, style: Dict[str, Any]):
        """应用样式到整行"""
        max_col = worksheet.max_column
        for col in range(1, max_col + 1):
            cell = worksheet.cell(row=row_num, column=col)
            self._apply_style_to_cell(cell, style)
    
    def _apply_style_to_specific_cells(self, worksheet, row_num: int, style: Dict[str, Any],
                                     target_columns: List[str], sample_data: Dict[str, Any]):
        """应用样式到特定列"""
        print(f"        🎨 应用样式到特定列: 行{row_num}, 目标列{target_columns}")

        # 创建字段名到Excel表头的映射
        field_to_header_mapping = {
            'billAmount': '应付金额',
            'businessOrderNo': '订单编号',
            'customerName': '客户名称',
            'productName': '商品名称',
            'rentStartTime': '起租日期',
            'rentEndTime': '本期结束日',
            'deliveryDate': '发货日期',
            'orderStatus': '订单状态',
            'unitPrice': '单价',
            'quantity': '起租数量',
            'rentingQuantity': '在租数量',
            'equipmentNo': '设备编号',
            'calculateQuantity': '计算数量',
            'productParams': '商品参数',
            'remark': '备注'
        }

        # 获取Excel表头到列号的映射
        header_row = 1  # 假设第一行是标题行
        header_to_col_mapping = {}
        for col in range(1, worksheet.max_column + 1):
            header_cell = worksheet.cell(row=header_row, column=col)
            if header_cell.value:
                header_to_col_mapping[str(header_cell.value)] = col

        print(f"        📋 Excel表头映射: {list(header_to_col_mapping.keys())}")

        # 如果没有找到有效的表头映射，使用数据字段顺序映射
        if len(header_to_col_mapping) <= 1:  # 只有标题行或没有表头
            print(f"        📋 使用数据字段顺序映射")
            self._apply_style_by_field_order(worksheet, row_num, style, target_columns, sample_data)
            return

        # 应用样式到目标列
        for field_name in target_columns:
            # 先尝试直接匹配字段名
            if field_name in header_to_col_mapping:
                col_num = header_to_col_mapping[field_name]
                cell = worksheet.cell(row=row_num, column=col_num)
                print(f"        ✅ 直接匹配应用样式: {cell.coordinate} (字段{field_name})")
                self._apply_style_to_cell(cell, style)
            # 再尝试通过映射表匹配
            elif field_name in field_to_header_mapping:
                excel_header = field_to_header_mapping[field_name]
                if excel_header in header_to_col_mapping:
                    col_num = header_to_col_mapping[excel_header]
                    cell = worksheet.cell(row=row_num, column=col_num)
                    print(f"        ✅ 映射匹配应用样式: {cell.coordinate} (字段{field_name} -> 表头{excel_header})")
                    self._apply_style_to_cell(cell, style)
                else:
                    print(f"        ⚠️ 未找到Excel表头: {field_name} -> {excel_header}")
            else:
                print(f"        ⚠️ 未找到字段映射: {field_name}")
                # 最后尝试模糊匹配
                self._try_fuzzy_match(worksheet, row_num, style, field_name, header_to_col_mapping)

    def _apply_style_by_field_order(self, worksheet, row_num: int, style: Dict[str, Any],
                                   target_columns: List[str], sample_data: Dict[str, Any]):
        """根据数据字段顺序应用样式"""
        if not sample_data:
            print(f"        ⚠️ 没有样本数据，无法确定字段顺序")
            return

        field_names = list(sample_data.keys())
        print(f"        📋 数据字段顺序: {field_names}")

        for field_name in target_columns:
            if field_name in field_names:
                col_index = field_names.index(field_name) + 1
                cell = worksheet.cell(row=row_num, column=col_index)
                print(f"        ✅ 字段顺序应用样式: {cell.coordinate} (字段{field_name} -> 第{col_index}列)")
                self._apply_style_to_cell(cell, style)
            else:
                print(f"        ⚠️ 字段不在数据中: {field_name}")

    def _try_fuzzy_match(self, worksheet, row_num: int, style: Dict[str, Any],
                        field_name: str, header_to_col_mapping: Dict[str, int]):
        """尝试模糊匹配列名"""
        fuzzy_mappings = {
            'billAmount': ['金额', '应付', '费用', '价格'],
            'businessOrderNo': ['订单', '编号', '单号'],
            'customerName': ['客户', '名称'],
            'productName': ['商品', '产品', '设备'],
            'rentStartTime': ['起租', '开始', '租期'],
            'rentEndTime': ['结束', '到期']
        }

        if field_name in fuzzy_mappings:
            keywords = fuzzy_mappings[field_name]
            for header, col_num in header_to_col_mapping.items():
                if any(keyword in header for keyword in keywords):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    print(f"        ✅ 模糊匹配应用样式: {cell.coordinate} (字段{field_name} -> 表头{header})")
                    self._apply_style_to_cell(cell, style)
                    return

        print(f"        ❌ 完全无法匹配字段: {field_name}")
    
    def _apply_style_to_cell(self, cell, style: Dict[str, Any]):
        """应用样式到单元格"""
        if 'font' in style:
            cell.font = style['font']
        if 'fill' in style:
            cell.fill = style['fill']
        if 'alignment' in style:
            cell.alignment = style['alignment']
    
    def _apply_conditional_formatting(self, worksheet, worksheet_name: str, 
                                    data_list: List[Dict[str, Any]], data_start_row: int):
        """应用条件格式化"""
        for formatting in self.conditional_formatting:
            try:
                apply_to_worksheets = formatting.get('apply_to_worksheets', [])
                if apply_to_worksheets and worksheet_name not in apply_to_worksheets:
                    continue
                
                self._apply_single_conditional_formatting(worksheet, formatting, data_list, data_start_row)
            except Exception as e:
                print(f"⚠️ 条件格式化 {formatting.get('name', 'Unknown')} 应用失败: {e}")
    
    def _apply_single_conditional_formatting(self, worksheet, formatting: Dict[str, Any], 
                                           data_list: List[Dict[str, Any]], data_start_row: int):
        """应用单个条件格式化"""
        formatting_name = formatting.get('name', 'Unknown')
        field = formatting.get('field')
        formatting_type = formatting.get('type')
        
        print(f"  🌈 应用条件格式化: {formatting_name}")
        
        if formatting_type == 'color_scale' and field:
            # 找到字段对应的列
            field_col = self._find_field_column(worksheet, field, data_list)
            if field_col:
                # 计算数据范围
                data_end_row = data_start_row + len(data_list) - 1
                range_str = f"{field_col}{data_start_row}:{field_col}{data_end_row}"
                
                # 创建颜色渐变规则
                min_color = formatting.get('min_color', 'FFFFFF')
                max_color = formatting.get('max_color', 'FF0000')
                
                rule = ColorScaleRule(
                    start_type='min', start_color=min_color,
                    end_type='max', end_color=max_color
                )
                
                worksheet.conditional_formatting.add(range_str, rule)
                print(f"    📊 应用颜色渐变到范围: {range_str}")
    
    def _find_field_column(self, worksheet, field_name: str, data_list: List[Dict[str, Any]]) -> Optional[str]:
        """查找字段对应的列"""
        # 方法1：从标题行查找
        for col in range(1, worksheet.max_column + 1):
            header_cell = worksheet.cell(row=1, column=col)
            if header_cell.value == field_name:
                return get_column_letter(col)
        
        # 方法2：从数据字段顺序推断
        if data_list:
            field_names = list(data_list[0].keys())
            if field_name in field_names:
                col_index = field_names.index(field_name) + 1
                return get_column_letter(col_index)
        
        return None
    
    def _apply_cell_operations(self, worksheet, worksheet_name: str, 
                             data_list: List[Dict[str, Any]], data_start_row: int):
        """应用单元格操作"""
        for operation in self.cell_operations:
            try:
                apply_to_worksheets = operation.get('apply_to_worksheets', [])
                if apply_to_worksheets and worksheet_name not in apply_to_worksheets:
                    continue
                
                self._apply_single_cell_operation(worksheet, operation, data_list, data_start_row)
            except Exception as e:
                print(f"⚠️ 单元格操作 {operation.get('name', 'Unknown')} 应用失败: {e}")
    
    def _apply_single_cell_operation(self, worksheet, operation: Dict[str, Any], 
                                   data_list: List[Dict[str, Any]], data_start_row: int):
        """应用单个单元格操作"""
        operation_name = operation.get('name', 'Unknown')
        fields = operation.get('fields', [])
        format_str = operation.get('format', '')
        
        print(f"  🔧 应用单元格操作: {operation_name}")
        
        if operation_name == 'format_dates' and fields and format_str:
            for field in fields:
                field_col = self._find_field_column(worksheet, field, data_list)
                if field_col:
                    # 应用日期格式
                    data_end_row = data_start_row + len(data_list) - 1
                    for row in range(data_start_row, data_end_row + 1):
                        cell = worksheet[f"{field_col}{row}"]
                        cell.number_format = format_str.replace('YYYY', 'yyyy').replace('MM', 'mm').replace('DD', 'dd')
                    
                    print(f"    📅 应用日期格式到字段: {field}")


def get_excel_operations_from_extra_data(extra_data_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """从额外数据配置中提取Excel操作配置"""
    if '_config' in extra_data_config:
        return extra_data_config['_config'].get('excel_operations')
    return None
