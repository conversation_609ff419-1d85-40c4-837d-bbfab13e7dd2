"""
统一配置化处理器
通过配置文件实现不同处理逻辑，支持UnifiedMobanProcessor和Custom001Processor的功能
"""

import json
import os
import sys
from typing import Dict, Any, List
from datetime import datetime

import openpyxl
import openpyxl.utils

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

# 导入父类处理器
from excel_data.unified_moban_processor import UnifiedMobanProcessor


class UnifiedConfigurableProcessor(UnifiedMobanProcessor):
    """统一配置化处理器 - 通过配置实现不同处理逻辑"""
    
    def __init__(self, config_path: str):
        # 加载配置
        self.config = self._load_config(config_path)
        
        # 获取模板和数据路径
        template_path = self.config.get('template_info', {}).get('template_path', 'template/default.xlsx')
        data_path = self.config.get('data_sources', {}).get('bill_data', {}).get('path', 'data/default.json')
        
        # 调用父类构造函数
        super().__init__(template_path, data_path)
        
        # 加载配置
        self.processing_mode = self.config.get('processing_mode', {})
        self.worksheet_config = self.config.get('worksheet_config', {})
        self.data_transformations = self.config.get('data_transformations', {})
        self.placeholder_mapping = self.config.get('placeholder_mapping', {})
        self.processing_options = self.config.get('processing_options', {})
        
        # 定义账单标志常量（如果需要）
        if self.processing_mode.get('enable_special_filtering', False):
            self.BILL_PERIOD_FLAG_NEW = "BILL_PERIOD_FLAG_NEW"
            self.BILL_PERIOD_FLAG_OLD = "BILL_PERIOD_FLAG_OLD"
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _is_special_worksheet(self, worksheet_name: str) -> bool:
        """判断是否是特殊处理的工作表"""
        worksheet_config = self.worksheet_config.get(worksheet_name, {})
        return worksheet_config.get('processing_type') in ['filtered', 'special']
    
    def _process_special_worksheet(self, worksheet, worksheet_name: str):
        """处理特殊工作表"""
        worksheet_config = self.worksheet_config.get(worksheet_name, {})
        processing_type = worksheet_config.get('processing_type', 'standard')
        
        if processing_type == 'filtered':
            # 使用过滤逻辑处理（类似Custom001）
            self._process_filtered_worksheet(worksheet, worksheet_name, worksheet_config)
        elif processing_type == 'special':
            # 使用特殊逻辑处理（如账单总览）
            self._process_special_worksheet_logic(worksheet, worksheet_name, worksheet_config)
        else:
            # 使用标准处理
            self._process_worksheet(worksheet)
    
    def _process_filtered_worksheet(self, worksheet, worksheet_name: str, worksheet_config: Dict):
        """处理需要过滤的工作表（类似Custom001的逻辑）"""
        filter_config = worksheet_config.get('filter_config', {})
        
        if not filter_config:
            # 没有过滤配置，使用标准处理
            self._process_worksheet(worksheet)
            return
        
        # 获取过滤参数
        bill_type = filter_config.get('bill_type')
        bill_flag = filter_config.get('bill_flag')
        
        # 过滤数据
        filtered_bills = self._filter_bills_by_type_and_flag(bill_type, bill_flag)
        
        # 使用过滤后的数据填充工作表
        self._process_worksheet_with_filtered_data(worksheet, worksheet_name, filtered_bills, "long")
    
    def _process_special_worksheet_logic(self, worksheet, worksheet_name: str, worksheet_config: Dict):
        """处理特殊逻辑的工作表"""
        if worksheet_config.get('enable_bill_summary', False):
            # 账单总览特殊处理
            self._process_bill_summary_rows(worksheet)
            # 添加标准占位符处理，确保${customerName}等占位符能被正确填充
            self._process_non_loop_placeholders(worksheet)
        else:
            # 使用标准处理
            self._process_worksheet(worksheet)
    
    def _filter_bills_by_type_and_flag(self, bill_type: str, bill_flag: str) -> List[Dict]:
        """根据账单类型和账单标志过滤数据（来自Custom001的逻辑）"""
        filtered_bills = []
        
        if not self.bill_data:
            return filtered_bills
        
        # 尝试不同的数据结构路径
        bill_period_statement_list = []
        
        # 路径1: 直接在根级别
        if 'billPeriodStatementList' in self.bill_data:
            bill_period_statement_list = self.bill_data.get('billPeriodStatementList', [])
        
        # 路径2: 在resultMap.data中
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_period_statement_list = data.get('billPeriodStatementList', [])
        
        if not bill_period_statement_list:
            return filtered_bills
        
        # 遍历所有账单周期，收集所有匹配的数据
        for statement in bill_period_statement_list:
            # 检查账单周期级别的账单类型
            if statement.get('billType') == bill_type:
                excel_bill_period_detail_list = statement.get('excelBillPeriodDetailList', [])
                
                for bill in excel_bill_period_detail_list:
                    # 检查账单标志
                    if bill.get('billPeriodFlag') == bill_flag:
                        filtered_bills.append(bill)
        
        return filtered_bills
    
    def _process_worksheet_with_filtered_data(self, worksheet, worksheet_name: str, 
                                            filtered_bills: list, bill_type_key: str = 'long'):
        """使用过滤后的数据填充指定工作表"""
        if not filtered_bills:
            print(f"⚠️  工作表 '{worksheet_name}' 没有匹配的数据")
            return
        
        print(f"📊 工作表 '{worksheet_name}' 找到 {len(filtered_bills)} 条匹配数据")
        
        # 处理工作表数据填充
        self._process_worksheet_data(worksheet, filtered_bills, bill_type_key)
    
    def _process_worksheet_data(self, worksheet, bills: list, bill_type_key: str):
        """处理工作表数据填充"""
        if not bills:
            print(f"⚠️  没有数据需要填充")
            return
        
        print(f"📊 开始填充 {len(bills)} 条数据到工作表")
        
        # 查找循环模板行
        loop_templates = self._find_loop_templates(worksheet)
        
        if loop_templates:
            print(f"  找到 {len(loop_templates)} 个循环模板")
            
            # 从后往前处理，避免行号变化影响
            for template_info in reversed(loop_templates):
                self._process_loop_template_with_custom_data(worksheet, template_info, bills, bill_type_key)
        
        # 处理非循环占位符
        self._process_non_loop_placeholders(worksheet)
    
    def _process_loop_template_with_custom_data(self, worksheet, template_info: dict, bills: list, bill_type_key: str):
        """使用自定义数据处理循环模板"""
        try:
            row_num = template_info['row_num']
            list_name = template_info['list_name']
            template_data = template_info['template_data']
            
            print(f"  处理第{row_num}行循环模板: {bill_type_key}.{list_name} ({len(bills)}条数据)")
            
            if len(bills) > 0:
                print(f"  将复制样式到新增的 {len(bills)} 行")
            
            # 获取原始行高
            original_row_height = worksheet.row_dimensions[row_num].height
            
            # 删除模板行
            worksheet.delete_rows(row_num)
            
            # 为每条数据创建新行
            for i, bill_data in enumerate(bills):
                new_row_num = row_num + i
                
                # 创建带样式的循环行
                self._create_styled_loop_row_with_custom_data(
                    worksheet, new_row_num, template_data, bill_data, bill_type_key, list_name, original_row_height
                )
            
        except Exception as e:
            print(f"  ❌ 处理循环模板时出错: {e}")
    
    def _create_styled_loop_row_with_custom_data(self, worksheet, row_num: int, template_data: list, 
                                                data_item: dict, bill_type: str, list_name: str, row_height: float):
        """使用自定义数据创建带样式的循环行"""
        try:
            # 设置行高
            if row_height:
                worksheet.row_dimensions[row_num].height = row_height
            
            # 为每个单元格填充数据和样式
            for col_num, cell_info in enumerate(template_data, 1):
                cell = worksheet.cell(row=row_num, column=col_num)
                
                # 获取原始值并替换占位符
                original_value = cell_info['value']
                new_value = self._replace_loop_placeholders(original_value, data_item, bill_type, list_name)
                
                # 设置单元格值
                cell.value = new_value
                
                # 复制样式
                if cell_info['font']:
                    cell.font = cell_info['font']
                if cell_info['border']:
                    cell.border = cell_info['border']
                if cell_info['fill']:
                    cell.fill = cell_info['fill']
                if cell_info['alignment']:
                    cell.alignment = cell_info['alignment']
                if cell_info['number_format']:
                    cell.number_format = cell_info['number_format']
                
                # 设置列宽
                if cell_info['col_width']:
                    col_letter = openpyxl.utils.get_column_letter(col_num)
                    worksheet.column_dimensions[col_letter].width = cell_info['col_width']
            
        except Exception as e:
            print(f"  ❌ 创建样式行时出错: {e}")
    
    def _replace_field_placeholder(self, match) -> str:
        """替换字段占位符（使用配置的映射和转换）"""
        field_name = match.group(1)
        
        # 使用配置的字段映射
        field_mappings = self.placeholder_mapping.get('field_mappings', {})
        mapped_field = field_mappings.get(field_name, field_name)
        
        # 获取值
        value = self._get_field_value(mapped_field)
        
        # 应用数据转换规则
        if field_name in self.data_transformations:
            value = self._apply_data_transformation(value, self.data_transformations[field_name])
        
        return str(value) if value is not None else ''
    
    def _apply_data_transformation(self, value: Any, rule: Dict) -> Any:
        """应用数据转换规则"""
        rule_type = rule.get('type')
        
        if rule_type == 'number':
            try:
                num_value = float(value) if value is not None else 0.0
                decimal_places = rule.get('decimal_places', 2)
                return f"{num_value:.{decimal_places}f}"
            except (ValueError, TypeError):
                return "0.00"
        
        elif rule_type == 'enum':
            mapping = rule.get('mapping', {})
            return mapping.get(str(value), str(value))
        
        return value
    
    def _format_order_status(self, status: Any) -> str:
        """格式化订单状态（优先使用配置映射）"""
        # 检查是否有配置的映射
        if 'orderStatus' in self.data_transformations:
            rule = self.data_transformations['orderStatus']
            if rule.get('type') == 'enum':
                mapping = rule.get('mapping', {})
                return mapping.get(str(status), str(status))
        
        # 如果没有配置，使用父类的默认逻辑
        return super()._format_order_status(status)
    
    def _format_pay_status(self, status: Any) -> str:
        """格式化支付状态（优先使用配置映射）"""
        # 检查是否有配置的映射
        if 'payStatus' in self.data_transformations:
            rule = self.data_transformations['payStatus']
            if rule.get('type') == 'enum':
                mapping = rule.get('mapping', {})
                return mapping.get(str(status), str(status))
        
        # 如果没有配置，使用父类的默认逻辑
        return super()._format_pay_status(status)
    
    def _format_bill_period_flag(self, detail: Dict[str, Any]) -> str:
        """格式化账单标志（优先使用配置映射）"""
        bill_period_flag = detail.get('billPeriodFlag')
        
        # 检查是否有配置的映射
        if 'billPeriodFlag' in self.data_transformations:
            rule = self.data_transformations['billPeriodFlag']
            if rule.get('type') == 'enum':
                mapping = rule.get('mapping', {})
                return mapping.get(str(bill_period_flag), str(bill_period_flag))
        
        # 如果没有配置，使用父类的默认逻辑
        return super()._format_bill_period_flag(detail)
    
    def process_template(self, output_path: str = None) -> bool:
        """处理模板（使用配置化的数据源）"""
        try:
            # 生成默认输出路径
            if output_path is None:
                current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_path = f"unified_config_output_{current_time}.xlsx"
            else:
                # 如果提供了输出路径，检查是否已经包含时间戳
                if not output_path.endswith('.xlsx'):
                    output_path += '.xlsx'
                
                # 检查文件名是否已经包含时间戳格式 (YYYYMMDD_HHMMSS)
                import re
                if not re.search(r'_\d{8}_\d{6}\.xlsx$', output_path):
                    base_name = output_path[:-5]  # 移除 .xlsx
                    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_path = f"{base_name}_{current_time}.xlsx"
            
            # 检查文件是否存在
            if not os.path.exists(self.moban_path):
                print(f"❌ 模板文件不存在: {self.moban_path}")
                return False
            
            if not self.bill_data:
                print(f"❌ 数据文件为空")
                return False
            
            # 加载工作簿
            workbook = openpyxl.load_workbook(self.moban_path)
            
            print(f"📋 开始处理模板: {self.moban_path}")
            
            # 使用父类的统一处理方法，会自动调用子类的特殊处理逻辑
            self._process_all_worksheets(workbook)
            
            # 保存文件
            workbook.save(output_path)
            print(f"💾 保存到: {output_path}")
            
            return True
                
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return False
    
    def show_data_statistics(self):
        """显示数据统计信息"""
        if not self.bill_data:
            print("❌ 没有数据")
            return
        
        # 尝试不同的数据结构路径
        bill_period_statement_list = []
        
        # 路径1: 直接在根级别
        if 'billPeriodStatementList' in self.bill_data:
            bill_period_statement_list = self.bill_data.get('billPeriodStatementList', [])
        
        # 路径2: 在resultMap.data中
        elif 'resultMap' in self.bill_data:
            result_map = self.bill_data.get('resultMap', {})
            data = result_map.get('data', {})
            bill_period_statement_list = data.get('billPeriodStatementList', [])
        
        if not bill_period_statement_list:
            print("❌ 没有账单周期数据")
            return
        
        # 遍历所有账单周期，统计总数据量
        total_bills = 0
        for statement in bill_period_statement_list:
            excel_bill_period_detail_list = statement.get('excelBillPeriodDetailList', [])
            total_bills += len(excel_bill_period_detail_list)
        
        # 如果启用了特殊过滤，显示过滤统计
        if self.processing_mode.get('enable_special_filtering', False):
            new_bills = self._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", self.BILL_PERIOD_FLAG_NEW)
            old_bills = self._filter_bills_by_type_and_flag("BUSINESS_BILL_TYPE_LONG", self.BILL_PERIOD_FLAG_OLD)
            
            print(f"📊 数据统计:")
            print(f"   总账单数: {total_bills}")
            print(f"   长租账单-新增: {len(new_bills)} 条")
            print(f"   长租账单-往期: {len(old_bills)} 条")
        else:
            print(f"📊 数据统计:")
            print(f"   总账单数: {total_bills}")


def main():
    """主函数 - 演示统一配置化处理器"""
    
    print("🚀 统一配置化处理器")
    print("=" * 60)
    
    # 获取项目根目录路径
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(current_dir)  # 回到项目根目录
    
    # 构建正确的配置文件路径
    simple_config_path = os.path.join(project_root, 'config', 'unified_simple_config.json')
    advanced_config_path = os.path.join(project_root, 'config', 'custom_001_advanced_config.json')
    output_dir = os.path.join(project_root, 'output')
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成带时间戳的文件名
    current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
    simple_output_file = f'simple_output_{current_time}.xlsx'
    advanced_output_file = f'advanced_output_{current_time}.xlsx'
    
    # 测试简单配置（类似UnifiedMobanProcessor）
    print("\n📋 测试简单配置...")
    try:
        simple_processor = UnifiedConfigurableProcessor(simple_config_path)
        simple_processor.show_data_statistics()
        success = simple_processor.process_template(os.path.join(output_dir, simple_output_file))
        if success:
            print(f"✅ 简单配置处理成功! 文件: {simple_output_file}")
        else:
            print("❌ 简单配置处理失败!")
    except Exception as e:
        print(f"❌ 简单配置测试失败: {e}")
    
    # 测试高级配置（类似Custom001Processor）
    print("\n📋 测试高级配置...")
    try:
        advanced_processor = UnifiedConfigurableProcessor(advanced_config_path)
        advanced_processor.show_data_statistics()
        success = advanced_processor.process_template(os.path.join(output_dir, advanced_output_file))
        if success:
            print(f"✅ 高级配置处理成功! 文件: {advanced_output_file}")
        else:
            print("❌ 高级配置处理失败!")
    except Exception as e:
        print(f"❌ 高级配置测试失败: {e}")


if __name__ == "__main__":
    main() 