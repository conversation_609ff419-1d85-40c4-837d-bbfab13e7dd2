# 数据映射问题说明

## 问题描述

你发现了一个重要问题：在 `config/custom_001_advanced_config.json` 中配置了 `orderStatus` 的枚举映射，但实际处理时却使用了硬编码的映射。这是因为存在两套不同的映射系统：

### 1. 硬编码映射系统
**位置**: `excel_data/field_mapping.py`
```python
ORDER_STATUS_MAPPING = {
    0: "待提交",
    4: "审核中", 
    5: "待备货",
    6: "备货中",
    8: "待发货",
    12: "处理中",
    16: "已发货",
    18: "已签收",
    20: "租赁中",
    22: "部分退还",
    24: "全部归还",
    28: "取消",
    32: "结束"
}
```

### 2. 配置化映射系统
**位置**: `config/custom_001_advanced_config.json`
```json
"orderStatus": {
  "type": "enum",
  "mapping": {
    "ORDER_STATUS_ACTIVE": "有效",
    "ORDER_STATUS_INACTIVE": "无效"
  }
}
```

## 问题根源

### 数据类型不匹配
- **实际数据**: `orderStatus` 字段是数值类型（0, 4, 5, 6, 8, 12, 16, 18, 20, 22, 24, 28, 32）
- **配置文件**: 使用了字符串键（"ORDER_STATUS_ACTIVE", "ORDER_STATUS_INACTIVE"）
- **硬编码映射**: 使用数值键（0, 4, 5, 6, 8, 12, 16, 18, 20, 22, 24, 28, 32）

### 处理逻辑冲突
1. `UnifiedMobanProcessor` 中的 `_format_order_status` 方法使用硬编码的 `ORDER_STATUS_MAPPING`
2. `UnifiedConfigurableProcessor` 中的 `_apply_data_transformation` 方法使用配置文件中的映射
3. 当字段名匹配时，子类的方法会覆盖父类的方法

## 解决方案

### 1. 修正配置文件映射
将配置文件中的映射改为与实际数据匹配的数值键：

```json
"orderStatus": {
  "type": "enum",
  "mapping": {
    "0": "待提交",
    "4": "审核中",
    "5": "待备货",
    "6": "备货中",
    "8": "待发货",
    "12": "处理中",
    "16": "已发货",
    "18": "已签收",
    "20": "租赁中",
    "22": "部分退还",
    "24": "全部归还",
    "28": "取消",
    "32": "结束"
  }
}
```

### 2. 修正支付状态映射
```json
"payStatus": {
  "type": "enum",
  "mapping": {
    "0": "未支付",
    "4": "部分支付",
    "8": "已支付",
    "16": "无需支付",
    "20": "无需支付"
  }
}
```

### 3. 修正账单标志映射
```json
"billPeriodFlag": {
  "type": "enum",
  "mapping": {
    "BILL_PERIOD_FLAG_NEW": "新增",
    "BILL_PERIOD_FLAG_OLD": "往期"
  }
}
```

## 验证修复

### 测试配置化映射是否生效
1. 运行统一配置化处理器
2. 检查生成的Excel文件中 `orderStatus` 和 `payStatus` 字段是否正确显示中文描述
3. 对比使用硬编码映射和配置化映射的结果

### 预期结果
- ✅ 配置化映射应该覆盖硬编码映射
- ✅ 数值字段应该正确转换为中文描述
- ✅ 配置文件中的映射应该生效

## 最佳实践

### 1. 数据一致性
- 确保配置文件中的映射与实际数据格式一致
- 数值字段使用数值键，字符串字段使用字符串键

### 2. 映射优先级
- 配置化映射 > 硬编码映射
- 子类方法 > 父类方法

### 3. 测试验证
- 为每个映射字段创建测试用例
- 验证不同数据类型的映射是否正确

### 4. 文档维护
- 保持配置文件映射与实际数据格式的同步
- 记录字段的数据类型和取值范围

## 总结

这个问题的核心是**数据类型不匹配**和**映射系统冲突**。通过修正配置文件中的映射键，使其与实际数据格式一致，可以确保配置化映射系统正常工作，实现真正的配置驱动数据处理。

现在配置文件中的映射应该能够正确覆盖硬编码的映射，实现你期望的配置化功能！ 