"""
工作表过滤处理器
支持配置化的数据过滤
"""

from typing import List, Dict, Any
from .data_filter import FilterFactory
from .custom_filter_methods import get_custom_filter_methods


class WorksheetFilterProcessor:
    """工作表过滤处理器"""
    
    def __init__(self):
        # 初始化过滤器工厂
        custom_methods = get_custom_filter_methods()
        self.filter_factory = FilterFactory(custom_methods)
    
    def process_filtered_worksheet(self, worksheet_name: str, worksheet_config: Dict[str, Any], 
                                 bill_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        处理过滤类型的工作表
        
        Args:
            worksheet_name: 工作表名称
            worksheet_config: 工作表配置
            bill_data: 账单数据
            
        Returns:
            过滤后的数据列表
        """
        filter_config = worksheet_config.get('filter_config', {})
        
        if not filter_config:
            print(f"⚠️ 工作表 {worksheet_name} 缺少过滤配置")
            return []
        
        # 获取数据路径
        data_path = filter_config.get('data_path', 'excelBillPeriodDetailList')
        
        # 获取账单汇总列表
        bill_statements = self._get_bill_statements(bill_data)
        if not bill_statements:
            print(f"⚠️ 未找到账单汇总数据")
            return []
        
        # 应用过滤逻辑
        filtered_data = []
        
        for statement in bill_statements:
            # 获取要过滤的数据列表
            data_list = statement.get(data_path, [])
            if not data_list:
                continue
            
            # 准备上下文数据
            context_data = {
                'parent_data': statement,  # 父级数据（账单汇总）
                'bill_data': bill_data,    # 完整账单数据
                'worksheet_name': worksheet_name
            }
            
            # 应用过滤器
            statement_filtered_data = self._apply_filters(filter_config, data_list, context_data)
            filtered_data.extend(statement_filtered_data)
        
        print(f"📊 工作表 {worksheet_name} 过滤结果: {len(filtered_data)} 条记录")
        return filtered_data
    
    def _get_bill_statements(self, bill_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取账单汇总列表"""
        # 尝试多个可能的路径
        paths = [
            'resultMap.data.billPeriodStatementList',
            'data.billPeriodStatementList', 
            'billPeriodStatementList'
        ]
        
        for path in paths:
            statements = self._get_nested_value(bill_data, path)
            if statements and isinstance(statements, list):
                return statements
        
        return []
    
    def _get_nested_value(self, data: Dict, path: str) -> Any:
        """获取嵌套字段值"""
        if not path:
            return None
        
        keys = path.split('.')
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return None
        
        return current
    
    def _apply_filters(self, filter_config: Dict[str, Any], data_list: List[Dict], 
                      context_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用过滤器"""
        
        # 检查是否是新的配置格式
        if 'filter_type' in filter_config:
            # 新格式：使用配置化过滤器
            filter_obj = self.filter_factory.create_filter(filter_config)
            return filter_obj.filter(data_list, context_data)
        
        else:
            # 兼容旧格式：转换为新格式
            return self._apply_legacy_filters(filter_config, data_list, context_data)
    
    def _apply_legacy_filters(self, filter_config: Dict[str, Any], data_list: List[Dict], 
                            context_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用旧格式的过滤器（兼容性）"""
        bill_type = filter_config.get('bill_type')
        bill_flag = filter_config.get('bill_flag')
        
        # 使用自定义方法进行过滤
        custom_filter_config = {
            'filter_type': 'custom',
            'method_name': 'filter_bill_type_and_flag',
            'parameters': {
                'bill_type': bill_type,
                'bill_flag': bill_flag
            }
        }
        
        filter_obj = self.filter_factory.create_filter(custom_filter_config)
        return filter_obj.filter(data_list, context_data)


# 使用示例和测试
def create_sample_filter_configs():
    """创建示例过滤配置"""
    
    # 新格式配置示例
    new_format_configs = {
        "长租账单-新增-新格式": {
            "processing_type": "filtered",
            "filter_config": {
                "data_path": "excelBillPeriodDetailList",
                "filter_type": "custom",
                "method_name": "filter_bill_type_and_flag",
                "parameters": {
                    "bill_type": "BUSINESS_BILL_TYPE_LONG",
                    "bill_flag": "BILL_PERIOD_FLAG_NEW"
                }
            },
            "header_row": 1,
            "data_start_row": 2
        },
        
        "高级过滤示例": {
            "processing_type": "filtered",
            "filter_config": {
                "data_path": "excelBillPeriodDetailList",
                "filter_type": "advanced",
                "conditions": [
                    {
                        "field_path": "billType",
                        "operator": "equals",
                        "value": "BUSINESS_BILL_TYPE_LONG",
                        "data_source": "parent"
                    },
                    {
                        "field_path": "billPeriodFlag",
                        "operator": "equals",
                        "value": "BILL_PERIOD_FLAG_NEW",
                        "data_source": "current"
                    },
                    {
                        "field_path": "billAmount",
                        "operator": "greater_than",
                        "value": 1000,
                        "data_source": "current"
                    }
                ],
                "logic": "AND"
            },
            "header_row": 1,
            "data_start_row": 2
        },
        
        "组合过滤示例": {
            "processing_type": "filtered",
            "filter_config": {
                "data_path": "excelBillPeriodDetailList",
                "filter_type": "combined",
                "filters": [
                    {
                        "filter_type": "custom",
                        "method_name": "filter_bill_type_and_flag",
                        "parameters": {
                            "bill_type": "BUSINESS_BILL_TYPE_LONG",
                            "bill_flag": "BILL_PERIOD_FLAG_NEW"
                        }
                    },
                    {
                        "filter_type": "advanced",
                        "conditions": [
                            {
                                "field_path": "billAmount",
                                "operator": "greater_than",
                                "value": 5000,
                                "data_source": "current"
                            }
                        ],
                        "logic": "AND"
                    }
                ],
                "combine_logic": "AND"
            },
            "header_row": 1,
            "data_start_row": 2
        }
    }
    
    return new_format_configs
