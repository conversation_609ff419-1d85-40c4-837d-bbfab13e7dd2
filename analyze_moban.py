"""
分析moban.xlsx文件结构和占位符
"""

import openpyxl
import re

def analyze_moban():
    """分析moban.xlsx文件"""
    try:
        wb = openpyxl.load_workbook('moban_enhanced.xlsx')
        print('📋 工作表列表:')
        
        for sheet_name in wb.sheetnames:
            print(f'  - {sheet_name}')
            ws = wb[sheet_name]
            print(f'    最大行数: {ws.max_row}')
            print(f'    最大列数: {ws.max_column}')
            print('    占位符分析:')
            
            placeholder_count = 0
            placeholders = []
            
            for row in ws.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        # 查找占位符
                        if '${' in cell.value or '#{' in cell.value:
                            placeholder_count += 1
                            placeholders.append({
                                'coordinate': cell.coordinate,
                                'value': cell.value
                            })
            
            # 显示占位符
            for ph in placeholders:
                print(f'      {ph["coordinate"]}: {ph["value"]}')
            
            print(f'    占位符总数: {placeholder_count}')
            print()
            
    except Exception as e:
        print(f'错误: {e}')

if __name__ == "__main__":
    analyze_moban()
