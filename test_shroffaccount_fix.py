#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试shroffAccount占位符处理
"""

import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor

def test_shroffaccount_placeholders():
    """测试shroffAccount占位符处理"""
    print("🧪 测试shroffAccount占位符处理")
    
    # 创建处理器
    processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
    
    # 测试占位符
    test_placeholders = [
        '${shroffAccount.accountName}',
        '${shroffAccount.accountBank}', 
        '${shroffAccount.accountNo}'
    ]
    
    print("\n📋 测试占位符:")
    for placeholder in test_placeholders:
        # 使用标准占位符替换逻辑
        result = processor._replace_standard_placeholders(placeholder)
        print(f"  {placeholder} -> {result}")
    
    # 检查数据加载
    print(f"\n📊 数据统计:")
    print(f"  bill_data keys: {list(processor.bill_data.keys())}")
    
    if 'shroffAccount' in processor.bill_data:
        shroff_data = processor.bill_data['shroffAccount']
        print(f"  shroffAccount data: {shroff_data}")
    else:
        print("  ❌ shroffAccount 不在根级别")
        
        # 检查其他路径
        if 'resultMap' in processor.bill_data:
            result_map = processor.bill_data['resultMap']
            if 'data' in result_map:
                data = result_map['data']
                if 'shroffAccount' in data:
                    print(f"  ✅ 在 resultMap.data.shroffAccount 中找到数据")
                    print(f"  shroffAccount data: {data['shroffAccount']}")
                else:
                    print("  ❌ 在 resultMap.data 中未找到 shroffAccount")
            else:
                print("  ❌ resultMap 中没有 data 字段")
        else:
            print("  ❌ 没有 resultMap 字段")

if __name__ == "__main__":
    test_shroffaccount_placeholders()
