#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试customerName的取值逻辑
"""

import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor

def test_customer_name_logic():
    """测试customerName的取值逻辑"""
    print("🧪 测试customerName的取值逻辑")
    print("=" * 50)
    
    # 创建处理器
    processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
    
    print(f"📊 数据路径检查:")
    print(f"  根级别 customerName: {processor.bill_data.get('customerName')}")
    print(f"  resultMap.data.customerName: {processor.bill_data.get('resultMap', {}).get('data', {}).get('customerName')}")
    print(f"  data.customerName: {processor.bill_data.get('data', {}).get('customerName')}")
    
    print(f"\n📋 调用链分析:")
    print(f"  1. 占位符: ${{customerName}}")
    print(f"  2. 字段名: customerName")
    print(f"  3. _get_field_value('customerName')")
    print(f"  4. 调用: self._get_customer_info('customerName')")
    
    print(f"\n📋 _get_customer_info方法逻辑:")
    print(f"  paths = [")
    print(f"    self.bill_data.get('customerName'),  # 根级别")
    print(f"    self.bill_data.get('resultMap', {{}}).get('data', {{}}).get('customerName'),  # resultMap.data中")
    print(f"    self.bill_data.get('data', {{}}).get('customerName'),  # data中")
    print(f"  ]")
    print(f"  for value in paths:")
    print(f"    if value is not None:")
    print(f"      return value")
    print(f"  return 'N/A'")
    
    # 实际测试
    print(f"\n📋 实际测试结果:")
    customer_name = processor._get_customer_info('customerName')
    print(f"  _get_customer_info('customerName') = {customer_name}")
    
    # 测试占位符替换
    print(f"\n📋 占位符替换测试:")
    placeholder = '${customerName}'
    result = processor._replace_standard_placeholders(placeholder)
    print(f"  {placeholder} -> {result}")
    
    # 详细路径检查
    print(f"\n📋 详细路径检查:")
    paths = [
        ("根级别", processor.bill_data.get('customerName')),
        ("resultMap.data", processor.bill_data.get('resultMap', {}).get('data', {}).get('customerName')),
        ("data", processor.bill_data.get('data', {}).get('customerName'))
    ]
    
    for path_name, value in paths:
        status = "✅ 找到" if value is not None else "❌ 未找到"
        print(f"  {path_name}: {value} {status}")

if __name__ == "__main__":
    test_customer_name_logic() 