#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Excel文件中的占位符替换情况
"""

import os
import sys
from openpyxl import load_workbook

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)


def check_excel_placeholders():
    """检查Excel文件中的占位符替换情况"""
    print("🔍 检查Excel文件中的占位符替换情况...")
    
    # 查找最新生成的Excel文件
    output_files = [f for f in os.listdir('.') if f.startswith('multi_source_output_') and f.endswith('.xlsx')]
    if not output_files:
        print("❌ 未找到输出文件")
        return
    
    latest_file = max(output_files, key=lambda x: os.path.getctime(x))
    print(f"📁 检查文件: {latest_file}")
    
    try:
        # 加载Excel文件
        workbook = load_workbook(latest_file)
        
        # 检查所有工作表
        for sheet_name in workbook.sheetnames:
            print(f"\n📋 检查工作表: {sheet_name}")
            worksheet = workbook[sheet_name]
            
            # 扫描所有单元格，查找占位符
            placeholder_count = 0
            replaced_count = 0
            
            for row in worksheet.iter_rows():
                for cell in row:
                    if cell.value and isinstance(cell.value, str):
                        cell_value = str(cell.value)
                        
                        # 检查是否包含占位符
                        if '${' in cell_value or '#{' in cell_value:
                            placeholder_count += 1
                            print(f"  ❌ {cell.coordinate}: 占位符未替换 - '{cell_value}'")
                        elif any(keyword in cell_value.lower() for keyword in ['深222圳', '凌雄', '2025', '账单', '总览']):
                            replaced_count += 1
                            print(f"  ✅ {cell.coordinate}: 已替换 - '{cell_value[:50]}{'...' if len(cell_value) > 50 else ''}'")
            
            print(f"  📊 统计: {placeholder_count} 个未替换占位符, {replaced_count} 个已替换内容")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ 读取Excel文件失败: {e}")


def check_template_placeholders():
    """检查模板文件中的占位符"""
    print("\n🔍 检查模板文件中的占位符...")
    
    template_file = "moban_enhanced.xlsx"
    
    try:
        # 加载模板文件
        workbook = load_workbook(template_file)
        
        # 检查账单总览工作表
        if '账单总览' in workbook.sheetnames:
            worksheet = workbook['账单总览']
            print(f"\n📋 检查模板工作表: 账单总览")
            
            # 检查前10行的内容
            for row_num in range(1, 11):
                for col_num in range(1, 11):
                    cell = worksheet.cell(row=row_num, column=col_num)
                    if cell.value and isinstance(cell.value, str):
                        cell_value = str(cell.value)
                        
                        # 检查是否包含占位符
                        if '${' in cell_value or '#{' in cell_value:
                            print(f"  📍 {cell.coordinate}: '{cell_value}'")
        
        workbook.close()
        
    except Exception as e:
        print(f"❌ 读取模板文件失败: {e}")


if __name__ == "__main__":
    check_template_placeholders()
    check_excel_placeholders()
