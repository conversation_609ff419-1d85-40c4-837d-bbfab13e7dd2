# OMS API 账单数据获取模块

## 概述

本模块提供了完整的OMS接口数据获取功能，用于获取客户的账单数据。根据OMS接口数据获取规则文档实现，支持完整的账单数据解析和处理。

## 文件结构

```
excel_data/
├── oms_api_client.py      # OMS API客户端主文件
├── config.py              # 配置文件
├── example_usage.py       # 使用示例
└── README.md              # 说明文档
```

## 功能特性

### 1. 完整的API客户端
- 支持OMS账单数据接口调用
- 完整的参数验证
- 错误处理和重试机制
- 详细的日志记录

### 2. 数据结构定义
- 完整的账单数据结构
- 支持所有OMS接口返回字段
- 类型安全的数据处理
- 枚举值映射

### 3. 数据处理功能
- 账单数据解析和转换
- 统计信息计算
- JSON格式导出
- 数据验证和清洗

### 4. 配置管理
- 多环境配置支持
- 环境变量配置
- 灵活的配置管理

## 安装依赖

```bash
pip install requests
```

## 快速开始

### 1. 基本使用

```python
from oms_api_client import OMSApiClient
from config import current_config

# 创建客户端
oms_client = OMSApiClient(
    base_url="http://oms-api.example.com",
    api_token="your_token_here"
)

# 获取账单数据
bill_data = oms_client.get_bill_data(
    customer_no="CUST001",
    current_bill_month="2024-12"
)

# 打印客户信息
print(f"客户名称: {bill_data.customerName}")
print(f"账单金额: {bill_data.billPeriodStatementList[0].billAmount}")
```

### 2. 使用数据处理器

```python
from example_usage import BillDataProcessor

# 创建处理器
processor = BillDataProcessor(oms_client)

# 获取并处理账单数据
bill_data = processor.get_customer_bill_data("CUST001", "2024-12")

# 打印摘要信息
processor.print_bill_summary(bill_data)

# 导出JSON文件
processor.export_to_json(bill_data, "bill_data.json")
```

## 配置说明

### 环境变量配置

```bash
# OMS API配置
export OMS_API_BASE_URL="http://oms-api.example.com"
export OMS_API_TOKEN="your_token_here"
export OMS_API_TIMEOUT="30"
export OMS_API_MAX_RETRIES="3"

# 环境配置
export ENVIRONMENT="development"  # development, testing, production

# 日志配置
export LOG_LEVEL="INFO"
export LOG_FILE="./logs/oms_api.log"
```

### 配置文件

```python
from config import current_config

# 获取OMS配置
oms_config = current_config.get_oms_config()

# 获取日志配置
logging_config = current_config.get_logging_config()

# 获取验证配置
validation_config = current_config.get_validation_config()
```

## 数据结构

### 主要数据类

1. **BillPeriodStatementStatistics** - 账单期结算统计
   - 客户基础信息
   - 账户信息
   - 收款账户信息
   - 账单明细列表

2. **BillPeriodStatement** - 账单期结算
   - 账单统计信息
   - 设备统计信息
   - 明细列表

3. **BillPeriodStatementDetail** - 账单期结算明细
   - 业务信息
   - 商品信息
   - 时间信息
   - 支付信息
   - 收货信息

4. **AccountOrderMonthEquipmentDetail** - 设备明细
   - 设备信息
   - 租赁信息
   - 序列号信息

### 枚举类型

- **PayStatus** - 支付状态
- **BusinessOrderType** - 业务单类型
- **RentMode** - 租赁方式
- **BusinessBillType** - 账单类型

## API接口

### 获取账单数据

```python
def get_bill_data(
    self,
    customer_no: str,
    current_bill_month: str,
    export_bill_account_template_id: int = 1,
    shroff_account_id: Optional[int] = None
) -> BillPeriodStatementStatistics
```

**参数说明：**
- `customer_no`: 客户编号 (必填)
- `current_bill_month`: 账单月份，格式YYYY-MM (必填)
- `export_bill_account_template_id`: 模板ID (可选，默认1)
- `shroff_account_id`: 收款账户ID (可选)

**返回值：**
- `BillPeriodStatementStatistics`: 完整的账单统计数据

## 错误处理

### 常见错误码

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |
| 1001 | 客户编号不存在 |
| 1002 | 账单月份格式错误 |
| 1003 | 模板ID不存在 |
| 1004 | 收款账户不存在 |
| 1005 | 无账单数据 |
| 1006 | 数据权限不足 |

### 异常处理示例

```python
try:
    bill_data = oms_client.get_bill_data("CUST001", "2024-12")
except Exception as e:
    print(f"获取账单数据失败: {str(e)}")
    # 处理异常
```

## 数据验证

### 参数验证规则

1. **客户编号验证**
   - 长度：3-50字符
   - 格式：字母数字组合

2. **账单月份验证**
   - 格式：YYYY-MM
   - 范围：2020-01至当前年月+1

3. **模板ID验证**
   - 类型：正整数
   - 范围：1-999999

### 数据验证示例

```python
# 验证客户编号
if len(customer_no) < 3 or len(customer_no) > 50:
    raise Exception("客户编号长度必须在3-50字符之间")

# 验证账单月份
try:
    datetime.strptime(bill_month, "%Y-%m")
except ValueError:
    raise Exception("账单月份格式错误，应为YYYY-MM格式")
```

## 日志记录

### 日志配置

```python
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    filename='./logs/oms_api.log'
)
```

### 日志级别

- **DEBUG**: 详细调试信息
- **INFO**: 一般信息
- **WARNING**: 警告信息
- **ERROR**: 错误信息

## 性能优化

### 缓存策略

```python
# 启用缓存
os.environ['CACHE_ENABLED'] = 'true'
os.environ['CACHE_TTL'] = '300'  # 5分钟
```

### 重试机制

```python
# 配置重试
os.environ['OMS_API_MAX_RETRIES'] = '3'
os.environ['RETRY_DELAY'] = '1.0'
os.environ['RETRY_BACKOFF'] = '2.0'
```

## 示例代码

### 完整示例

```python
import os
from oms_api_client import OMSApiClient
from example_usage import BillDataProcessor

# 设置环境变量
os.environ['OMS_API_BASE_URL'] = 'http://oms-api.example.com'
os.environ['OMS_API_TOKEN'] = 'your_token_here'

# 创建客户端和处理器
oms_client = OMSApiClient(
    base_url="http://oms-api.example.com",
    api_token="your_token_here"
)
processor = BillDataProcessor(oms_client)

# 获取账单数据
bill_data = processor.get_customer_bill_data("CUST001", "2024-12")

# 打印摘要
processor.print_bill_summary(bill_data)

# 导出数据
processor.export_to_json(bill_data, "bill_data_CUST001_2024-12.json")
```

### 批量处理示例

```python
# 批量处理多个客户
customers = [
    {"customer_no": "CUST001", "bill_month": "2024-12"},
    {"customer_no": "CUST002", "bill_month": "2024-12"},
    {"customer_no": "CUST003", "bill_month": "2024-11"}
]

for customer in customers:
    try:
        bill_data = processor.get_customer_bill_data(
            customer['customer_no'], 
            customer['bill_month']
        )
        
        # 处理数据...
        processor.export_to_json(
            bill_data, 
            f"bill_data_{customer['customer_no']}_{customer['bill_month']}.json"
        )
        
    except Exception as e:
        print(f"处理客户 {customer['customer_no']} 失败: {str(e)}")
```

## 注意事项

1. **API令牌安全**
   - 不要在代码中硬编码API令牌
   - 使用环境变量或配置文件管理令牌
   - 定期更新API令牌

2. **数据精度**
   - 金额字段使用Decimal类型确保精度
   - 避免浮点数计算误差

3. **错误处理**
   - 始终使用try-catch处理API调用
   - 记录详细的错误信息
   - 实现适当的重试机制

4. **性能考虑**
   - 合理设置超时时间
   - 使用连接池
   - 实现数据缓存

5. **数据验证**
   - 验证所有输入参数
   - 检查API响应数据完整性
   - 处理空值和异常数据

## 更新日志

### v1.0.0
- 初始版本发布
- 完整的OMS API客户端实现
- 支持账单数据获取和解析
- 提供完整的数据结构和枚举定义

## 技术支持

如有问题或建议，请联系开发团队。 