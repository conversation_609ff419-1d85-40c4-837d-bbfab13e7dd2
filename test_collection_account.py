#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试collectionAccount字段获取
"""

import json
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from config.unified_configurable_processor import UnifiedConfigurableProcessor

def test_collection_account():
    """测试collectionAccount字段获取"""
    print("🧪 测试collectionAccount字段获取")
    print("=" * 40)
    
    # 创建处理器
    processor = UnifiedConfigurableProcessor('config/unified_simple_config.json')
    
    # 检查数据路径
    print(f"📊 数据路径检查:")
    print(f"  根级别 collectionAccount: {processor.bill_data.get('collectionAccount')}")
    print(f"  resultMap.data.collectionAccount: {processor.bill_data.get('resultMap', {}).get('data', {}).get('collectionAccount')}")
    print(f"  data.collectionAccount: {processor.bill_data.get('data', {}).get('collectionAccount')}")
    
    # 测试_get_account_info方法
    print(f"\n📋 _get_account_info方法测试:")
    test_fields = ['accountName', 'accountBank', 'accountNo']
    for field in test_fields:
        value = processor._get_account_info(field)
        print(f"  {field}: {value}")
    
    # 测试直接字段获取
    print(f"\n📋 直接字段获取测试:")
    if 'collectionAccount' in processor.bill_data:
        collection_data = processor.bill_data['collectionAccount']
        print(f"  collectionAccount.accountName: {collection_data.get('accountName')}")
        print(f"  collectionAccount.accountBank: {collection_data.get('accountBank')}")
        print(f"  collectionAccount.accountNo: {collection_data.get('accountNo')}")
    
    # 测试占位符替换
    print(f"\n📋 占位符替换测试:")
    test_placeholders = [
        '${collectionAccount.accountName}',
        '${collectionAccount.accountBank}',
        '${collectionAccount.accountNo}'
    ]
    
    for placeholder in test_placeholders:
        result = processor._replace_standard_placeholders(placeholder)
        print(f"  {placeholder} -> {result}")

if __name__ == "__main__":
    test_collection_account() 